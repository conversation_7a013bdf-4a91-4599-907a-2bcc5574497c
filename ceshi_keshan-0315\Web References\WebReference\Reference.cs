﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

// 
// 此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
// 
#pragma warning disable 1591

namespace ceshi_keshan_0315.WebReference {
    using System;
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    using System.Data;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="hx_xiaoshou_jiekouSoap", Namespace="http://tempuri.org/")]
    public partial class hx_xiaoshou_jiekou : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback HelloWorldOperationCompleted;
        
        private System.Threading.SendOrPostCallback fenbo_dan_hkOperationCompleted;
        
        private System.Threading.SendOrPostCallback fenbo_mingxiOperationCompleted;
        
        private System.Threading.SendOrPostCallback shangpin_shuxingOperationCompleted;
        
        private System.Threading.SendOrPostCallback kucun_xsOperationCompleted;
        
        private System.Threading.SendOrPostCallback haixin_mendian_kucunOperationCompleted;
        
        private System.Threading.SendOrPostCallback xiaoshou_pmOperationCompleted;
        
        private System.Threading.SendOrPostCallback zhinengbuhuo_jinhuoliangOperationCompleted;
        
        private System.Threading.SendOrPostCallback quan_xiaoshou_mingxiOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_xiaoshou_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_zhifufangshi_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_zhifufangshi_huizong_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_xiaoshoumingxi_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_xiaoshoudan_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_xiaoshouhuizong_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback APP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_yanshoudan_chaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_xiche_zhifufangshi_huizongchaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_xiangyan_kucunchaxunOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_xiaoshou_mingxi_tSalPluDetailOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_bianlidian_bishuOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_diaobodanOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_yanshoudanOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_tuangoudan_TJOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_cuxiaodan_yue_TJOperationCompleted;
        
        private System.Threading.SendOrPostCallback HX_cuxiaodan_TJOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public hx_xiaoshou_jiekou() {
            this.Url = global::ceshi_keshan_0315.Properties.Settings.Default.ceshi_keshan_0315_WebReference_hx_xiaoshou_jiekou;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event HelloWorldCompletedEventHandler HelloWorldCompleted;
        
        /// <remarks/>
        public event fenbo_dan_hkCompletedEventHandler fenbo_dan_hkCompleted;
        
        /// <remarks/>
        public event fenbo_mingxiCompletedEventHandler fenbo_mingxiCompleted;
        
        /// <remarks/>
        public event shangpin_shuxingCompletedEventHandler shangpin_shuxingCompleted;
        
        /// <remarks/>
        public event kucun_xsCompletedEventHandler kucun_xsCompleted;
        
        /// <remarks/>
        public event haixin_mendian_kucunCompletedEventHandler haixin_mendian_kucunCompleted;
        
        /// <remarks/>
        public event xiaoshou_pmCompletedEventHandler xiaoshou_pmCompleted;
        
        /// <remarks/>
        public event zhinengbuhuo_jinhuoliangCompletedEventHandler zhinengbuhuo_jinhuoliangCompleted;
        
        /// <remarks/>
        public event quan_xiaoshou_mingxiCompletedEventHandler quan_xiaoshou_mingxiCompleted;
        
        /// <remarks/>
        public event APP_ziti_xiaoshou_chaxunCompletedEventHandler APP_ziti_xiaoshou_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_zhifufangshi_chaxunCompletedEventHandler APP_ziti_zhifufangshi_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_zhifufangshi_huizong_chaxunCompletedEventHandler APP_ziti_zhifufangshi_huizong_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventHandler APP_ziti_zhifufangshi_mingxi_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_xiaoshoumingxi_chaxunCompletedEventHandler APP_ziti_xiaoshoumingxi_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_xiaoshoudan_chaxunCompletedEventHandler APP_ziti_xiaoshoudan_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_xiaoshouhuizong_chaxunCompletedEventHandler APP_ziti_xiaoshouhuizong_chaxunCompleted;
        
        /// <remarks/>
        public event APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventHandler APP_ziti_xiaoshouhuizong_yuechaxunCompleted;
        
        /// <remarks/>
        public event HX_yanshoudan_chaxunCompletedEventHandler HX_yanshoudan_chaxunCompleted;
        
        /// <remarks/>
        public event HX_xiche_zhifufangshi_huizongchaxunCompletedEventHandler HX_xiche_zhifufangshi_huizongchaxunCompleted;
        
        /// <remarks/>
        public event HX_xiangyan_kucunchaxunCompletedEventHandler HX_xiangyan_kucunchaxunCompleted;
        
        /// <remarks/>
        public event HX_xiaoshou_mingxi_tSalPluDetailCompletedEventHandler HX_xiaoshou_mingxi_tSalPluDetailCompleted;
        
        /// <remarks/>
        public event HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventHandler HX_xiaoshou_maxserialno_tSalPluDetailCompleted;
        
        /// <remarks/>
        public event HX_bianlidian_bishuCompletedEventHandler HX_bianlidian_bishuCompleted;
        
        /// <remarks/>
        public event HX_diaobodanCompletedEventHandler HX_diaobodanCompleted;
        
        /// <remarks/>
        public event HX_yanshoudanCompletedEventHandler HX_yanshoudanCompleted;
        
        /// <remarks/>
        public event HX_tuangoudan_TJCompletedEventHandler HX_tuangoudan_TJCompleted;
        
        /// <remarks/>
        public event HX_cuxiaodan_yue_TJCompletedEventHandler HX_cuxiaodan_yue_TJCompleted;
        
        /// <remarks/>
        public event HX_cuxiaodan_TJCompletedEventHandler HX_cuxiaodan_TJCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HelloWorld", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HelloWorld(string code, System.DateTime workday1, System.DateTime workday2) {
            object[] results = this.Invoke("HelloWorld", new object[] {
                        code,
                        workday1,
                        workday2});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HelloWorldAsync(string code, System.DateTime workday1, System.DateTime workday2) {
            this.HelloWorldAsync(code, workday1, workday2, null);
        }
        
        /// <remarks/>
        public void HelloWorldAsync(string code, System.DateTime workday1, System.DateTime workday2, object userState) {
            if ((this.HelloWorldOperationCompleted == null)) {
                this.HelloWorldOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHelloWorldOperationCompleted);
            }
            this.InvokeAsync("HelloWorld", new object[] {
                        code,
                        workday1,
                        workday2}, this.HelloWorldOperationCompleted, userState);
        }
        
        private void OnHelloWorldOperationCompleted(object arg) {
            if ((this.HelloWorldCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HelloWorldCompleted(this, new HelloWorldCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/fenbo_dan_hk", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable fenbo_dan_hk(System.DateTime workday3, System.DateTime workday4) {
            object[] results = this.Invoke("fenbo_dan_hk", new object[] {
                        workday3,
                        workday4});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void fenbo_dan_hkAsync(System.DateTime workday3, System.DateTime workday4) {
            this.fenbo_dan_hkAsync(workday3, workday4, null);
        }
        
        /// <remarks/>
        public void fenbo_dan_hkAsync(System.DateTime workday3, System.DateTime workday4, object userState) {
            if ((this.fenbo_dan_hkOperationCompleted == null)) {
                this.fenbo_dan_hkOperationCompleted = new System.Threading.SendOrPostCallback(this.Onfenbo_dan_hkOperationCompleted);
            }
            this.InvokeAsync("fenbo_dan_hk", new object[] {
                        workday3,
                        workday4}, this.fenbo_dan_hkOperationCompleted, userState);
        }
        
        private void Onfenbo_dan_hkOperationCompleted(object arg) {
            if ((this.fenbo_dan_hkCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.fenbo_dan_hkCompleted(this, new fenbo_dan_hkCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/fenbo_mingxi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable fenbo_mingxi(string code2) {
            object[] results = this.Invoke("fenbo_mingxi", new object[] {
                        code2});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void fenbo_mingxiAsync(string code2) {
            this.fenbo_mingxiAsync(code2, null);
        }
        
        /// <remarks/>
        public void fenbo_mingxiAsync(string code2, object userState) {
            if ((this.fenbo_mingxiOperationCompleted == null)) {
                this.fenbo_mingxiOperationCompleted = new System.Threading.SendOrPostCallback(this.Onfenbo_mingxiOperationCompleted);
            }
            this.InvokeAsync("fenbo_mingxi", new object[] {
                        code2}, this.fenbo_mingxiOperationCompleted, userState);
        }
        
        private void Onfenbo_mingxiOperationCompleted(object arg) {
            if ((this.fenbo_mingxiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.fenbo_mingxiCompleted(this, new fenbo_mingxiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/shangpin_shuxing", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable shangpin_shuxing(string plucode, string orgcode, string pluname, string barcode) {
            object[] results = this.Invoke("shangpin_shuxing", new object[] {
                        plucode,
                        orgcode,
                        pluname,
                        barcode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void shangpin_shuxingAsync(string plucode, string orgcode, string pluname, string barcode) {
            this.shangpin_shuxingAsync(plucode, orgcode, pluname, barcode, null);
        }
        
        /// <remarks/>
        public void shangpin_shuxingAsync(string plucode, string orgcode, string pluname, string barcode, object userState) {
            if ((this.shangpin_shuxingOperationCompleted == null)) {
                this.shangpin_shuxingOperationCompleted = new System.Threading.SendOrPostCallback(this.Onshangpin_shuxingOperationCompleted);
            }
            this.InvokeAsync("shangpin_shuxing", new object[] {
                        plucode,
                        orgcode,
                        pluname,
                        barcode}, this.shangpin_shuxingOperationCompleted, userState);
        }
        
        private void Onshangpin_shuxingOperationCompleted(object arg) {
            if ((this.shangpin_shuxingCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.shangpin_shuxingCompleted(this, new shangpin_shuxingCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/kucun_xs", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable kucun_xs(string code, System.DateTime workday1, System.DateTime workday2) {
            object[] results = this.Invoke("kucun_xs", new object[] {
                        code,
                        workday1,
                        workday2});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void kucun_xsAsync(string code, System.DateTime workday1, System.DateTime workday2) {
            this.kucun_xsAsync(code, workday1, workday2, null);
        }
        
        /// <remarks/>
        public void kucun_xsAsync(string code, System.DateTime workday1, System.DateTime workday2, object userState) {
            if ((this.kucun_xsOperationCompleted == null)) {
                this.kucun_xsOperationCompleted = new System.Threading.SendOrPostCallback(this.Onkucun_xsOperationCompleted);
            }
            this.InvokeAsync("kucun_xs", new object[] {
                        code,
                        workday1,
                        workday2}, this.kucun_xsOperationCompleted, userState);
        }
        
        private void Onkucun_xsOperationCompleted(object arg) {
            if ((this.kucun_xsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.kucun_xsCompleted(this, new kucun_xsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/haixin_mendian_kucun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable haixin_mendian_kucun() {
            object[] results = this.Invoke("haixin_mendian_kucun", new object[0]);
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void haixin_mendian_kucunAsync() {
            this.haixin_mendian_kucunAsync(null);
        }
        
        /// <remarks/>
        public void haixin_mendian_kucunAsync(object userState) {
            if ((this.haixin_mendian_kucunOperationCompleted == null)) {
                this.haixin_mendian_kucunOperationCompleted = new System.Threading.SendOrPostCallback(this.Onhaixin_mendian_kucunOperationCompleted);
            }
            this.InvokeAsync("haixin_mendian_kucun", new object[0], this.haixin_mendian_kucunOperationCompleted, userState);
        }
        
        private void Onhaixin_mendian_kucunOperationCompleted(object arg) {
            if ((this.haixin_mendian_kucunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.haixin_mendian_kucunCompleted(this, new haixin_mendian_kucunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/xiaoshou_pm", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable xiaoshou_pm(System.DateTime workday1, System.DateTime workday2) {
            object[] results = this.Invoke("xiaoshou_pm", new object[] {
                        workday1,
                        workday2});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void xiaoshou_pmAsync(System.DateTime workday1, System.DateTime workday2) {
            this.xiaoshou_pmAsync(workday1, workday2, null);
        }
        
        /// <remarks/>
        public void xiaoshou_pmAsync(System.DateTime workday1, System.DateTime workday2, object userState) {
            if ((this.xiaoshou_pmOperationCompleted == null)) {
                this.xiaoshou_pmOperationCompleted = new System.Threading.SendOrPostCallback(this.Onxiaoshou_pmOperationCompleted);
            }
            this.InvokeAsync("xiaoshou_pm", new object[] {
                        workday1,
                        workday2}, this.xiaoshou_pmOperationCompleted, userState);
        }
        
        private void Onxiaoshou_pmOperationCompleted(object arg) {
            if ((this.xiaoshou_pmCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.xiaoshou_pmCompleted(this, new xiaoshou_pmCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/zhinengbuhuo_jinhuoliang", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable zhinengbuhuo_jinhuoliang(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1) {
            object[] results = this.Invoke("zhinengbuhuo_jinhuoliang", new object[] {
                        day_min,
                        day_max,
                        day_song,
                        kucun_min,
                        workday1});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void zhinengbuhuo_jinhuoliangAsync(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1) {
            this.zhinengbuhuo_jinhuoliangAsync(day_min, day_max, day_song, kucun_min, workday1, null);
        }
        
        /// <remarks/>
        public void zhinengbuhuo_jinhuoliangAsync(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1, object userState) {
            if ((this.zhinengbuhuo_jinhuoliangOperationCompleted == null)) {
                this.zhinengbuhuo_jinhuoliangOperationCompleted = new System.Threading.SendOrPostCallback(this.Onzhinengbuhuo_jinhuoliangOperationCompleted);
            }
            this.InvokeAsync("zhinengbuhuo_jinhuoliang", new object[] {
                        day_min,
                        day_max,
                        day_song,
                        kucun_min,
                        workday1}, this.zhinengbuhuo_jinhuoliangOperationCompleted, userState);
        }
        
        private void Onzhinengbuhuo_jinhuoliangOperationCompleted(object arg) {
            if ((this.zhinengbuhuo_jinhuoliangCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.zhinengbuhuo_jinhuoliangCompleted(this, new zhinengbuhuo_jinhuoliangCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/quan_xiaoshou_mingxi", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable quan_xiaoshou_mingxi(System.DateTime workday33, System.DateTime workday44) {
            object[] results = this.Invoke("quan_xiaoshou_mingxi", new object[] {
                        workday33,
                        workday44});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void quan_xiaoshou_mingxiAsync(System.DateTime workday33, System.DateTime workday44) {
            this.quan_xiaoshou_mingxiAsync(workday33, workday44, null);
        }
        
        /// <remarks/>
        public void quan_xiaoshou_mingxiAsync(System.DateTime workday33, System.DateTime workday44, object userState) {
            if ((this.quan_xiaoshou_mingxiOperationCompleted == null)) {
                this.quan_xiaoshou_mingxiOperationCompleted = new System.Threading.SendOrPostCallback(this.Onquan_xiaoshou_mingxiOperationCompleted);
            }
            this.InvokeAsync("quan_xiaoshou_mingxi", new object[] {
                        workday33,
                        workday44}, this.quan_xiaoshou_mingxiOperationCompleted, userState);
        }
        
        private void Onquan_xiaoshou_mingxiOperationCompleted(object arg) {
            if ((this.quan_xiaoshou_mingxiCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.quan_xiaoshou_mingxiCompleted(this, new quan_xiaoshou_mingxiCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_xiaoshou_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_xiaoshou_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            object[] results = this.Invoke("APP_ziti_xiaoshou_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshou_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            this.APP_ziti_xiaoshou_chaxunAsync(workday33, workday44, orgname, djzbillno, orgcode, null);
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshou_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode, object userState) {
            if ((this.APP_ziti_xiaoshou_chaxunOperationCompleted == null)) {
                this.APP_ziti_xiaoshou_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_xiaoshou_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_xiaoshou_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode}, this.APP_ziti_xiaoshou_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_xiaoshou_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_xiaoshou_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_xiaoshou_chaxunCompleted(this, new APP_ziti_xiaoshou_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_zhifufangshi_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_zhifufangshi_chaxun(string billno) {
            object[] results = this.Invoke("APP_ziti_zhifufangshi_chaxun", new object[] {
                        billno});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_chaxunAsync(string billno) {
            this.APP_ziti_zhifufangshi_chaxunAsync(billno, null);
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_chaxunAsync(string billno, object userState) {
            if ((this.APP_ziti_zhifufangshi_chaxunOperationCompleted == null)) {
                this.APP_ziti_zhifufangshi_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_zhifufangshi_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_zhifufangshi_chaxun", new object[] {
                        billno}, this.APP_ziti_zhifufangshi_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_zhifufangshi_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_zhifufangshi_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_zhifufangshi_chaxunCompleted(this, new APP_ziti_zhifufangshi_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_zhifufangshi_huizong_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_zhifufangshi_huizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode) {
            object[] results = this.Invoke("APP_ziti_zhifufangshi_huizong_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        orgcode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_huizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode) {
            this.APP_ziti_zhifufangshi_huizong_chaxunAsync(workday33, workday44, orgname, orgcode, null);
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_huizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, object userState) {
            if ((this.APP_ziti_zhifufangshi_huizong_chaxunOperationCompleted == null)) {
                this.APP_ziti_zhifufangshi_huizong_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_zhifufangshi_huizong_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_zhifufangshi_huizong_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        orgcode}, this.APP_ziti_zhifufangshi_huizong_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_zhifufangshi_huizong_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_zhifufangshi_huizong_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_zhifufangshi_huizong_chaxunCompleted(this, new APP_ziti_zhifufangshi_huizong_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_zhifufangshi_mingxi_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_zhifufangshi_mingxi_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno) {
            object[] results = this.Invoke("APP_ziti_zhifufangshi_mingxi_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        orgcode,
                        billno});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_mingxi_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno) {
            this.APP_ziti_zhifufangshi_mingxi_chaxunAsync(workday33, workday44, orgname, orgcode, billno, null);
        }
        
        /// <remarks/>
        public void APP_ziti_zhifufangshi_mingxi_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno, object userState) {
            if ((this.APP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted == null)) {
                this.APP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_zhifufangshi_mingxi_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        orgcode,
                        billno}, this.APP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_zhifufangshi_mingxi_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_zhifufangshi_mingxi_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_zhifufangshi_mingxi_chaxunCompleted(this, new APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_xiaoshoumingxi_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_xiaoshoumingxi_chaxun(string billno) {
            object[] results = this.Invoke("APP_ziti_xiaoshoumingxi_chaxun", new object[] {
                        billno});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshoumingxi_chaxunAsync(string billno) {
            this.APP_ziti_xiaoshoumingxi_chaxunAsync(billno, null);
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshoumingxi_chaxunAsync(string billno, object userState) {
            if ((this.APP_ziti_xiaoshoumingxi_chaxunOperationCompleted == null)) {
                this.APP_ziti_xiaoshoumingxi_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_xiaoshoumingxi_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_xiaoshoumingxi_chaxun", new object[] {
                        billno}, this.APP_ziti_xiaoshoumingxi_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_xiaoshoumingxi_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_xiaoshoumingxi_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_xiaoshoumingxi_chaxunCompleted(this, new APP_ziti_xiaoshoumingxi_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_xiaoshoudan_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_xiaoshoudan_chaxun(string billno) {
            object[] results = this.Invoke("APP_ziti_xiaoshoudan_chaxun", new object[] {
                        billno});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshoudan_chaxunAsync(string billno) {
            this.APP_ziti_xiaoshoudan_chaxunAsync(billno, null);
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshoudan_chaxunAsync(string billno, object userState) {
            if ((this.APP_ziti_xiaoshoudan_chaxunOperationCompleted == null)) {
                this.APP_ziti_xiaoshoudan_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_xiaoshoudan_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_xiaoshoudan_chaxun", new object[] {
                        billno}, this.APP_ziti_xiaoshoudan_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_xiaoshoudan_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_xiaoshoudan_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_xiaoshoudan_chaxunCompleted(this, new APP_ziti_xiaoshoudan_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_xiaoshouhuizong_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_xiaoshouhuizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            object[] results = this.Invoke("APP_ziti_xiaoshouhuizong_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshouhuizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            this.APP_ziti_xiaoshouhuizong_chaxunAsync(workday33, workday44, orgname, djzbillno, orgcode, null);
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshouhuizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode, object userState) {
            if ((this.APP_ziti_xiaoshouhuizong_chaxunOperationCompleted == null)) {
                this.APP_ziti_xiaoshouhuizong_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_xiaoshouhuizong_chaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_xiaoshouhuizong_chaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode}, this.APP_ziti_xiaoshouhuizong_chaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_xiaoshouhuizong_chaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_xiaoshouhuizong_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_xiaoshouhuizong_chaxunCompleted(this, new APP_ziti_xiaoshouhuizong_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/APP_ziti_xiaoshouhuizong_yuechaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable APP_ziti_xiaoshouhuizong_yuechaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            object[] results = this.Invoke("APP_ziti_xiaoshouhuizong_yuechaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshouhuizong_yuechaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            this.APP_ziti_xiaoshouhuizong_yuechaxunAsync(workday33, workday44, orgname, djzbillno, orgcode, null);
        }
        
        /// <remarks/>
        public void APP_ziti_xiaoshouhuizong_yuechaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode, object userState) {
            if ((this.APP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted == null)) {
                this.APP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnAPP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted);
            }
            this.InvokeAsync("APP_ziti_xiaoshouhuizong_yuechaxun", new object[] {
                        workday33,
                        workday44,
                        orgname,
                        djzbillno,
                        orgcode}, this.APP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted, userState);
        }
        
        private void OnAPP_ziti_xiaoshouhuizong_yuechaxunOperationCompleted(object arg) {
            if ((this.APP_ziti_xiaoshouhuizong_yuechaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.APP_ziti_xiaoshouhuizong_yuechaxunCompleted(this, new APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_yanshoudan_chaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_yanshoudan_chaxun(System.DateTime workday33, System.DateTime workday44, string SUPNAME) {
            object[] results = this.Invoke("HX_yanshoudan_chaxun", new object[] {
                        workday33,
                        workday44,
                        SUPNAME});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_yanshoudan_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string SUPNAME) {
            this.HX_yanshoudan_chaxunAsync(workday33, workday44, SUPNAME, null);
        }
        
        /// <remarks/>
        public void HX_yanshoudan_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string SUPNAME, object userState) {
            if ((this.HX_yanshoudan_chaxunOperationCompleted == null)) {
                this.HX_yanshoudan_chaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_yanshoudan_chaxunOperationCompleted);
            }
            this.InvokeAsync("HX_yanshoudan_chaxun", new object[] {
                        workday33,
                        workday44,
                        SUPNAME}, this.HX_yanshoudan_chaxunOperationCompleted, userState);
        }
        
        private void OnHX_yanshoudan_chaxunOperationCompleted(object arg) {
            if ((this.HX_yanshoudan_chaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_yanshoudan_chaxunCompleted(this, new HX_yanshoudan_chaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_xiche_zhifufangshi_huizongchaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_xiche_zhifufangshi_huizongchaxun(System.DateTime workday33, System.DateTime workday44) {
            object[] results = this.Invoke("HX_xiche_zhifufangshi_huizongchaxun", new object[] {
                        workday33,
                        workday44});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_xiche_zhifufangshi_huizongchaxunAsync(System.DateTime workday33, System.DateTime workday44) {
            this.HX_xiche_zhifufangshi_huizongchaxunAsync(workday33, workday44, null);
        }
        
        /// <remarks/>
        public void HX_xiche_zhifufangshi_huizongchaxunAsync(System.DateTime workday33, System.DateTime workday44, object userState) {
            if ((this.HX_xiche_zhifufangshi_huizongchaxunOperationCompleted == null)) {
                this.HX_xiche_zhifufangshi_huizongchaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_xiche_zhifufangshi_huizongchaxunOperationCompleted);
            }
            this.InvokeAsync("HX_xiche_zhifufangshi_huizongchaxun", new object[] {
                        workday33,
                        workday44}, this.HX_xiche_zhifufangshi_huizongchaxunOperationCompleted, userState);
        }
        
        private void OnHX_xiche_zhifufangshi_huizongchaxunOperationCompleted(object arg) {
            if ((this.HX_xiche_zhifufangshi_huizongchaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_xiche_zhifufangshi_huizongchaxunCompleted(this, new HX_xiche_zhifufangshi_huizongchaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_xiangyan_kucunchaxun", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_xiangyan_kucunchaxun(string orgcode, string orgname, string plucode) {
            object[] results = this.Invoke("HX_xiangyan_kucunchaxun", new object[] {
                        orgcode,
                        orgname,
                        plucode});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_xiangyan_kucunchaxunAsync(string orgcode, string orgname, string plucode) {
            this.HX_xiangyan_kucunchaxunAsync(orgcode, orgname, plucode, null);
        }
        
        /// <remarks/>
        public void HX_xiangyan_kucunchaxunAsync(string orgcode, string orgname, string plucode, object userState) {
            if ((this.HX_xiangyan_kucunchaxunOperationCompleted == null)) {
                this.HX_xiangyan_kucunchaxunOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_xiangyan_kucunchaxunOperationCompleted);
            }
            this.InvokeAsync("HX_xiangyan_kucunchaxun", new object[] {
                        orgcode,
                        orgname,
                        plucode}, this.HX_xiangyan_kucunchaxunOperationCompleted, userState);
        }
        
        private void OnHX_xiangyan_kucunchaxunOperationCompleted(object arg) {
            if ((this.HX_xiangyan_kucunchaxunCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_xiangyan_kucunchaxunCompleted(this, new HX_xiangyan_kucunchaxunCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_xiaoshou_mingxi_tSalPluDetail", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_xiaoshou_mingxi_tSalPluDetail(int serialno_start, int serialno_end) {
            object[] results = this.Invoke("HX_xiaoshou_mingxi_tSalPluDetail", new object[] {
                        serialno_start,
                        serialno_end});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_xiaoshou_mingxi_tSalPluDetailAsync(int serialno_start, int serialno_end) {
            this.HX_xiaoshou_mingxi_tSalPluDetailAsync(serialno_start, serialno_end, null);
        }
        
        /// <remarks/>
        public void HX_xiaoshou_mingxi_tSalPluDetailAsync(int serialno_start, int serialno_end, object userState) {
            if ((this.HX_xiaoshou_mingxi_tSalPluDetailOperationCompleted == null)) {
                this.HX_xiaoshou_mingxi_tSalPluDetailOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_xiaoshou_mingxi_tSalPluDetailOperationCompleted);
            }
            this.InvokeAsync("HX_xiaoshou_mingxi_tSalPluDetail", new object[] {
                        serialno_start,
                        serialno_end}, this.HX_xiaoshou_mingxi_tSalPluDetailOperationCompleted, userState);
        }
        
        private void OnHX_xiaoshou_mingxi_tSalPluDetailOperationCompleted(object arg) {
            if ((this.HX_xiaoshou_mingxi_tSalPluDetailCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_xiaoshou_mingxi_tSalPluDetailCompleted(this, new HX_xiaoshou_mingxi_tSalPluDetailCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_xiaoshou_maxserialno_tSalPluDetail", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_xiaoshou_maxserialno_tSalPluDetail() {
            object[] results = this.Invoke("HX_xiaoshou_maxserialno_tSalPluDetail", new object[0]);
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_xiaoshou_maxserialno_tSalPluDetailAsync() {
            this.HX_xiaoshou_maxserialno_tSalPluDetailAsync(null);
        }
        
        /// <remarks/>
        public void HX_xiaoshou_maxserialno_tSalPluDetailAsync(object userState) {
            if ((this.HX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted == null)) {
                this.HX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted);
            }
            this.InvokeAsync("HX_xiaoshou_maxserialno_tSalPluDetail", new object[0], this.HX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted, userState);
        }
        
        private void OnHX_xiaoshou_maxserialno_tSalPluDetailOperationCompleted(object arg) {
            if ((this.HX_xiaoshou_maxserialno_tSalPluDetailCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_xiaoshou_maxserialno_tSalPluDetailCompleted(this, new HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_bianlidian_bishu", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_bianlidian_bishu(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname) {
            object[] results = this.Invoke("HX_bianlidian_bishu", new object[] {
                        workday33,
                        workday44,
                        orgcode,
                        orgname});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_bianlidian_bishuAsync(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname) {
            this.HX_bianlidian_bishuAsync(workday33, workday44, orgcode, orgname, null);
        }
        
        /// <remarks/>
        public void HX_bianlidian_bishuAsync(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname, object userState) {
            if ((this.HX_bianlidian_bishuOperationCompleted == null)) {
                this.HX_bianlidian_bishuOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_bianlidian_bishuOperationCompleted);
            }
            this.InvokeAsync("HX_bianlidian_bishu", new object[] {
                        workday33,
                        workday44,
                        orgcode,
                        orgname}, this.HX_bianlidian_bishuOperationCompleted, userState);
        }
        
        private void OnHX_bianlidian_bishuOperationCompleted(object arg) {
            if ((this.HX_bianlidian_bishuCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_bianlidian_bishuCompleted(this, new HX_bianlidian_bishuCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_diaobodan", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_diaobodan(System.DateTime workday33, System.DateTime workday44) {
            object[] results = this.Invoke("HX_diaobodan", new object[] {
                        workday33,
                        workday44});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_diaobodanAsync(System.DateTime workday33, System.DateTime workday44) {
            this.HX_diaobodanAsync(workday33, workday44, null);
        }
        
        /// <remarks/>
        public void HX_diaobodanAsync(System.DateTime workday33, System.DateTime workday44, object userState) {
            if ((this.HX_diaobodanOperationCompleted == null)) {
                this.HX_diaobodanOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_diaobodanOperationCompleted);
            }
            this.InvokeAsync("HX_diaobodan", new object[] {
                        workday33,
                        workday44}, this.HX_diaobodanOperationCompleted, userState);
        }
        
        private void OnHX_diaobodanOperationCompleted(object arg) {
            if ((this.HX_diaobodanCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_diaobodanCompleted(this, new HX_diaobodanCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_yanshoudan", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_yanshoudan(System.DateTime workday33, System.DateTime workday44) {
            object[] results = this.Invoke("HX_yanshoudan", new object[] {
                        workday33,
                        workday44});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_yanshoudanAsync(System.DateTime workday33, System.DateTime workday44) {
            this.HX_yanshoudanAsync(workday33, workday44, null);
        }
        
        /// <remarks/>
        public void HX_yanshoudanAsync(System.DateTime workday33, System.DateTime workday44, object userState) {
            if ((this.HX_yanshoudanOperationCompleted == null)) {
                this.HX_yanshoudanOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_yanshoudanOperationCompleted);
            }
            this.InvokeAsync("HX_yanshoudan", new object[] {
                        workday33,
                        workday44}, this.HX_yanshoudanOperationCompleted, userState);
        }
        
        private void OnHX_yanshoudanOperationCompleted(object arg) {
            if ((this.HX_yanshoudanCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_yanshoudanCompleted(this, new HX_yanshoudanCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_tuangoudan_TJ", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_tuangoudan_TJ(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            object[] results = this.Invoke("HX_tuangoudan_TJ", new object[] {
                        billno,
                        workday_k,
                        workday_d});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_tuangoudan_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            this.HX_tuangoudan_TJAsync(billno, workday_k, workday_d, null);
        }
        
        /// <remarks/>
        public void HX_tuangoudan_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d, object userState) {
            if ((this.HX_tuangoudan_TJOperationCompleted == null)) {
                this.HX_tuangoudan_TJOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_tuangoudan_TJOperationCompleted);
            }
            this.InvokeAsync("HX_tuangoudan_TJ", new object[] {
                        billno,
                        workday_k,
                        workday_d}, this.HX_tuangoudan_TJOperationCompleted, userState);
        }
        
        private void OnHX_tuangoudan_TJOperationCompleted(object arg) {
            if ((this.HX_tuangoudan_TJCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_tuangoudan_TJCompleted(this, new HX_tuangoudan_TJCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_cuxiaodan_yue_TJ", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_cuxiaodan_yue_TJ(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            object[] results = this.Invoke("HX_cuxiaodan_yue_TJ", new object[] {
                        billno,
                        workday_k,
                        workday_d});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_cuxiaodan_yue_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            this.HX_cuxiaodan_yue_TJAsync(billno, workday_k, workday_d, null);
        }
        
        /// <remarks/>
        public void HX_cuxiaodan_yue_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d, object userState) {
            if ((this.HX_cuxiaodan_yue_TJOperationCompleted == null)) {
                this.HX_cuxiaodan_yue_TJOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_cuxiaodan_yue_TJOperationCompleted);
            }
            this.InvokeAsync("HX_cuxiaodan_yue_TJ", new object[] {
                        billno,
                        workday_k,
                        workday_d}, this.HX_cuxiaodan_yue_TJOperationCompleted, userState);
        }
        
        private void OnHX_cuxiaodan_yue_TJOperationCompleted(object arg) {
            if ((this.HX_cuxiaodan_yue_TJCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_cuxiaodan_yue_TJCompleted(this, new HX_cuxiaodan_yue_TJCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapDocumentMethodAttribute("http://tempuri.org/HX_cuxiaodan_TJ", RequestNamespace="http://tempuri.org/", ResponseNamespace="http://tempuri.org/", Use=System.Web.Services.Description.SoapBindingUse.Literal, ParameterStyle=System.Web.Services.Protocols.SoapParameterStyle.Wrapped)]
        public System.Data.DataTable HX_cuxiaodan_TJ(string billno, System.DateTime workday_k) {
            object[] results = this.Invoke("HX_cuxiaodan_TJ", new object[] {
                        billno,
                        workday_k});
            return ((System.Data.DataTable)(results[0]));
        }
        
        /// <remarks/>
        public void HX_cuxiaodan_TJAsync(string billno, System.DateTime workday_k) {
            this.HX_cuxiaodan_TJAsync(billno, workday_k, null);
        }
        
        /// <remarks/>
        public void HX_cuxiaodan_TJAsync(string billno, System.DateTime workday_k, object userState) {
            if ((this.HX_cuxiaodan_TJOperationCompleted == null)) {
                this.HX_cuxiaodan_TJOperationCompleted = new System.Threading.SendOrPostCallback(this.OnHX_cuxiaodan_TJOperationCompleted);
            }
            this.InvokeAsync("HX_cuxiaodan_TJ", new object[] {
                        billno,
                        workday_k}, this.HX_cuxiaodan_TJOperationCompleted, userState);
        }
        
        private void OnHX_cuxiaodan_TJOperationCompleted(object arg) {
            if ((this.HX_cuxiaodan_TJCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.HX_cuxiaodan_TJCompleted(this, new HX_cuxiaodan_TJCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HelloWorldCompletedEventHandler(object sender, HelloWorldCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HelloWorldCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HelloWorldCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void fenbo_dan_hkCompletedEventHandler(object sender, fenbo_dan_hkCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class fenbo_dan_hkCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal fenbo_dan_hkCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void fenbo_mingxiCompletedEventHandler(object sender, fenbo_mingxiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class fenbo_mingxiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal fenbo_mingxiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void shangpin_shuxingCompletedEventHandler(object sender, shangpin_shuxingCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class shangpin_shuxingCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal shangpin_shuxingCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void kucun_xsCompletedEventHandler(object sender, kucun_xsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class kucun_xsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal kucun_xsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void haixin_mendian_kucunCompletedEventHandler(object sender, haixin_mendian_kucunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class haixin_mendian_kucunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal haixin_mendian_kucunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void xiaoshou_pmCompletedEventHandler(object sender, xiaoshou_pmCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class xiaoshou_pmCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal xiaoshou_pmCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void zhinengbuhuo_jinhuoliangCompletedEventHandler(object sender, zhinengbuhuo_jinhuoliangCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class zhinengbuhuo_jinhuoliangCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal zhinengbuhuo_jinhuoliangCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void quan_xiaoshou_mingxiCompletedEventHandler(object sender, quan_xiaoshou_mingxiCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class quan_xiaoshou_mingxiCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal quan_xiaoshou_mingxiCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_xiaoshou_chaxunCompletedEventHandler(object sender, APP_ziti_xiaoshou_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_xiaoshou_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_xiaoshou_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_zhifufangshi_chaxunCompletedEventHandler(object sender, APP_ziti_zhifufangshi_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_zhifufangshi_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_zhifufangshi_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_zhifufangshi_huizong_chaxunCompletedEventHandler(object sender, APP_ziti_zhifufangshi_huizong_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_zhifufangshi_huizong_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_zhifufangshi_huizong_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventHandler(object sender, APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_zhifufangshi_mingxi_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_xiaoshoumingxi_chaxunCompletedEventHandler(object sender, APP_ziti_xiaoshoumingxi_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_xiaoshoumingxi_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_xiaoshoumingxi_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_xiaoshoudan_chaxunCompletedEventHandler(object sender, APP_ziti_xiaoshoudan_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_xiaoshoudan_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_xiaoshoudan_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_xiaoshouhuizong_chaxunCompletedEventHandler(object sender, APP_ziti_xiaoshouhuizong_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_xiaoshouhuizong_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_xiaoshouhuizong_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventHandler(object sender, APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal APP_ziti_xiaoshouhuizong_yuechaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_yanshoudan_chaxunCompletedEventHandler(object sender, HX_yanshoudan_chaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_yanshoudan_chaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_yanshoudan_chaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_xiche_zhifufangshi_huizongchaxunCompletedEventHandler(object sender, HX_xiche_zhifufangshi_huizongchaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_xiche_zhifufangshi_huizongchaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_xiche_zhifufangshi_huizongchaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_xiangyan_kucunchaxunCompletedEventHandler(object sender, HX_xiangyan_kucunchaxunCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_xiangyan_kucunchaxunCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_xiangyan_kucunchaxunCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_xiaoshou_mingxi_tSalPluDetailCompletedEventHandler(object sender, HX_xiaoshou_mingxi_tSalPluDetailCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_xiaoshou_mingxi_tSalPluDetailCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_xiaoshou_mingxi_tSalPluDetailCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventHandler(object sender, HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_xiaoshou_maxserialno_tSalPluDetailCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_bianlidian_bishuCompletedEventHandler(object sender, HX_bianlidian_bishuCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_bianlidian_bishuCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_bianlidian_bishuCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_diaobodanCompletedEventHandler(object sender, HX_diaobodanCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_diaobodanCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_diaobodanCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_yanshoudanCompletedEventHandler(object sender, HX_yanshoudanCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_yanshoudanCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_yanshoudanCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_tuangoudan_TJCompletedEventHandler(object sender, HX_tuangoudan_TJCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_tuangoudan_TJCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_tuangoudan_TJCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_cuxiaodan_yue_TJCompletedEventHandler(object sender, HX_cuxiaodan_yue_TJCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_cuxiaodan_yue_TJCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_cuxiaodan_yue_TJCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    public delegate void HX_cuxiaodan_TJCompletedEventHandler(object sender, HX_cuxiaodan_TJCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.9032.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class HX_cuxiaodan_TJCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal HX_cuxiaodan_TJCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public System.Data.DataTable Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((System.Data.DataTable)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591