<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Owin.Security.Facebook</name>
    </assembly>
    <members>
        <member name="T:Owin.FacebookAuthenticationExtensions">
            <summary>
            Extension methods for using <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Owin.FacebookAuthenticationExtensions.UseFacebookAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions)">
            <summary>
            Authenticate users using Facebook
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="options">Middleware configuration options</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="M:Owin.FacebookAuthenticationExtensions.UseFacebookAuthentication(O<PERSON>.<PERSON><PERSON>uilder,System.String,System.String)">
            <summary>
            Authenticate users using Facebook
            </summary>
            <param name="app">The <see cref="T:Owin.IAppBuilder"/> passed to the configuration method</param>
            <param name="appId">The appId assigned by Facebook</param>
            <param name="appSecret">The appSecret assigned by Facebook</param>
            <returns>The updated <see cref="T:Owin.IAppBuilder"/></returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware">
            <summary>
            OWIN middleware for authenticating users using Facebook
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware"/>
            </summary>
            <param name="next">The next middleware in the OWIN pipeline to invoke</param>
            <param name="app">The OWIN application</param>
            <param name="options">Configuration options for the middleware</param>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware.CreateHandler">
            <summary>
            Provides the <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> object for processing authentication-related requests.
            </summary>
            <returns>An <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler"/> configured with the <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions"/> supplied to the constructor.</returns>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions">
            <summary>
            Configuration options for <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.#ctor">
            <summary>
            Initializes a new <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions"/>
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.AppId">
            <summary>
            Gets or sets the Facebook-assigned appId
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.AppSecret">
            <summary>
            Gets or sets the Facebook-assigned app secret
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.AuthorizationEndpoint">
            <summary>
            Gets or sets the URI where the client will be redirected to authenticate.
            The default value is 'https://www.facebook.com/dialog/oauth'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.TokenEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to exchange the OAuth token.
            The default value is 'https://graph.facebook.com/oauth/access_token'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.UserInformationEndpoint">
            <summary>
            Gets or sets the URI the middleware will access to obtain the user information.
            The default value is 'https://graph.facebook.com/me'.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelCertificateValidator">
            <summary>
            Gets or sets the a pinned certificate validator to use to validate the endpoints used
            in back channel communications belong to Facebook.
            </summary>
            <value>
            The pinned certificate validator.
            </value>
            <remarks>If this property is null then the default certificate checks are performed,
            validating the subject name and if the signing chain is a trusted party.</remarks>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelTimeout">
            <summary>
            Gets or sets timeout value in milliseconds for back channel communications with Facebook.
            </summary>
            <value>
            The back channel timeout in milliseconds.
            </value>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelHttpHandler">
            <summary>
            The HttpMessageHandler used to communicate with Facebook.
            This cannot be set at the same time as BackchannelCertificateValidator unless the value 
            can be downcast to a WebRequestHandler.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Caption">
            <summary>
            Get or sets the text that the user can display on a sign in user interface.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.CallbackPath">
            <summary>
            The request path within the application's base path where the user-agent will be returned.
            The middleware will process this request when it arrives.
            Default value is "/signin-facebook".
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.SignInAsAuthenticationType">
            <summary>
            Gets or sets the name of another authentication middleware which will be responsible for actually issuing a user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Provider">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider"/> used to handle authentication events.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.StateDataFormat">
            <summary>
            Gets or sets the type used to secure data handled by the middleware.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Scope">
            <summary>
            A list of permissions to request.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.SendAppSecretProof">
            <summary>
            Gets or sets if the appsecret_proof should be generated and sent with Facebook API calls.
            This is enabled by default.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext">
            <summary>
            Context passed when a Challenge causes a redirect to authorize endpoint in the Facebook middleware
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions,Microsoft.Owin.Security.AuthenticationProperties,System.String)">
            <summary>
            Creates a new context object.
            </summary>
            <param name="context">The OWIN request context</param>
            <param name="options">The Facebook middleware options</param>
            <param name="properties">The authenticaiton properties of the challenge</param>
            <param name="redirectUri">The initial redirect URI</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.RedirectUri">
            <summary>
            Gets the URI used for the redirect operation.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.Properties">
            <summary>
            Gets the authentication properties of the challenge
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext">
            <summary>
            Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,Newtonsoft.Json.Linq.JObject,System.String,System.String)">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext"/>
            </summary>
            <param name="context">The OWIN environment</param>
            <param name="user">The JSON-serialized user</param>
            <param name="accessToken">Facebook Access token</param>
            <param name="expires">Seconds until expiration</param>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.User">
            <summary>
            Gets the JSON-serialized user
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.AccessToken">
            <summary>
            Gets the Facebook access token
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.ExpiresIn">
            <summary>
            Gets the Facebook access token expiration time
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Id">
            <summary>
            Gets the Facebook user ID
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Name">
            <summary>
            Gets the user's name
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.UserName">
            <summary>
            Gets the Facebook username
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Email">
            <summary>
            Gets the Facebook email
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Identity">
            <summary>
            Gets the <see cref="T:System.Security.Claims.ClaimsIdentity"/> representing the user
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Properties">
            <summary>
            Gets or sets a property bag for common authentication properties
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider">
            <summary>
            Default <see cref="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider"/> implementation.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider">
            <summary>
            Specifies callback methods which the <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware"></see> invokes to enable developer control over the authentication process. /&gt;
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext)">
            <summary>
            Invoked whenever Facebook succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context"></param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Facebook middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.#ctor">
            <summary>
            Initializes a <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider"/>
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext)">
            <summary>
            Invoked whenever Facebook succesfully authenticates a user
            </summary>
            <param name="context">Contains information about the login session as well as the user <see cref="T:System.Security.Claims.ClaimsIdentity"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext)">
            <summary>
            Invoked prior to the <see cref="T:System.Security.Claims.ClaimsIdentity"/> being saved in a local cookie and the browser being redirected to the originally requested URL.
            </summary>
            <param name="context"></param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> representing the completed operation.</returns>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext)">
            <summary>
            Called when a Challenge causes a redirect to authorize endpoint in the Facebook middleware
            </summary>
            <param name="context">Contains redirect URI and <see cref="T:Microsoft.Owin.Security.AuthenticationProperties"/> of the challenge </param>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnAuthenticated">
            <summary>
            Gets or sets the function that is invoked when the Authenticated method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnReturnEndpoint">
            <summary>
            Gets or sets the function that is invoked when the ReturnEndpoint method is invoked.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnApplyRedirect">
            <summary>
            Gets or sets the delegate that is invoked when the ApplyRedirect method is invoked.
            </summary>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext">
            <summary>
            Provides context information to middleware providers.
            </summary>
        </member>
        <member name="M:Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)">
            <summary>
            
            </summary>
            <param name="context">OWIN environment</param>
            <param name="ticket">The authentication ticket</param>
        </member>
        <member name="T:Microsoft.Owin.Security.Facebook.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.Resources.Exception_OptionMustBeProvided">
            <summary>
              Looks up a localized string similar to The &apos;{0}&apos; option must be provided..
            </summary>
        </member>
        <member name="P:Microsoft.Owin.Security.Facebook.Resources.Exception_ValidatorHandlerMismatch">
            <summary>
              Looks up a localized string similar to An ICertificateValidator cannot be specified at the same time as an HttpMessageHandler unless it is a WebRequestHandler..
            </summary>
        </member>
    </members>
</doc>
