﻿
@{
    ViewBag.Title = "haixin_yanshoudan_chaxun";
}

<h2>海信验收单查询</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_shxysdcx" id="workday_shxysdcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_dhxysdcx" id="workday_dhxysdcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;供应商名称：<input type="text" name="SUPNAME" id="SUPNAME" size="15">
            </div>
            
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/haixin_yanshoudan_chaxun?workday_shxysdcx=" + $('#workday_shxysdcx').val() + "&workday_dhxysdcx=" + $('#workday_dhxysdcx').val() + "&SUPNAME=" + $('#SUPNAME').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>单据号</td><td>提交日期</td><td>提交人编码</td><td>提交人姓名</td><td>记账日期</td><td>记账人编码</td><td>记账人姓名</td><td>报表日期</td><td>验收代码</td><td>验收组织编码</td><td>验收组织名称</td><td>供应商名称</td><td>合同编码</td><td>合同名称</td><td>相关单据号</td><td>上传状态</td><td>商品|编码</td><td>商品|名称</td><td>商品条码</td><td>规格</td><td>单位</td><td>含税进价</td><td>无税进价</td><td>售价</td><td>进项税率</td><td>包装|单位</td><td>包装|细数</td><td>进货数量</td><td>含税进价金额</td><td>无税进价金额</td><td>进项税额</td><td>售价金额</td><td>进销差额</td><td>进销差率</td><td>生产日期</td><td>到期日期</td><td>保质期</td><td>备注</td><td>销项税率</td><td>销项税额</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.BILLNO + '</td><td>' + item.TJDATE + '</td><td>' + item.TJRCODE + '</td><td>' + item.TJRNAME + '</td><td>' + item.JZDATE + '</td><td>' + item.JZRCODE + '</td><td>' + item.JZRNAME + '</td><td>' + item.RPTDATE + '</td><td>' + item.YWTYPE + '</td><td>' + item.ORGCODE
                            + '</td><td>' + item.ORGNAME + '</td><td>' + item.SUPNAME + '</td><td>' + item.HTCODE + '</td><td>' + item.HTNAME + '</td><td>' + item.REFBILLNO + '</td><td>' + item.UPSTATUS + '</td><td>' + item.PLUCODE + '</td><td>' + item.PLUNAME + '</td><td>' + item.BARCODE
                            + '</td><td>' + item.SPEC + '</td><td>' + item.UNIT + '</td><td>' + item.HJPRICE + '</td><td>' + item.WJPRICE + '</td><td>' + item.PRICE + '</td><td>' + item.JTAXRATE + '</td><td>' + item.PACKUNIT + '</td><td>' + item.PACKQTY + '</td><td>' + item.JHCOUNT
                            + '</td><td>' + item.HCOST + '</td><td>' + item.WCOST + '</td><td>' + item.JTAXTOTAL + '</td><td>' + item.STOTAL + '</td><td>' + item.CJTOTAL + '</td><td>' + item.CJRATE + '%</td><td>' + item.SCDATE + '</td><td>' + item.DQDATE + '</td><td>' + item.BZDAYS
                            + '</td><td>' + item.REMARK + '</td><td>' + item.XTAXRATE + '</td><td>' + item.XTAXTOTAL + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/haixin_yanshoudan_chaxun_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
