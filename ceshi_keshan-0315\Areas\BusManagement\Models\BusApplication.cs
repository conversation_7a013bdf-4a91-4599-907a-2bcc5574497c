using System;
using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace ceshi_keshan_0315.Areas.BusManagement.Models
{
    /// <summary>
    /// 公车申请记录实体
    /// 新增模块：公车申请和审批流程管理
    /// </summary>
    [Table(Name = "T_BusApplication")]
    public class BusApplication
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Column(IsPrimary = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 公车ID
        /// </summary>
        [Column]
        [Required(ErrorMessage = "请选择公车")]
        public int BusId { get; set; }

        /// <summary>
        /// 申请人姓名
        /// </summary>
        [Column(StringLength = 50, IsNullable = false)]
        [Required(ErrorMessage = "申请人姓名不能为空")]
        [Display(Name = "申请人")]
        public string ApplicantName { get; set; }

        /// <summary>
        /// 申请人部门
        /// </summary>
        [Column(StringLength = 100)]
        [Display(Name = "申请部门")]
        public string Department { get; set; }

        /// <summary>
        /// 申请人联系电话
        /// </summary>
        [Column(StringLength = 20)]
        [Display(Name = "联系电话")]
        [Phone(ErrorMessage = "请输入正确的电话号码")]
        public string Phone { get; set; }

        /// <summary>
        /// 用车目的
        /// </summary>
        [Column(StringLength = 200)]
        [Required(ErrorMessage = "用车目的不能为空")]
        [Display(Name = "用车目的")]
        public string Purpose { get; set; }

        /// <summary>
        /// 目的地
        /// </summary>
        [Column(StringLength = 200)]
        [Display(Name = "目的地")]
        public string Destination { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [Column]
        [Required(ErrorMessage = "开始时间不能为空")]
        [Display(Name = "开始时间")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [Column]
        [Required(ErrorMessage = "结束时间不能为空")]
        [Display(Name = "结束时间")]
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 预计人数
        /// </summary>
        [Column]
        [Display(Name = "预计人数")]
        [Range(1, 50, ErrorMessage = "人数必须在1-50之间")]
        public int EstimatedPeople { get; set; }

        /// <summary>
        /// 申请状态
        /// </summary>
        [Column]
        [Display(Name = "申请状态")]
        public ApplicationStatus Status { get; set; } = ApplicationStatus.Pending;

        /// <summary>
        /// 审批人ID
        /// </summary>
        [Column(StringLength = 50)]
        public string ReviewerId { get; set; }

        /// <summary>
        /// 审批人姓名
        /// </summary>
        [Column(StringLength = 50)]
        [Display(Name = "审批人")]
        public string ReviewerName { get; set; }

        /// <summary>
        /// 审批时间
        /// </summary>
        [Column]
        [Display(Name = "审批时间")]
        public DateTime? ReviewTime { get; set; }

        /// <summary>
        /// 审批意见
        /// </summary>
        [Column(StringLength = 500)]
        [Display(Name = "审批意见")]
        public string ReviewComments { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Column]
        public bool IsDeleted { get; set; } = false;

        /// <summary>
        /// 导航属性：关联的公车信息
        /// </summary>
        [Navigate(nameof(BusId))]
        public Bus Bus { get; set; }
    }

    /// <summary>
    /// 申请状态枚举
    /// </summary>
    public enum ApplicationStatus
    {
        /// <summary>
        /// 待审批
        /// </summary>
        [Display(Name = "待审批")]
        Pending = 1,

        /// <summary>
        /// 已批准
        /// </summary>
        [Display(Name = "已批准")]
        Approved = 2,

        /// <summary>
        /// 已拒绝
        /// </summary>
        [Display(Name = "已拒绝")]
        Rejected = 3,

        /// <summary>
        /// 已完成
        /// </summary>
        [Display(Name = "已完成")]
        Completed = 4,

        /// <summary>
        /// 已取消
        /// </summary>
        [Display(Name = "已取消")]
        Cancelled = 5
    }
}
