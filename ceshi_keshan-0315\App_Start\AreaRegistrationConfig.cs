using System.Web.Mvc;

namespace ceshi_keshan_0315.App_Start
{
    /// <summary>
    /// Area 注册配置管理
    /// 新增模块：统一管理所有 Area 的注册
    /// </summary>
    public static class AreaRegistrationConfig
    {
        /// <summary>
        /// 注册所有 Areas
        /// 这是对 AreaRegistration.RegisterAllAreas() 的封装
        /// </summary>
        public static void RegisterAllAreas()
        {
            // 调用 ASP.NET MVC 框架的 Area 自动注册功能
            // 这会自动扫描所有继承自 AreaRegistration 的类并注册它们
            AreaRegistration.RegisterAllAreas();
        }

        /// <summary>
        /// 手动注册特定的 Areas（可选方法）
        /// 如果需要控制注册顺序或添加特殊逻辑，可以使用此方法
        /// </summary>
        public static void RegisterSpecificAreas()
        {
            // 手动注册 HelpPage Area
            var helpPageArea = new Areas.HelpPage.HelpPageAreaRegistration();
            var helpPageContext = new AreaRegistrationContext(helpPageArea.AreaName, RouteTable.Routes);
            helpPageArea.RegisterArea(helpPageContext);

            // 手动注册 BusManagement Area
            var busManagementArea = new Areas.BusManagement.BusManagementAreaRegistration();
            var busManagementContext = new AreaRegistrationContext(busManagementArea.AreaName, RouteTable.Routes);
            busManagementArea.RegisterArea(busManagementContext);
        }

        /// <summary>
        /// 获取已注册的 Area 信息
        /// </summary>
        /// <returns></returns>
        public static string[] GetRegisteredAreas()
        {
            return new string[]
            {
                "HelpPage",
                "BusManagement"
            };
        }

        /// <summary>
        /// 验证 Area 注册状态
        /// </summary>
        /// <param name="areaName">Area 名称</param>
        /// <returns></returns>
        public static bool IsAreaRegistered(string areaName)
        {
            // 这里可以添加验证逻辑
            // 检查路由表中是否包含指定 Area 的路由
            foreach (var route in RouteTable.Routes)
            {
                if (route is Route routeItem)
                {
                    var dataTokens = routeItem.DataTokens;
                    if (dataTokens != null && dataTokens.ContainsKey("area"))
                    {
                        if (dataTokens["area"].ToString().Equals(areaName, System.StringComparison.OrdinalIgnoreCase))
                        {
                            return true;
                        }
                    }
                }
            }
            return false;
        }
    }
}
