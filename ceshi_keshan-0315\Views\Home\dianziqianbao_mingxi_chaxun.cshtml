﻿@{
//Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            日期：<input type="text" name="workday" id="workday" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
            <input id="Button1" type="button" value="查询" />
            &nbsp;<input id="btn" type="submit" value="导出" />
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>



<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            $.getJSON("/api/values/dianziqianbao_mingxi_chaxun?workday=" + $('#workday').val(), function (data) {   //$('#workday').html()  取页面信息值

                var jsonobj = $.parseJSON(data);//转化data的格式
                var htmltable = "";
                htmltable += '<tr><td>区域</td><td>油站名称</td><td>组织机构</td><td>交易日期</td><td>班次</td><td>枪号</td><td>TTC</td><td>付油量</td><td>单价</td><td>金额</td><td>状态</tr>';
                $.each(jsonobj, function (i,item) {
                    htmltable += '<tr><td>' + item.quyu + '</td><td>' + item.OUName + '</td><td>' + item.ShortName + '</td><td>' + item.workday + '</td><td>' + item.checkshift + '</td><td>' + item.gunno + '</td><td>' + item.posttc + '</td><td>' + item.qty + '</td><td>' + item.price + '</td><td>' + item.amount + '</td><td>' + item.OrderStatusTxt + '</tr>';
                });  //循环each   json数组     <tr>行  <td>列
                $('#shuju_1').html(htmltable);
                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/dzqb_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>

