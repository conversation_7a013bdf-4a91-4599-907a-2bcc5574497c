using System;
using System.Web.Mvc;

namespace ceshi_keshan_0315.Areas.BusManagement.Controllers
{
    /// <summary>
    /// 路由测试控制器
    /// 新增模块：用于测试 Area 路由是否正常工作
    /// </summary>
    public class TestController : Controller
    {
        /// <summary>
        /// 路由测试页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewBag.Title = "路由测试页面";
            ViewBag.CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.ControllerName = this.GetType().FullName;
            ViewBag.ActionName = this.ControllerContext.RouteData.Values["action"];
            ViewBag.AreaName = this.ControllerContext.RouteData.DataTokens["area"];
            
            return View();
        }

        /// <summary>
        /// 路由信息 JSON 接口
        /// </summary>
        /// <returns></returns>
        public JsonResult RouteInfo()
        {
            var routeData = this.RouteData;
            var result = new
            {
                Success = true,
                Message = "Area 路由工作正常！",
                RouteInfo = new
                {
                    Area = routeData.DataTokens["area"]?.ToString(),
                    Controller = routeData.Values["controller"]?.ToString(),
                    Action = routeData.Values["action"]?.ToString(),
                    Namespace = this.GetType().Namespace,
                    FullControllerName = this.GetType().FullName,
                    RequestUrl = Request.Url?.ToString(),
                    RequestPath = Request.Url?.AbsolutePath,
                    CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                },
                AvailableRoutes = new[]
                {
                    "/BusManagement/Test/Index",
                    "/BusManagement/Test/RouteInfo",
                    "/BusManagement/Bus/Index",
                    "/BusManagement/Application/Index"
                }
            };

            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 测试参数传递
        /// </summary>
        /// <param name="id">测试ID</param>
        /// <param name="name">测试名称</param>
        /// <returns></returns>
        public ActionResult TestParams(int? id, string name)
        {
            ViewBag.Title = "参数测试页面";
            ViewBag.TestId = id ?? 0;
            ViewBag.TestName = name ?? "未提供";
            
            return View("Index"); // 复用 Index 视图
        }
    }
}
