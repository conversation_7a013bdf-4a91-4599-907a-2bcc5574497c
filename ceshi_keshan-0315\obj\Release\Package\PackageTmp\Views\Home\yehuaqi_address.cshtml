﻿
@{
    ViewBag.Title = "代客下单液化气订单地址同步";
}

<h2>代客下单液化气订单地址同步</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div class="pull-left">
                网络订单号：<input type="text" name="billno" id="billno" value="" size="20" />


                &nbsp;<input id="btn_tongbu" type="submit" value="地址和联系方式-同步" />
            </div>
        </form>

    </div>
</body>
</html>

<script>
    //$(document).ready(function)   当 DOM（document object model 文档对象模型）加载完毕且页面完全加载（包括图像）时发生 ready 事件
    $(document).ready(function () {
        

        $('#btn_tongbu').click(function () {
            //$.getJSON("/api/values/HX_tuangoudan_tongbu?billno=" + $('#billno').val(), function (data) { }
            $("#form1").attr("action", "/Home/APP_yyhq_address_Exta");//根据促发表单，把action 替换成后面的路劲
            //这个是同步的功能

            $('#btn_tongbu').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

        
    });
</script>
