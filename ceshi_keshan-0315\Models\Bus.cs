using System;
using System.ComponentModel.DataAnnotations;
using FreeSql.DataAnnotations;

namespace ceshi_keshan_0315.Models
{
    /// <summary>
    /// 公车信息实体
    /// 新增模块：公车基础信息管理
    /// </summary>
    [Table(Name = "T_Bus")]
    public class Bus
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Column(IsPrimary = true, IsIdentity = true)]
        public int Id { get; set; }

        /// <summary>
        /// 车牌号
        /// </summary>
        [Column(StringLength = 20, IsNullable = false)]
        [Required(ErrorMessage = "车牌号不能为空")]
        [Display(Name = "车牌号")]
        public string PlateNumber { get; set; }

        /// <summary>
        /// 车型
        /// </summary>
        [Column(StringLength = 50)]
        [Display(Name = "车型")]
        public string Model { get; set; }

        /// <summary>
        /// 车辆品牌
        /// </summary>
        [Column(StringLength = 30)]
        [Display(Name = "品牌")]
        public string Brand { get; set; }

        /// <summary>
        /// 车辆状态
        /// </summary>
        [Column]
        [Display(Name = "状态")]
        public BusStatus Status { get; set; }

        /// <summary>
        /// 购买日期
        /// </summary>
        [Column]
        [Display(Name = "购买日期")]
        [DataType(DataType.Date)]
        public DateTime PurchaseDate { get; set; }

        /// <summary>
        /// 座位数
        /// </summary>
        [Column]
        [Display(Name = "座位数")]
        [Range(1, 50, ErrorMessage = "座位数必须在1-50之间")]
        public int SeatCount { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [Column(StringLength = 500)]
        [Display(Name = "备注")]
        public string Remarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [Column]
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        [Column]
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 是否删除
        /// </summary>
        [Column]
        public bool IsDeleted { get; set; } = false;
    }

    /// <summary>
    /// 公车状态枚举
    /// </summary>
    public enum BusStatus
    {
        /// <summary>
        /// 可用
        /// </summary>
        [Display(Name = "可用")]
        Available = 1,

        /// <summary>
        /// 使用中
        /// </summary>
        [Display(Name = "使用中")]
        InUse = 2,

        /// <summary>
        /// 维修中
        /// </summary>
        [Display(Name = "维修中")]
        Maintenance = 3,

        /// <summary>
        /// 已报废
        /// </summary>
        [Display(Name = "已报废")]
        Retired = 4
    }
}
