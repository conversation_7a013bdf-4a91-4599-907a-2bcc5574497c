using System;
using System.Configuration;
using FreeSql;

namespace ceshi_keshan_0315.App_Start
{
    /// <summary>
    /// 公车管理模块 FreeSql 配置
    /// 新增模块：支持多数据库切换（SQL Server/Oracle/MySQL）
    /// </summary>
    public static class BusFreeSqlConfig
    {
        private static IFreeSql _freeSql;
        private static readonly object _lock = new object();

        /// <summary>
        /// FreeSql 实例（单例模式）
        /// </summary>
        public static IFreeSql Instance
        {
            get
            {
                if (_freeSql == null)
                {
                    lock (_lock)
                    {
                        if (_freeSql == null)
                        {
                            _freeSql = CreateFreeSql();
                        }
                    }
                }
                return _freeSql;
            }
        }

        /// <summary>
        /// 创建 FreeSql 实例
        /// </summary>
        /// <returns></returns>
        public static IFreeSql CreateFreeSql()
        {
            var dbType = ConfigurationManager.AppSettings["BusManagement:MainDB"] ?? "BusManagementDB";
            var connStr = ConfigurationManager.ConnectionStrings[dbType]?.ConnectionString;
            
            if (string.IsNullOrEmpty(connStr))
            {
                throw new ConfigurationErrorsException($"未找到数据库连接字符串: {dbType}");
            }

            return new FreeSql.FreeSqlBuilder()
                .UseConnectionString(GetDbType(dbType), connStr)
                .UseAutoSyncStructure(false) // 关闭自动迁移，生产环境安全
                .UseNoneCommandParameter(true) // 不使用参数化，兼容复杂查询
                .Build();
        }

        /// <summary>
        /// 根据配置获取数据库类型
        /// </summary>
        /// <param name="type">数据库类型配置</param>
        /// <returns></returns>
        private static DataType GetDbType(string type)
        {
            return type.ToLower() switch
            {
                var t when t.Contains("sqlserver") || t.Contains("mssql") => DataType.SqlServer,
                var t when t.Contains("oracle") => DataType.Oracle,
                var t when t.Contains("mysql") => DataType.MySql,
                _ => DataType.SqlServer // 默认使用 SQL Server
            };
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public static void Dispose()
        {
            _freeSql?.Dispose();
            _freeSql = null;
        }
    }
}
