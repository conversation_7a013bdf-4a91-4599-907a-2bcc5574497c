using System;
using System.Configuration;
// using FreeSql; // 暂时注释，避免编译错误

namespace ceshi_keshan_0315.App_Start
{
    /// <summary>
    /// 公车管理模块数据库配置
    /// 新增模块：支持多数据库切换（SQL Server/Oracle/MySQL）
    /// 注意：当前为简化版本，避免 FreeSql 依赖问题
    /// </summary>
    public static class BusFreeSqlConfig
    {
        // 简化版本，移除了 FreeSql 相关代码

        /// <summary>
        /// 获取数据库连接字符串
        /// </summary>
        public static string GetConnectionString()
        {
            var dbType = ConfigurationManager.AppSettings["BusManagement:MainDB"] ?? "BusManagementDB";
            var connStr = ConfigurationManager.ConnectionStrings[dbType]?.ConnectionString;

            if (string.IsNullOrEmpty(connStr))
            {
                // 返回默认连接字符串，避免异常
                return "Data Source=.;Initial Catalog=BusManagement;Integrated Security=true;";
            }

            return connStr;
        }

        /// <summary>
        /// 获取数据库类型字符串
        /// </summary>
        /// <param name="type">数据库类型配置</param>
        /// <returns></returns>
        public static string GetDbTypeString(string type)
        {
            var lowerType = type.ToLower();

            if (lowerType.Contains("sqlserver") || lowerType.Contains("mssql"))
            {
                return "SqlServer";
            }
            else if (lowerType.Contains("oracle"))
            {
                return "Oracle";
            }
            else if (lowerType.Contains("mysql"))
            {
                return "MySql";
            }
            else
            {
                return "SqlServer"; // 默认使用 SQL Server
            }
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns></returns>
        public static bool TestConnection()
        {
            try
            {
                var connStr = GetConnectionString();
                // 这里可以添加实际的数据库连接测试逻辑
                return !string.IsNullOrEmpty(connStr);
            }
            catch
            {
                return false;
            }
        }
    }
}
