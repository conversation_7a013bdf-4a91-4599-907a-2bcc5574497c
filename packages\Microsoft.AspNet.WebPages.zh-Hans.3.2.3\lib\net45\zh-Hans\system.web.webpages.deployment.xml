﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.WebPages.Deployment</name>
  </assembly>
  <members>
    <member name="T:System.Web.WebPages.Deployment.PreApplicationStartCode">
      <summary>为 Web Pages 部署应用程序预启动代码提供注册点。</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.PreApplicationStartCode.Start">
      <summary>注册 Web Pages 部署应用程序预启动代码。</summary>
    </member>
    <member name="T:System.Web.WebPages.Deployment.WebPagesDeployment">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。提供用于获取有关 Web 应用程序的部署信息的方法。</summary>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetAssemblyPath(System.Version)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Web Pages 部署的程序集路径。</summary>
      <returns>Web Pages 部署的程序集路径。</returns>
      <param name="version">Web Pages 版本。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetExplicitWebPagesVersion(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从给定的二进制路径获取 Web Pages 版本。</summary>
      <returns>Web Pages 版本。</returns>
      <param name="path">Web Pages 的二进制路径。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetIncompatibleDependencies(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。从给定的路径获取程序集引用，而不管 Web Pages 版本为何。</summary>
      <returns>包含 Web Pages 及其版本的程序集引用的字典。</returns>
      <param name="appPath">Web Pages 应用程序的路径。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetMaxVersion">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。获取 Web Pages 加载的程序集的最大版本。</summary>
      <returns>Web Pages 加载的程序集的最大版本。</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersion(System.String)">
      <summary>从给定的路径获取 Web Pages 版本。</summary>
      <returns>Web Pages 版本。</returns>
      <param name="path">应用程序根目录的路径。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetVersionWithoutEnabledCheck(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。使用具有指定路径的配置设置获取 Web Pages 版本。</summary>
      <returns>Web Pages 版本。</returns>
      <param name="path">应用程序设置的路径。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.GetWebPagesAssemblies">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。返回 Web Pages 部署的程序集。</summary>
      <returns>包含此 Web Pages 部署的程序集的列表。</returns>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsEnabled(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示是否启用 Web Pages 部署。</summary>
      <returns>如果启用了 Web Pages 部署，则为 true；否则为 false。</returns>
      <param name="path">Web Pages 部署的路径。</param>
    </member>
    <member name="M:System.Web.WebPages.Deployment.WebPagesDeployment.IsExplicitlyDisabled(System.String)">
      <summary>此类型/成员支持 .NET Framework 基础结构，不能在代码中直接使用。指示是否显式禁用 Web Pages 部署。</summary>
      <returns>如果显式禁用了 Web Pages 部署，则为 true；否则为 false。</returns>
      <param name="path">Web Pages 部署的路径。</param>
    </member>
  </members>
</doc>