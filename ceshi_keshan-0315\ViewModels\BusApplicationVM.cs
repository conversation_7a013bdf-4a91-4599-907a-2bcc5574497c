using System;
using System.ComponentModel.DataAnnotations;
using ceshi_keshan_0315.Models;

namespace ceshi_keshan_0315.ViewModels
{
    /// <summary>
    /// 公车申请视图模型
    /// 新增模块：用于前端展示和数据传输
    /// </summary>
    public class BusApplicationVM
    {
        public int Id { get; set; }

        [Display(Name = "车牌号")]
        public string PlateNumber { get; set; }

        [Display(Name = "车型")]
        public string Model { get; set; }

        [Display(Name = "申请人")]
        public string ApplicantName { get; set; }

        [Display(Name = "申请部门")]
        public string Department { get; set; }

        [Display(Name = "联系电话")]
        public string Phone { get; set; }

        [Display(Name = "用车目的")]
        public string Purpose { get; set; }

        [Display(Name = "目的地")]
        public string Destination { get; set; }

        [Display(Name = "开始时间")]
        public DateTime StartTime { get; set; }

        [Display(Name = "结束时间")]
        public DateTime EndTime { get; set; }

        [Display(Name = "预计人数")]
        public int EstimatedPeople { get; set; }

        [Display(Name = "申请状态")]
        public string StatusText { get; set; }

        public ApplicationStatus Status { get; set; }

        [Display(Name = "审批人")]
        public string ReviewerName { get; set; }

        [Display(Name = "审批时间")]
        public DateTime? ReviewTime { get; set; }

        [Display(Name = "审批意见")]
        public string ReviewComments { get; set; }

        [Display(Name = "申请时间")]
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 用车时长（小时）
        /// </summary>
        [Display(Name = "用车时长")]
        public double Duration => (EndTime - StartTime).TotalHours;

        /// <summary>
        /// 是否可以编辑
        /// </summary>
        public bool CanEdit => Status == ApplicationStatus.Pending;

        /// <summary>
        /// 是否可以取消
        /// </summary>
        public bool CanCancel => Status == ApplicationStatus.Pending || Status == ApplicationStatus.Approved;
    }

    /// <summary>
    /// 公车申请查询条件视图模型
    /// </summary>
    public class BusApplicationQueryVM
    {
        [Display(Name = "车牌号")]
        public string PlateNumber { get; set; }

        [Display(Name = "申请人")]
        public string ApplicantName { get; set; }

        [Display(Name = "申请部门")]
        public string Department { get; set; }

        [Display(Name = "申请状态")]
        public ApplicationStatus? Status { get; set; }

        [Display(Name = "开始日期")]
        [DataType(DataType.Date)]
        public DateTime? StartDate { get; set; }

        [Display(Name = "结束日期")]
        [DataType(DataType.Date)]
        public DateTime? EndDate { get; set; }

        [Display(Name = "页码")]
        public int PageIndex { get; set; } = 1;

        [Display(Name = "每页条数")]
        public int PageSize { get; set; } = 20;

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);

        /// <summary>
        /// 是否有上一页
        /// </summary>
        public bool HasPreviousPage => PageIndex > 1;

        /// <summary>
        /// 是否有下一页
        /// </summary>
        public bool HasNextPage => PageIndex < TotalPages;
    }

    /// <summary>
    /// 公车视图模型
    /// </summary>
    public class BusVM
    {
        public int Id { get; set; }

        [Display(Name = "车牌号")]
        public string PlateNumber { get; set; }

        [Display(Name = "车型")]
        public string Model { get; set; }

        [Display(Name = "品牌")]
        public string Brand { get; set; }

        [Display(Name = "状态")]
        public string StatusText { get; set; }

        public BusStatus Status { get; set; }

        [Display(Name = "座位数")]
        public int SeatCount { get; set; }

        [Display(Name = "购买日期")]
        [DataType(DataType.Date)]
        public DateTime PurchaseDate { get; set; }

        [Display(Name = "备注")]
        public string Remarks { get; set; }

        /// <summary>
        /// 是否可用
        /// </summary>
        public bool IsAvailable => Status == BusStatus.Available;

        /// <summary>
        /// 使用年限
        /// </summary>
        [Display(Name = "使用年限")]
        public int UsageYears => DateTime.Now.Year - PurchaseDate.Year;
    }

    /// <summary>
    /// 审批操作视图模型
    /// </summary>
    public class ReviewActionVM
    {
        [Required(ErrorMessage = "申请ID不能为空")]
        public int ApplicationId { get; set; }

        [Required(ErrorMessage = "操作类型不能为空")]
        public ApplicationStatus ActionType { get; set; }

        [Display(Name = "审批意见")]
        [StringLength(500, ErrorMessage = "审批意见不能超过500个字符")]
        public string Comments { get; set; }
    }
}
