﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_sqyyjxs" id="workday_sqyyjxs" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_dqyyjxs" id="workday_dqyyjxs" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;（备注：日期 大于等于前面日期   小于后面日期）
                油品编码<input type="text" name="oilno" id="oilno" size="15" />(60000359,60000359)
            </div>
            <br>
            <div>
                <input id="Button1" type="button" value="夜间销售查询" />
                &nbsp;<input id="btn" type="submit" value="夜间销售导出" />
            </div>
            <div class="pull-right">
                <input id="btn_dtcx" type="button" value="打桶销售查询" />
                &nbsp;<input id="btn_dtdc" type="submit" value="打桶销售导出" />
            </div>
        </form>
         

        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/lingshou_quyu_yejianxiaoshou?workday_sqyyjxs=" + $('#workday_sqyyjxs').val() + "&workday_dqyyjxs=" + $('#workday_dqyyjxs').val() + "&oilno=" + $('#oilno').val();
            $.getJSON("/api/values/lingshou_quyu_yejianxiaoshou?workday_sqyyjxs=" + $('#workday_sqyyjxs').val() + "&workday_dqyyjxs=" + $('#workday_dqyyjxs').val() + "&oilno=" + $('#oilno').val()
                , function (data) {   //$('#workday').html()  取页面信息值
                    //$('#div1').html(data);
                    if (data.length > 0) {
                        var jsonobj = $.parseJSON(data);//转化data的格式
                        var htmltable = "";
                        htmltable += '<tr><td>区域名称</td><td>油站名称</td><td>编码</td><td>油品编码</td><td>提枪数</td><td>销售量</td><td>销售金额</td></tr>';
                        $.each(jsonobj, function (i, item) {
                            htmltable += '<tr><td>' + item.quyu + '</td><td>' + item.yzmc + '</td><td>' + item.nodeno + '</td><td>' + item.oilno + '</td><td>' + item.tqs + '</td><td>' + item.xsl + '</td><td>' + item.xsje + '</td></tr>';
                        });  //循环each   json数组     <tr>行  <td>列
                        $('#shuju_1').html(htmltable);
                    } else { alert("查询为空"); alert(workday_sqyyjxs);}

                    // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                    // $('#div1').html(data);    //getJSON 是有数据返回的使用

                    //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
                })
        });

        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/lingshou_quyu_yejianxiaoshou_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

        $('#btn_dtcx').click(function () {
            var test = "/api/values/lingshou_quyu_datongxiaoshou_chaxun?workday_sqyyjxs=" + $('#workday_sqyyjxs').val() + "&workday_dqyyjxs=" + $('#workday_dqyyjxs').val() + "&oilno=" + $('#oilno').val();
            $.getJSON("/api/values/lingshou_quyu_datongxiaoshou_chaxun?workday_sqyyjxs=" + $('#workday_sqyyjxs').val() + "&workday_dqyyjxs=" + $('#workday_dqyyjxs').val() + "&oilno=" + $('#oilno').val()
                , function (data) {   //$('#workday').html()  取页面信息值
                    //$('#div1').html(data);
                    if (data.length > 0) {
                        var jsonobj = $.parseJSON(data);//转化data的格式
                        var htmltable = "";
                        htmltable += '<tr><td>区域名称</td><td>油站名称</td><td>编码</td><td>油品编码</td><td>提枪数</td><td>销售量</td><td>销售金额</td></tr>';
                        $.each(jsonobj, function (i, item) {
                            htmltable += '<tr><td>' + item.quyu + '</td><td>' + item.yzmc + '</td><td>' + item.nodeno + '</td><td>' + item.oilno + '</td><td>' + item.tqs + '</td><td>' + item.xsl + '</td><td>' + item.xsje + '</td></tr>';
                        });  //循环each   json数组     <tr>行  <td>列
                        $('#shuju_1').html(htmltable);
                    } else { alert("查询为空"); alert(workday_sqyyjxs); }

                    // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                    // $('#div1').html(data);    //getJSON 是有数据返回的使用

                    //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
                })
        });
         
        $('#btn_dtdc').click(function () {
            $("#form1").attr("action", "/Home/lingshou_quyu_datongxiaoshou_chaxun_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

    });
</script>
