@{
    ViewBag.Title = "创建公车申请";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- 新增模块：公车申请创建页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">填写用车申请信息，提交后等待审批</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">申请信息</h4>
                </div>
                <div class="panel-body">
                    <form id="applicationForm" class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请人 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="applicantName" name="applicantName" 
                                       placeholder="请输入申请人姓名" required />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请部门</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="department" name="department" 
                                       placeholder="请输入申请部门" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话</label>
                            <div class="col-sm-9">
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       placeholder="请输入联系电话" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">选择车辆 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <select class="form-control" id="busId" name="busId" required>
                                    <option value="">请选择车辆</option>
                                </select>
                                <p class="help-block">只显示当前可用的车辆</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">用车目的 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <textarea class="form-control" id="purpose" name="purpose" rows="3" 
                                          placeholder="请详细说明用车目的" required></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">目的地</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" id="destination" name="destination" 
                                       placeholder="请输入目的地" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">开始时间 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control Wdate" id="startTime" name="startTime" 
                                       onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m',onpicked:checkTimeConflict})" 
                                       placeholder="选择开始时间" readonly required />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">结束时间 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control Wdate" id="endTime" name="endTime" 
                                       onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',minDate:'%y-%M-%d %H:%m',onpicked:checkTimeConflict})" 
                                       placeholder="选择结束时间" readonly required />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">预计人数</label>
                            <div class="col-sm-9">
                                <input type="number" class="form-control" id="estimatedPeople" name="estimatedPeople" 
                                       min="1" max="50" value="1" />
                                <p class="help-block">请根据车辆座位数合理填写</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <div id="conflictAlert" class="alert alert-warning" style="display: none;">
                                    <strong>提示：</strong> <span id="conflictMessage"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="glyphicon glyphicon-ok"></i> 提交申请
                                </button>
                                <a href="@Url.Action("Index", "Application")" class="btn btn-default">
                                    <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <script>
        $(document).ready(function() {
            // 加载可用车辆
            loadAvailableBuses();

            // 表单提交
            $('#applicationForm').on('submit', function(e) {
                e.preventDefault();
                submitApplication();
            });

            // 车辆选择变化时检查冲突
            $('#busId').on('change', function() {
                checkTimeConflict();
            });
        });

        // 加载可用车辆
        function loadAvailableBuses() {
            $.get('/BusManagement/api/Bus/GetAvailableBuses', function(response) {
                if (response.success) {
                    var $select = $('#busId');
                    $select.empty().append('<option value="">请选择车辆</option>');
                    
                    $.each(response.data, function(i, bus) {
                        $select.append('<option value="' + bus.Id + '">' + bus.DisplayText + '</option>');
                    });
                } else {
                    alert('加载车辆列表失败：' + response.message);
                }
            }).fail(function() {
                alert('加载车辆列表失败，请刷新页面重试');
            });
        }

        // 检查时间冲突
        function checkTimeConflict() {
            var busId = $('#busId').val();
            var startTime = $('#startTime').val();
            var endTime = $('#endTime').val();

            if (!busId || !startTime || !endTime) {
                $('#conflictAlert').hide();
                return;
            }

            if (new Date(startTime) >= new Date(endTime)) {
                showConflictAlert('结束时间必须大于开始时间');
                return;
            }

            $.get('/BusManagement/api/Bus/CheckAvailability', {
                busId: busId,
                startTime: startTime,
                endTime: endTime
            }, function(response) {
                if (response.success) {
                    $('#conflictAlert').hide();
                    $('#submitBtn').prop('disabled', false);
                } else {
                    showConflictAlert(response.message);
                    $('#submitBtn').prop('disabled', true);
                }
            }).fail(function() {
                showConflictAlert('检查车辆可用性失败');
            });
        }

        // 显示冲突提示
        function showConflictAlert(message) {
            $('#conflictMessage').text(message);
            $('#conflictAlert').show();
        }

        // 提交申请
        function submitApplication() {
            var formData = {
                ApplicantName: $('#applicantName').val(),
                Department: $('#department').val(),
                Phone: $('#phone').val(),
                BusId: parseInt($('#busId').val()),
                Purpose: $('#purpose').val(),
                Destination: $('#destination').val(),
                StartTime: $('#startTime').val(),
                EndTime: $('#endTime').val(),
                EstimatedPeople: parseInt($('#estimatedPeople').val()) || 1
            };

            // 基本验证
            if (!formData.ApplicantName) {
                alert('请输入申请人姓名');
                return;
            }

            if (!formData.BusId) {
                alert('请选择车辆');
                return;
            }

            if (!formData.Purpose) {
                alert('请输入用车目的');
                return;
            }

            if (!formData.StartTime || !formData.EndTime) {
                alert('请选择用车时间');
                return;
            }

            $('#submitBtn').prop('disabled', true).html('<i class="glyphicon glyphicon-refresh glyphicon-refresh-animate"></i> 提交中...');

            $.ajax({
                url: '/BusManagement/api/Application/Create',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(formData),
                success: function(response) {
                    if (response.success) {
                        alert('申请提交成功！申请ID：' + response.id);
                        window.location.href = '@Url.Action("Index", "Application")';
                    } else {
                        alert('申请提交失败：' + response.message);
                        $('#submitBtn').prop('disabled', false).html('<i class="glyphicon glyphicon-ok"></i> 提交申请');
                    }
                },
                error: function() {
                    alert('申请提交失败，请重试');
                    $('#submitBtn').prop('disabled', false).html('<i class="glyphicon glyphicon-ok"></i> 提交申请');
                }
            });
        }
    </script>
    <style>
        .glyphicon-refresh-animate {
            animation: spin .7s infinite linear;
        }
        
        @keyframes spin {
            from { transform: scale(1) rotate(0deg); }
            to { transform: scale(1) rotate(360deg); }
        }
    </style>
}
