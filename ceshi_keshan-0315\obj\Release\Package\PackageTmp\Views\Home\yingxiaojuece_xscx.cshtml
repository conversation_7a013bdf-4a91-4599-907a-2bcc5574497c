﻿@{
//Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_yxjcxscx" id="workday_yxjcxscx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>


<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/yingxiaojuece_xscx?workday_yxjcxscx=" + $('#workday_yxjcxscx').val();
            $.getJSON("/api/values/yingxiaojuece_xscx?workday_yxjcxscx=" + $('#workday_yxjcxscx').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                      if (data.length > 0)
                      {
                          var jsonobj = $.parseJSON(data);//转化data的格式
                          var htmltable = "";
                          htmltable += '<tr><td>名称</td><td>日销售总数</tr>';
                          $.each(jsonobj, function (i, item) {
                              htmltable += '<tr><td>' + item.kkb + '</td><td>' + item.yxje_xl + '</tr>';
                          });  //循环each   json数组     <tr>行  <td>列
                          $('#shuju_1').html(htmltable);
                      } else
                      { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/.");//根据促发表单，把action 替换成后面的路劲  Home里面的方法
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>