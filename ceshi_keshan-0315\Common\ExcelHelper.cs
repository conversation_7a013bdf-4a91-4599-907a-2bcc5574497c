﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using NPOI.SS.UserModel;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using System.Data;
namespace ceshi_keshan_0315.Common
{
    public class ExcelHelper
    {
        /// <summary>
        /// 将DataSet数据集转换HSSFworkbook对象，并保存为Stream流
        /// </summary>
        /// <param name="ds"></param>
        /// <returns>返回数据流Stream对象</returns>
        public static MemoryStream ExportDatasetToExcel(DataSet ds)
        {
            try
            {
                //文件流对象
                //FileStream file = new FileStream(fileName, FileMode.OpenOrCreate, FileAccess.ReadWrite);
                MemoryStream stream = new MemoryStream();

                //打开Excel对象
                HSSFWorkbook workbook = new HSSFWorkbook();
                foreach (DataTable dt in ds.Tables)
                {
                    //Excel的Sheet对象
                    NPOI.SS.UserModel.ISheet sheet = workbook.CreateSheet(dt.TableName);

                    //set date format
                    ICellStyle cellStyleDate = workbook.CreateCellStyle();
                    IDataFormat format = workbook.CreateDataFormat();
                    cellStyleDate.DataFormat = format.GetFormat("yyyy年m月d日");

                    //使用NPOI操作Excel表
                    NPOI.SS.UserModel.IRow row = sheet.CreateRow(0);
                    int count = 0;
                    for (int i = 0; i < dt.Columns.Count; i++) //生成sheet第一行列名 
                    {
                        NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                        cell.SetCellValue(dt.Columns[i].Caption);
                        cell.CellStyle.FillBackgroundColor = 48;
                    }
                    int sheetNum = 0;
                    int tempIndex = 1;
                    //将数据导入到excel表中
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        NPOI.SS.UserModel.IRow rows = sheet.CreateRow(tempIndex);
                        count = 0;
                        for (int j = 0; j < dt.Columns.Count; j++)
                        {
                            NPOI.SS.UserModel.ICell cell = rows.CreateCell(count++);
                            Type type = dt.Rows[i][j].GetType();
                            if (type == typeof(int) || type == typeof(Int16)
                                || type == typeof(Int32) || type == typeof(Int64))
                            {
                                cell.SetCellValue((int)dt.Rows[i][j]);
                            }
                            else
                            {
                                if (type == typeof(float) || type == typeof(double) || type == typeof(Double))
                                {
                                    cell.SetCellValue((Double)dt.Rows[i][j]);
                                }
                                else
                                {
                                    if (type == typeof(DateTime))
                                    {
                                        cell.SetCellValue(((DateTime)dt.Rows[i][j]).ToString("yyyy-MM-dd HH:mm:ss"));
                                    }
                                    else
                                    {
                                        if (type == typeof(bool) || type == typeof(Boolean))
                                        {
                                            cell.SetCellValue((bool)dt.Rows[i][j]);
                                        }
                                        else
                                        {
                                            cell.SetCellValue(dt.Rows[i][j].ToString());
                                        }
                                    }
                                }
                            }
                        }
                        //2018-09-07 Mod 数据量过大时自动分sheet页
                        if (tempIndex == 50000)
                        {
                            sheetNum++;
                            sheet = workbook.CreateSheet(dt.TableName + "_" + sheetNum);
                            tempIndex = 0;
                            row = sheet.CreateRow(0);
                            count = 0;
                            for (int m = 0; m < dt.Columns.Count; m++) //生成sheet第一行列名 
                            {
                                NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                                cell.SetCellValue(dt.Columns[m].Caption);
                                cell.CellStyle.FillBackgroundColor = 48;
                            }
                        }
                        tempIndex++;
                    }

                    //保存excel文档
                    sheet.ForceFormulaRecalculation = true;
                }
                workbook.Write(stream);
                workbook.Close();

                return stream;
            }
            catch
            {
                return new MemoryStream();
            }
        }

        /// <summary>
        /// 将DataSet数据集转换XSSFWorkbook对象，并保存为Stream流
        /// </summary>
        /// <param name="ds"></param>
        /// <returns></returns>
        public static MemoryStream ExportDatasetToXlsx(DataSet ds)
        {
            try
            {
                //文件流对象
                MemoryStream stream = new MemoryStream();

                //打开Excel对象
                XSSFWorkbook workbook = new XSSFWorkbook();
                foreach (DataTable dt in ds.Tables)
                {
                    //Excel的Sheet对象
                    NPOI.SS.UserModel.ISheet sheet = workbook.CreateSheet(dt.TableName);

                    //set date format
                    ICellStyle cellStyleDate = workbook.CreateCellStyle();
                    IDataFormat format = workbook.CreateDataFormat();
                    cellStyleDate.DataFormat = format.GetFormat("yyyy/m/d");

                    //使用NPOI操作Excel表
                    NPOI.SS.UserModel.IRow row = sheet.CreateRow(0);
                    int count = 0;
                    for (int i = 0; i < dt.Columns.Count; i++) //生成sheet第一行列名 
                    {
                        NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                        cell.SetCellValue(dt.Columns[i].Caption);
                        cell.CellStyle.FillBackgroundColor = (short)48;
                    }
                    int sheetNum = 0;
                    int tempIndex = 1;
                    //将数据导入到excel表中
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        NPOI.SS.UserModel.IRow rows = sheet.CreateRow(tempIndex);
                        count = 0;
                        for (int j = 0; j < dt.Columns.Count; j++)
                        {
                            NPOI.SS.UserModel.ICell cell = rows.CreateCell(count++);
                            Type type = dt.Rows[i][j].GetType();
                            if (type == typeof(int) || type == typeof(Int16)
                                || type == typeof(Int32) || type == typeof(Int64))
                            {
                                cell.SetCellValue((int)dt.Rows[i][j]);
                            }
                            else
                            {
                                if (type == typeof(float) || type == typeof(double) || type == typeof(Double))
                                {
                                    cell.SetCellValue((Double)dt.Rows[i][j]);
                                }
                                else
                                {
                                    if (type == typeof(DateTime))
                                    {
                                        cell.SetCellValue(((DateTime)dt.Rows[i][j]).Date);
                                        cell.CellStyle = cellStyleDate;
                                    }
                                    else
                                    {
                                        if (type == typeof(bool) || type == typeof(Boolean))
                                        {
                                            cell.SetCellValue((bool)dt.Rows[i][j]);
                                        }
                                        else
                                        {
                                            cell.SetCellValue(dt.Rows[i][j].ToString());
                                        }
                                    }
                                }
                            }
                        }
                        //2018-09-07 Mod 数据量过大时自动分sheet页
                        if (tempIndex == 50000)
                        {
                            sheetNum++;
                            sheet = workbook.CreateSheet(dt.TableName + "_" + sheetNum);
                            tempIndex = 0;
                            row = sheet.CreateRow(0);
                            count = 0;
                            for (int m = 0; m < dt.Columns.Count; m++) //生成sheet第一行列名 
                            {
                                NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                                cell.SetCellValue(dt.Columns[m].Caption);
                                cell.CellStyle.FillBackgroundColor = (short)48;
                            }
                        }
                        tempIndex++;
                    }

                    //保存excel文档
                    sheet.ForceFormulaRecalculation = true;
                }
                //workbook.Write(stream);
                stream = WriteStream(workbook);
                workbook.Close();

                return stream;
            }
            catch (Exception ex)
            {
                string ss = ex.Message;
                return new MemoryStream();
            }
        }

        //将XSSFWorkbook转换为Stream流
        public static MemoryStream WriteStream(XSSFWorkbook workbook)
        {
            string filename = "//temp//" + System.Guid.NewGuid().ToString() + ".xlsx";
            string filePath = System.Web.HttpContext.Current.Server.MapPath(filename);
            FileStream fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);

            workbook.Write(fileStream);//调用这个后会关于文件流，在HSSFWorkbook不会关闭所以在处理时应注意
            FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            long fileSize = fs.Length;
            byte[] fileBuffer = new byte[fileSize];
            fs.Read(fileBuffer, 0, (int)fileSize);
            fs.Close();
            MemoryStream stream = new MemoryStream(fileBuffer);
            return stream;
        }

        /// <summary>
        /// 将DataSet数据集转换HSSFworkbook对象，并在指定路径生成文件
        /// </summary>
        /// <param name="ds"></param>
        /// <param name="filepath"></param>
        /// <returns></returns>
        public static void ExportExcelToDisk(DataSet ds, string filePath)
        {
            try
            {
                //文件流对象
                MemoryStream stream = new MemoryStream();

                //打开Excel对象
                XSSFWorkbook workbook = new XSSFWorkbook();
                foreach (DataTable dt in ds.Tables)
                {
                    //Excel的Sheet对象
                    NPOI.SS.UserModel.ISheet sheet = workbook.CreateSheet(dt.TableName);

                    //set date format
                    ICellStyle cellStyleDate = workbook.CreateCellStyle();
                    IDataFormat format = workbook.CreateDataFormat();
                    cellStyleDate.DataFormat = format.GetFormat("yyyy/m/d");

                    //使用NPOI操作Excel表
                    NPOI.SS.UserModel.IRow row = sheet.CreateRow(0);
                    int count = 0;
                    for (int i = 0; i < dt.Columns.Count; i++) //生成sheet第一行列名 
                    {
                        NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                        cell.SetCellValue(dt.Columns[i].Caption);
                        cell.CellStyle.FillBackgroundColor = (short)48;
                    }
                    int sheetNum = 0;
                    int tempIndex = 1;
                    //将数据导入到excel表中
                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        NPOI.SS.UserModel.IRow rows = sheet.CreateRow(tempIndex);
                        count = 0;
                        for (int j = 0; j < dt.Columns.Count; j++)
                        {
                            NPOI.SS.UserModel.ICell cell = rows.CreateCell(count++);
                            Type type = dt.Rows[i][j].GetType();
                            if (type == typeof(int) || type == typeof(Int16)
                                || type == typeof(Int32) || type == typeof(Int64))
                            {
                                cell.SetCellValue((int)dt.Rows[i][j]);
                            }
                            else
                            {
                                if (type == typeof(float) || type == typeof(double) || type == typeof(Double))
                                {
                                    cell.SetCellValue((Double)dt.Rows[i][j]);
                                }
                                else
                                {
                                    if (type == typeof(DateTime))
                                    {
                                        cell.SetCellValue(((DateTime)dt.Rows[i][j]).Date);
                                        cell.CellStyle = cellStyleDate;
                                    }
                                    else
                                    {
                                        if (type == typeof(bool) || type == typeof(Boolean))
                                        {
                                            cell.SetCellValue((bool)dt.Rows[i][j]);
                                        }
                                        else
                                        {
                                            cell.SetCellValue(dt.Rows[i][j].ToString());
                                        }
                                    }
                                }
                            }
                        }
                        //2018-09-07 Mod 数据量过大时自动分sheet页
                        if (tempIndex == 50000)
                        {
                            sheetNum++;
                            sheet = workbook.CreateSheet(dt.TableName + "_" + sheetNum);
                            tempIndex = 0;
                            row = sheet.CreateRow(0);
                            count = 0;
                            for (int m = 0; m < dt.Columns.Count; m++) //生成sheet第一行列名 
                            {
                                NPOI.SS.UserModel.ICell cell = row.CreateCell(count++);
                                cell.SetCellValue(dt.Columns[m].Caption);
                                cell.CellStyle.FillBackgroundColor = (short)48;
                            }
                        }
                        tempIndex++;
                    }

                    //保存excel文档
                    sheet.ForceFormulaRecalculation = true;
                }

                FileStream fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
                workbook.Write(fileStream);
                workbook.Close();

            }
            catch (Exception ex)
            {
                string ss = ex.Message;
            }
        }
    }
}
