﻿
@{
    ViewBag.Title = "优惠平衡点查询计算";
}



<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <link rel="stylesheet" href="styles.css">

    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title>优惠数据管理</title>
</head>
<body>
    <header>
        <h2>优惠平衡点查询计算</h2>
    </header>
    <form method="post" id="form1">
        <div class="form-group">
            <label for="youpin_name">油品名称：</label>
            <input type="text" name="youpin_name" id="youpin_name" size="15" />
            <label for="qty">优惠前销量升：</label>
            <input type="text" name="qty" id="qty" size="15" />
            <label for="guaipai_price">挂牌价（汽油-1.05）：</label>
            <input type="text" name="guaipai_price" id="guaipai_price" size="15" />
            <label for="guiding_maoli">规定毛利（元/吨）-凋零差：</label>
            <input type="text" name="guiding_maoli" id="guiding_maoli" size="15" />
        </div>
        <div class="form-group">
            <label for="guiding_midu">规定密度：</label>
            <input type="text" name="guiding_midu" id="guiding_midu" size="20" />
            
            <label for="yh_fudu">优惠幅度：</label>
            <input type="text" name="yh_fudu" id="yh_fudu" size="20" />
        </div>
        <button type="submit" id="btn_charu">1--优惠基础数据-插入</button>
    </form>
    <br />
    <form method="post" id="form2">
        <button type="button" id="Button1">2--优惠平衡点查询</button>
    </form>
    <div id="div1">
        <br />
    </div>
    <table id="shuju_1" border="1"></table>
    <footer>
        <p>&copy; 2022 优惠数据管理系统</p>
    </footer>
    <script src="scripts.js"></script>
</body>
</html>



<script>
    //$(document).ready(function)   当 DOM（document object model 文档对象模型）加载完毕且页面完全加载（包括图像）时发生 ready 事件
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/qy_fgs_youhuipinghengdian_select?youpin_name=" + $('#youpin_name').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><th>序号</th><th>油品编码</th><th>油品名称</th><th>优惠前销量升</th><th>优惠前销量吨</th><th>挂牌价（汽油-1.05）</th><th>销售吨价</th><th>销售收入（元）</th><th>规定毛利（元/吨）-凋零差</th><th>毛利（元）</th><th>优惠幅度（元/升）</th><th>优惠幅度（元/吨）</th><th>规定密度</th><th>优惠后毛利（元）</th><th>优惠后吨油毛利（元）</th><th>优惠后销售量（吨）</th><th>优惠后销售量（升）</th><th>优惠后销售收入（元）</th><th>增幅比例</th><th>日期</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.id + '</td><td>' + item.youpin_code + '</td><td>'
                            + item.youpin_name + '</td><td>' + item.qty + '</td><td>' + item.qty_kg + '</td><td>'
                            + item.guaipai_price + '</td><td>' + item.xs_price + '</td><td>'
                            + item.xs_amount + '</td ><td>' + item.guiding_maoli + '</td ><td>' + item.maoli + '</td ><td>'
                            + item.yh_fudu + '</td><td>' + item.yh_fudu_kg + '</td><td>' + item.guiding_midu + '</td ><td>'
                            + item.yh_maoli + '</td><td>' + item.yh_maoli_kg + '</td><td>' + item.yh_qty_kg + '</td ><td>'
                            + item.yh_qty + '</td><td>' + item.yh_xs_amount + '</td><td>' + item.zengfu_bili + '%</td ><td>' + item.workday  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        //$('#btn_charu').click(function () {
        //    //$.getJSON("/api/values/HX_tuangoudan_tongbu?billno=" + $('#billno').val(), function (data) { }
        //    $("#form1").attr("action", "/Home/qy_fgs_youhuipinghengdian_insert_ExportData");//根据促发表单，把action 替换成后面的路劲
        //    //这个是同步的功能

        //    $('#btn_charu').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲

        //});
        $('#btn_charu').click(function (event) {
            event.preventDefault(); // 阻止默认提交行为

            $("#form1").attr("action", "/Home/qy_fgs_youhuipinghengdian_insert_ExportData");
            $('#form1').submit();

            // 表单提交后延迟重定向至当前页面
            $('#form1').on('submit', function () {
                setTimeout(function () {
                    window.location.href = "http://************:44401/Home/qy_fgs_youhuipinghengdian_select"; // 重定向至当前页面
                }, 100); // 延迟时间可以根据实际情况调整
            });
        });


    });
</script>
