﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Common</name>
  </assembly>
  <members>
    <member name="T:System.DBNull">
      <summary>表示不存在的值。此类不能被继承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString">
      <summary>返回空字符串（<see cref="F:System.String.Empty" />）。</summary>
      <returns>空字符串（<see cref="F:System.String.Empty" />）。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString(System.IFormatProvider)">
      <summary>使用指定的 <see cref="T:System.IFormatProvider" /> 返回空字符串。</summary>
      <returns>空字符串（<see cref="F:System.String.Empty" />）。</returns>
      <param name="provider">用于格式化返回值的 <see cref="T:System.IFormatProvider" />。- 或 -从操作系统的当前区域设置中获取格式信息的 null。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.DBNull.Value">
      <summary>表示 <see cref="T:System.DBNull" /> 类的唯一实例。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.CommandBehavior">
      <summary>提供对查询结果和查询对数据库的影响的说明。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandBehavior.CloseConnection">
      <summary>在执行该命令时，如果关闭关联的 DataReader 对象，则关联的 Connection 对象也将关闭。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.Default">
      <summary>此查询可能返回多个结果集。执行查询可能会影响数据库的状态。Default 不设置 <see cref="T:System.Data.CommandBehavior" /> 标志，因此调用 ExecuteReader(CommandBehavior.Default) 在功能上等效于调用 ExecuteReader()。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.KeyInfo">
      <summary>此查询返回列和主键信息。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SchemaOnly">
      <summary>查询仅返回列信息。当使用 <see cref="F:System.Data.CommandBehavior.SchemaOnly" /> 时，用于 SQL Server 的 .NET Framework 数据提供程序将在要执行的语句前加上 SET FMTONLY ON。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SequentialAccess">
      <summary>提供一种方法，以便 DataReader 处理包含带有大二进制值的列的行。SequentialAccess 不是加载整行，而是使 DataReader 将数据作为流来加载。然后可以使用 GetBytes 或 GetChars 方法来指定开始读取操作的字节位置以及正在返回的数据的有限的缓冲区大小。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleResult">
      <summary>查询返回一个结果集。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleRow">
      <summary>查询应返回结果集中的单个行。执行查询可能会影响数据库的状态。一些 .NET Framework 数据提供程序可能（但不要求）使用此信息来优化命令的性能。用 <see cref="T:System.Data.OleDb.OleDbCommand" /> 对象的 <see cref="M:System.Data.OleDb.OleDbCommand.ExecuteReader" /> 方法指定 <see cref="F:System.Data.CommandBehavior.SingleRow" /> 时，用于 OLE DB 的 .NET Framework 数据提供程序使用 OLE DB IRow 接口（如果可用）执行绑定。否则，它使用 IRowset 接口。如果您的 SQL 语句应该只返回一行，则指定 <see cref="F:System.Data.CommandBehavior.SingleRow" /> 还可以提高应用程序性能。在执行应返回多个结果集的查询时，可以指定 SingleRow。在这种情况下，同时指定了多结果集 SQL 查询和单行，返回的结果将仅包含第一个结果集的第一行。将不返回查询的其他结果集。</summary>
    </member>
    <member name="T:System.Data.CommandType">
      <summary>指定如何解释命令字符串。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandType.StoredProcedure">
      <summary>存储过程的名称。</summary>
    </member>
    <member name="F:System.Data.CommandType.TableDirect">
      <summary>表的名称。</summary>
    </member>
    <member name="F:System.Data.CommandType.Text">
      <summary>SQL 文本命令。（默认。）</summary>
    </member>
    <member name="T:System.Data.ConnectionState">
      <summary>描述与数据源的连接的当前状态。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ConnectionState.Broken">
      <summary>与数据源的连接中断。只有在连接打开之后才可能发生这种情况。可以关闭处于这种状态的连接，然后重新打开。（该值是为此产品的未来版本保留的。）</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Closed">
      <summary>连接处于关闭状态。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Connecting">
      <summary>连接对象正在与数据源连接。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Executing">
      <summary>连接对象正在执行命令。（该值是为此产品的未来版本保留的。）</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Fetching">
      <summary>连接对象正在检索数据。（该值是为此产品的未来版本保留的。）</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Open">
      <summary>连接处于打开状态。</summary>
    </member>
    <member name="T:System.Data.DbType">
      <summary>指定 .NET Framework 数据提供程序的字段、属性或 Parameter 对象的数据类型。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.DbType.AnsiString">
      <summary>非 Unicode 字符的可变长度流，范围在 1 到 8,000 个字符之间。</summary>
    </member>
    <member name="F:System.Data.DbType.AnsiStringFixedLength">
      <summary>非 Unicode 字符的固定长度流。</summary>
    </member>
    <member name="F:System.Data.DbType.Binary">
      <summary>二进制数据的可变长度流，范围在 1 到 8,000 个字节之间。</summary>
    </member>
    <member name="F:System.Data.DbType.Boolean">
      <summary>简单类型，表示 true 或 false 的布尔值。</summary>
    </member>
    <member name="F:System.Data.DbType.Byte">
      <summary>一个 8 位无符号整数，范围在 0 到 255 之间。</summary>
    </member>
    <member name="F:System.Data.DbType.Currency">
      <summary>货币值，范围在 -2 63（即 -922,337,203,685,477.5808）到 2 63 -1（即 +922,337,203,685,477.5807）之间，精度为千分之十个货币单位。</summary>
    </member>
    <member name="F:System.Data.DbType.Date">
      <summary>表示日期值的类型。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime">
      <summary>表示一个日期和时间值的类型。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime2">
      <summary>日期和时间数据。日期值范围从公元 1 年 1 月 1 日到公元 9999 年 12 月 31 日。时间值范围从 00:00:00 到 23:59:59.9999999，精度为 100 毫微秒。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTimeOffset">
      <summary>显示时区的日期和时间数据。日期值范围从公元 1 年 1 月 1 日到公元 9999 年 12 月 31 日。时间值范围从 00:00:00 到 23:59:59.9999999，精度为 100 毫微秒。时区值范围从 -14:00 到 +14:00。</summary>
    </member>
    <member name="F:System.Data.DbType.Decimal">
      <summary>简单类型，表示从 1.0 x 10 -28 到大约 7.9 x 10 28 且有效位数为 28 到 29 位的值。</summary>
    </member>
    <member name="F:System.Data.DbType.Double">
      <summary>浮点型，表示从大约 5.0 x 10 -324 到 1.7 x 10 308 且精度为 15 到 16 位的值。</summary>
    </member>
    <member name="F:System.Data.DbType.Guid">
      <summary>全局唯一标识符（或 GUID）。</summary>
    </member>
    <member name="F:System.Data.DbType.Int16">
      <summary>整型，表示值介于 -32768 到 32767 之间的有符号 16 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.Int32">
      <summary>整型，表示值介于 -2147483648 到 2147483647 之间的有符号 32 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.Int64">
      <summary>整型，表示值介于 -9223372036854775808 到 9223372036854775807 之间的有符号 64 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.Object">
      <summary>常规类型，表示任何没有由其他 DbType 值显式表示的引用或值类型。</summary>
    </member>
    <member name="F:System.Data.DbType.SByte">
      <summary>整型，表示值介于 -128 到 127 之间的有符号 8 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.Single">
      <summary>浮点型，表示从大约 1.5 x 10 -45 到 3.4 x 10 38 且精度为 7 位的值。</summary>
    </member>
    <member name="F:System.Data.DbType.String">
      <summary>表示 Unicode 字符串的类型。</summary>
    </member>
    <member name="F:System.Data.DbType.StringFixedLength">
      <summary>Unicode 字符的定长串。</summary>
    </member>
    <member name="F:System.Data.DbType.Time">
      <summary>一个表示 SQL Server DateTime 值的类型。如果要使用 SQL Server time 值，请使用 <see cref="F:System.Data.SqlDbType.Time" />。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt16">
      <summary>整型，表示值介于 0 到 65535 之间的无符号 16 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt32">
      <summary>整型，表示值介于 0 到 4294967295 之间的无符号 32 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt64">
      <summary>整型，表示值介于 0 到 18446744073709551615 之间的无符号 64 位整数。</summary>
    </member>
    <member name="F:System.Data.DbType.VarNumeric">
      <summary>变长数值。</summary>
    </member>
    <member name="F:System.Data.DbType.Xml">
      <summary>XML 文档或片段的分析表示。</summary>
    </member>
    <member name="T:System.Data.IsolationLevel">
      <summary>指定连接的事务锁定行为。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.IsolationLevel.Chaos">
      <summary>无法覆盖隔离级别更高的事务中的挂起的更改。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadCommitted">
      <summary>在正在读取数据时保持共享锁，以避免脏读，但是在事务结束之前可以更改数据，从而导致不可重复的读取或幻像数据。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadUncommitted">
      <summary>可以进行脏读，意思是说，不发布共享锁，也不接受独占锁。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.RepeatableRead">
      <summary>在查询中使用的所有数据上放置锁，以防止其他用户更新这些数据。防止不可重复的读取，但是仍可以有幻像行。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Serializable">
      <summary>在 <see cref="T:System.Data.DataSet" /> 上放置范围锁，以防止在事务完成之前由其他用户更新行或向数据集中插入行。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Snapshot">
      <summary>通过在一个应用程序正在修改数据时存储另一个应用程序可以读取的相同数据版本来减少阻止。表示您无法从一个事务中看到在其他事务中进行的更改，即便重新查询也是如此。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Unspecified">
      <summary>正在使用与指定隔离级别不同的隔离级别，但是无法确定该级别。</summary>
    </member>
    <member name="T:System.Data.ParameterDirection">
      <summary>指定查询内的有关 <see cref="T:System.Data.DataSet" /> 的参数的类型。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ParameterDirection.Input">
      <summary>参数是输入参数。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.InputOutput">
      <summary>参数既能输入，也能输出。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.Output">
      <summary>参数是输出参数。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.ReturnValue">
      <summary>参数表示诸如存储过程、内置函数或用户定义函数之类的操作的返回值。</summary>
    </member>
    <member name="T:System.Data.StateChangeEventArgs">
      <summary>为 .NET Framework 数据提供程序的状态更改事件提供数据。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.StateChangeEventArgs.#ctor(System.Data.ConnectionState,System.Data.ConnectionState)">
      <summary>在给定对象的原始状态和当前状态的情况下，初始化 <see cref="T:System.Data.StateChangeEventArgs" /> 类的新实例。</summary>
      <param name="originalState">
        <see cref="T:System.Data.ConnectionState" /> 值之一。</param>
      <param name="currentState">
        <see cref="T:System.Data.ConnectionState" /> 值之一。</param>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.CurrentState">
      <summary>获取连接的新状态。在激发该事件时，连接对象将处于新状态。</summary>
      <returns>
        <see cref="T:System.Data.ConnectionState" /> 值之一。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.OriginalState">
      <summary>获取连接的原始状态。</summary>
      <returns>
        <see cref="T:System.Data.ConnectionState" /> 值之一。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.StateChangeEventHandler">
      <summary>表示将处理 <see cref="E:System.Data.Common.DbConnection.StateChange" /> 事件的方法。</summary>
      <param name="sender">事件源。</param>
      <param name="e">包含事件数据的 <see cref="T:System.Data.StateChangeEventArgs" />。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.UpdateRowSource">
      <summary>指定如何将查询命令结果应用到正在更新的行。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.UpdateRowSource.Both">
      <summary>将输出参数和第一个返回行都映射到 <see cref="T:System.Data.DataSet" /> 中的已更改的行。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.FirstReturnedRecord">
      <summary>将第一个返回行中的数据映射到 <see cref="T:System.Data.DataSet" /> 中的已更改的行。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.None">
      <summary>忽略任何返回的参数或行。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.OutputParameters">
      <summary>将输出参数映射到 <see cref="T:System.Data.DataSet" /> 中的已更改的行。</summary>
    </member>
    <member name="T:System.Data.Common.DbCommand">
      <summary>表示要对数据源执行的 SQL 语句或存储过程。为表示命令的、数据库特有的类提供一个基类。<see cref="Overload:System.Data.Common.DbCommand.ExecuteNonQueryAsync" /></summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.#ctor">
      <summary>构造 <see cref="T:System.Data.Common.DbCommand" /> 对象的实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbCommand.Cancel">
      <summary>尝试取消 <see cref="T:System.Data.Common.DbCommand" /> 的执行。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandText">
      <summary>获取或设置针对数据源运行的文本命令。</summary>
      <returns>要执行的文本命令。默认值为空字符串 ("")。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandTimeout">
      <summary>获取或设置在终止执行命令的尝试并生成错误之前的等待时间。</summary>
      <returns>等待命令执行的时间（以秒为单位）。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandType">
      <summary>指示或指定如何解释 <see cref="P:System.Data.Common.DbCommand.CommandText" /> 属性。</summary>
      <returns>
        <see cref="T:System.Data.CommandType" /> 值之一。默认值为 Text。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Connection">
      <summary>获取或设置此 <see cref="T:System.Data.Common.DbCommand" /> 使用的 <see cref="T:System.Data.Common.DbConnection" />。</summary>
      <returns>与数据源的连接。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateDbParameter">
      <summary>创建 <see cref="T:System.Data.Common.DbParameter" /> 对象的新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 对象。</returns>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateParameter">
      <summary>创建 <see cref="T:System.Data.Common.DbParameter" /> 对象的新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbConnection">
      <summary>获取或设置此 <see cref="T:System.Data.Common.DbCommand" /> 使用的 <see cref="T:System.Data.Common.DbConnection" />。</summary>
      <returns>与数据源的连接。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbParameterCollection">
      <summary>获取 <see cref="T:System.Data.Common.DbParameter" /> 对象的集合。</summary>
      <returns>SQL 语句或存储过程的参数。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbTransaction">
      <summary>获取或设置将在其中执行此 <see cref="T:System.Data.Common.DbCommand" /> 对象的 <see cref="P:System.Data.Common.DbCommand.DbTransaction" />。</summary>
      <returns>要在其中执行 .NET Framework 数据提供程序的 Command 对象的事务。默认值为 null 引用（在 Visual Basic 中为 Nothing）。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DesignTimeVisible">
      <summary>获取或设置一个值，该值指示此命令对象在自定义界面控件中是否可见。</summary>
      <returns>如果该命令对象应在控件中可见，则为 true；否则为 false。默认值为 true。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Dispose"></member>
    <member name="M:System.Data.Common.DbCommand.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
      <summary>对连接执行命令文本。</summary>
      <returns>表示该操作的任务。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 的一个实例。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>提供程序应执行该方法对 <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> 重载提供非默认的实现。默认实现调用同步 <see cref="M:System.Data.Common.DbCommand.ExecuteReader" /> 方法并返回已完成任务，以便阻止调用线程。如果传递到已取消的取消标记，则默认实现将返回已取消的任务。ExecuteReader 引发的异常将通过返回的任务异常属性传递。该方法可用于请求操作之前接受取消标记。实现可能会忽略该请求。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="behavior">语句执行和数据检索的选项。</param>
      <param name="cancellationToken">针对取消请求监视的标记。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQuery">
      <summary>对连接对象执行 SQL 语句。</summary>
      <returns>受影响的行数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync">
      <summary>
        <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />的异步版本，针对一个连接对象执行一个 SQL 声明。通过 CancellationToken.None 调用 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>这是 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> 的异步版本。提供程序应使用合适的实现进行重写。可选择性忽略取消标记。默认实现调用同步 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> 方法并返回已完成任务，以便阻止调用线程。如果传递到已取消的取消标记，则默认实现将返回已取消的任务。<see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />引发的异常将通过任务异常属性传递。在返回的任务完成前，不要调用 DbCommand 对象的其他方法和属性。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">针对取消请求监视的标记。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader">
      <summary>针对 <see cref="P:System.Data.Common.DbCommand.Connection" /> 执行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，并返回 <see cref="T:System.Data.Common.DbDataReader" />。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>针对 <see cref="P:System.Data.Common.DbCommand.Connection" /> 执行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，并使用 <see cref="T:System.Data.CommandBehavior" /> 值之一返回 <see cref="T:System.Data.Common.DbDataReader" />。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 对象。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 值之一。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync">
      <summary>
        <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> 的异步版本，针对 <see cref="P:System.Data.Common.DbCommand.Connection" /> 和 <see cref="T:System.Data.Common.DbDataReader" /> 执行 <see cref="P:System.Data.Common.DbCommand.CommandText" />。通过 CancellationToken.None 调用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>
        <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> 的异步版本，针对 <see cref="P:System.Data.Common.DbCommand.Connection" /> 和 <see cref="T:System.Data.Common.DbDataReader" /> 执行 <see cref="P:System.Data.Common.DbCommand.CommandText" />。调用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 值之一。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>调用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 值之一。</param>
      <param name="cancellationToken">针对取消请求监视的标记。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>
        <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> 的异步版本，针对 <see cref="P:System.Data.Common.DbCommand.Connection" /> 和 <see cref="T:System.Data.Common.DbDataReader" /> 执行 <see cref="P:System.Data.Common.DbCommand.CommandText" />。此方法传播有关应取消操作的通知。调用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">针对取消请求监视的标记。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
      <exception cref="T:System.ArgumentException">
        <see cref="T:System.Data.CommandBehavior" /> 值无效。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalar">
      <summary>执行查询，并返回查询所返回的结果集中第一行的第一列。所有其他的列和行将被忽略。</summary>
      <returns>结果集中第一行的第一列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync">
      <summary>
        <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 的异步版本，执行查询，并将查询返回的结果集中第一行的第一列返回。所有其他的列和行将被忽略。通过 CancellationToken.None 调用 <see cref="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>这是 <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 的异步版本。提供程序应使用合适的实现进行重写。可选择性忽略取消标记。默认实现调用同步 <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 方法并返回已完成任务，以便阻止调用线程。如果传递到已取消的取消标记，则默认实现将返回已取消的任务。ExecuteScalar 引发的异常将通过返回的任务异常属性传递。在返回的任务完成前，不要调用 DbCommand 对象的其他方法和属性。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">针对取消请求监视的标记。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="P:System.Data.Common.DbCommand.Parameters">
      <summary>获取 <see cref="T:System.Data.Common.DbParameter" /> 对象的集合。有关参数的更多信息，请参见 配置参数和参数数据类型。</summary>
      <returns>SQL 语句或存储过程的参数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Prepare">
      <summary>在数据源上创建该命令的准备好的（或已编译的）版本。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Transaction">
      <summary>获取或设置将在其中执行此 <see cref="T:System.Data.Common.DbCommand" /> 对象的 <see cref="T:System.Data.Common.DbTransaction" />。</summary>
      <returns>要在其中执行 .NET Framework 数据提供程序的 Command 对象的事务。默认值为 null 引用（在 Visual Basic 中为 Nothing）。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.UpdatedRowSource">
      <summary>获取或设置命令结果在由 <see cref="T:System.Data.Common.DbDataAdapter" /> 的 Update 方法使用时如何应用于 <see cref="T:System.Data.DataRow" />。</summary>
      <returns>
        <see cref="T:System.Data.UpdateRowSource" /> 值之一。如果该命令不是自动生成的，则默认值为 Both。否则，默认值为 None。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbConnection">
      <summary>表示到数据库的连接。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbConnection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
      <summary>开始数据库事务。</summary>
      <returns>表示新事务的对象。</returns>
      <param name="isolationLevel">指定事务的隔离级别。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction">
      <summary>开始数据库事务。</summary>
      <returns>表示新事务的对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>以指定的隔离级别启动数据库事务。</summary>
      <returns>表示新事务的对象。</returns>
      <param name="isolationLevel">指定事务的隔离级别。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.ChangeDatabase(System.String)">
      <summary>为打开的连接更改当前数据库。</summary>
      <param name="databaseName">为要使用的连接指定数据库名称。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Close">
      <summary>关闭与数据库的连接。此方法是关闭任何已打开连接的首选方法。</summary>
      <exception cref="T:System.Data.Common.DbException">在打开连接时出现连接级别的错误。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionString">
      <summary>获取或设置用于打开连接的字符串。</summary>
      <returns>用来建立初始连接的连接字符串。该连接字符串的确切内容取决于此连接的特定数据源。默认值为空字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionTimeout">
      <summary>获取在建立连接时终止尝试并生成错误之前所等待的时间。</summary>
      <returns>等待连接打开的时间（以秒为单位）。默认值是由您正在使用的连接的特定类型确定的。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateCommand">
      <summary>创建并返回与当前连接关联的 <see cref="T:System.Data.Common.DbCommand" /> 对象。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateDbCommand">
      <summary>创建并返回与当前连接关联的 <see cref="T:System.Data.Common.DbCommand" /> 对象。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 对象。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnection.Database">
      <summary>在连接打开之后获取当前数据库的名称，或者在连接打开之前获取连接字符串中指定的数据库名。</summary>
      <returns>当前数据库的名称或连接打开后要使用的数据库的名称。默认值为空字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.DataSource">
      <summary>获取要连接的数据库服务器的名称。</summary>
      <returns>要连接的数据库服务器的名称。默认值为空字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Dispose"></member>
    <member name="M:System.Data.Common.DbConnection.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbConnection.OnStateChange(System.Data.StateChangeEventArgs)">
      <summary>引发 <see cref="E:System.Data.Common.DbConnection.StateChange" /> 事件。</summary>
      <param name="stateChange">包含事件数据的 <see cref="T:System.Data.StateChangeEventArgs" />。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.Open">
      <summary>使用 <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> 所指定的设置打开数据库连接。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync">
      <summary>
        <see cref="M:System.Data.Common.DbConnection.Open" />的异步版本，打开由 <see cref="P:System.Data.Common.DbConnection.ConnectionString" />指定的设置的数据库连接。此方法通过 CancellationToken.None 调用虚拟方法 <see cref="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>这是 <see cref="M:System.Data.Common.DbConnection.Open" /> 的异步版本。提供程序应使用合适的实现进行重写。可选择性接受取消标记。默认实现调用同步 <see cref="M:System.Data.Common.DbConnection.Open" /> 调用并返回已完成任务。如果传递到已取消的取消标记，则默认实现将返回已取消的 cancellationToken。Open 引发的异常将通过返回的任务异常属性传递。.在返回的任务完成前，不要调用 DbConnection 对象的其他方法和属性。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">取消指示。</param>
    </member>
    <member name="P:System.Data.Common.DbConnection.ServerVersion">
      <summary>获取表示对象所连接到的服务器的版本的字符串。</summary>
      <returns>数据库的版本。返回的字符串的格式取决于您正在使用的连接的特定类型。</returns>
      <exception cref="T:System.InvalidOperationException">当返回的任务尚未完成，且调用 <see cref="Overload:System.Data.Common.DbConnection.OpenAsync" /> 后连接并未打开时，将调用 <see cref="P:System.Data.Common.DbConnection.ServerVersion" />。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.State">
      <summary>获取描述连接状态的字符串。</summary>
      <returns>连接的状态。返回的字符串的格式取决于您正在使用的连接的特定类型。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="E:System.Data.Common.DbConnection.StateChange">
      <summary>当事件状态更改时发生。</summary>
    </member>
    <member name="T:System.Data.Common.DbConnectionStringBuilder">
      <summary>为强类型连接字符串生成器提供基类。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Add(System.String,System.Object)">
      <summary>将带有指定键和值的项添加到 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中。</summary>
      <param name="keyword">要添加到 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的键。</param>
      <param name="value">指定键的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 为 null 引用（在 Visual Basic 中为 Nothing）。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 为只读。- 或 -<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.AppendKeyValuePair(System.Text.StringBuilder,System.String,System.String)">
      <summary>提供了一种有效而安全的方法，用于将项和值追加到某个现有的 <see cref="T:System.Text.StringBuilder" /> 对象。</summary>
      <param name="builder">要向其中添加键/值对的 <see cref="T:System.Text.StringBuilder" />。</param>
      <param name="keyword">要添加的键。</param>
      <param name="value">提供的键的值。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Clear">
      <summary>清除 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 实例的内容。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 为只读。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString">
      <summary>获取或设置与 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 关联的连接字符串。</summary>
      <returns>当前的连接字符串，它由 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中所包含的键/值对创建。默认值为空字符串。</returns>
      <exception cref="T:System.ArgumentException">提供的字符串参数无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ContainsKey(System.String)">
      <summary>确定 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是否包含特定键。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 包含具有指定键的项，则为 true；否则为 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中定位的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 为 null 引用（在 Visual Basic 中为 Nothing）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Count">
      <summary>获取 <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> 属性中当前包含的键的数目。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 实例所维护的连接字符串内包含的键的数目。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.EquivalentTo(System.Data.Common.DbConnectionStringBuilder)">
      <summary>将此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 对象中的连接信息与提供的对象中的连接信息进行比较。</summary>
      <returns>如果两个 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 对象中的连接信息生成等效的连接字符串，则为 true；否则为 false。</returns>
      <param name="connectionStringBuilder">要与此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 对象进行比较的 <see cref="T:System.Data.Common.DbConnectionStringBuilder" />。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Item(System.String)">
      <summary>获取或设置与指定的键相关联的值。</summary>
      <returns>与指定的键相关联的值。如果未找到指定的键，尝试获取它将返回空引用（在 Visual Basic 中为 Nothing），尝试设置它将使用指定的键创建新元素。传递空（在 Visual Basic 中为 Nothing）键将引发 <see cref="T:System.ArgumentNullException" />。赋予空值将移除键/值对。</returns>
      <param name="keyword">要获取或设置的项的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 为 null 引用（在 Visual Basic 中为 Nothing）。</exception>
      <exception cref="T:System.NotSupportedException">设置该属性，而且 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 为只读。- 或 -设置该属性，集合中不存在 <paramref name="keyword" />，而且 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Keys">
      <summary>获取一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的键。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的键。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Remove(System.String)">
      <summary>移除 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 实例中具有指定键的项。</summary>
      <returns>如果该键存在于连接字符串中并被移除，则为 true；如果该键不存在，则为 false。</returns>
      <param name="keyword">要从此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的连接字符串移除的键/值对中的键。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 为 null（在 Visual Basic 中为 Nothing）</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 为只读，或 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>指示此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 实例中是否存在指定键。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 包含具有指定键的项，则为 true；否则为 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中定位的键。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>从特定的 <see cref="T:System.Array" /> 索引处开始，将 <see cref="T:System.Collections.ICollection" /> 的元素复制到一个 <see cref="T:System.Array" /> 中。</summary>
      <param name="array">作为从 <see cref="T:System.Collections.ICollection" /> 复制的元素的目标的一维 <see cref="T:System.Array" />。<see cref="T:System.Array" /> 必须具有从零开始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中从零开始的索引，从此索引处开始进行复制。</param>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#IsSynchronized">
      <summary>获取一个值，该值指示是否同步对 <see cref="T:System.Collections.ICollection" /> 的访问（线程安全）。</summary>
      <returns>如果对 <see cref="T:System.Collections.ICollection" /> 的访问是同步的（线程安全），则为 true；否则为 false。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#SyncRoot">
      <summary>获取可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</summary>
      <returns>可用于同步对 <see cref="T:System.Collections.ICollection" /> 的访问的对象。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>在 <see cref="T:System.Collections.IDictionary" /> 对象中添加一个带有所提供的键和值的元素。</summary>
      <param name="keyword">用作要添加的元素的键的 <see cref="T:System.Object" />。</param>
      <param name="value">用作要添加的元素的值的 <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>确定 <see cref="T:System.Collections.IDictionary" /> 对象是否包含具有指定键的元素。</summary>
      <returns>如果 <see cref="T:System.Collections.IDictionary" /> 包含带有该键的元素，则为 true；否则为 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Collections.IDictionary" /> 对象中定位的键。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#GetEnumerator">
      <summary>返回一个用于 <see cref="T:System.Collections.IDictionary" /> 对象的 <see cref="T:System.Collections.IDictionaryEnumerator" /> 对象。</summary>
      <returns>一个用于 <see cref="T:System.Collections.IDictionary" /> 对象的 <see cref="T:System.Collections.IDictionaryEnumerator" /> 对象。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Item(System.Object)">
      <summary>获取或设置具有指定键的元素。</summary>
      <returns>带有指定键的元素。</returns>
      <param name="keyword">要获取或设置的元素的键。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>从 <see cref="T:System.Collections.IDictionary" /> 对象中移除带有指定键的元素。</summary>
      <param name="keyword">要移除的元素的键。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IEnumerable#GetEnumerator">
      <summary>返回一个循环访问集合的枚举器。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" /> 对象。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ToString">
      <summary>返回与此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 关联的连接字符串。</summary>
      <returns>当前的 <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> 属性。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>从此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中检索与所提供的键对应的值。</summary>
      <returns>如果在连接字符串中找到 <paramref name="keyword" />，则为 true；否则为 false。</returns>
      <param name="keyword">要检索的项的键。</param>
      <param name="value">与 <paramref name="key" /> 对应的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 包含一个 null 值（在 Visual Basic 中为 Nothing）。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Values">
      <summary>获取包含 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的值的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>一个 <see cref="T:System.Collections.ICollection" />，它包含 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的值。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="T:System.Data.Common.DbDataReader">
      <summary>从数据源读取行的一个只进流。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbDataReader" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Depth">
      <summary>获取一个值，该值指示当前行的嵌套深度。</summary>
      <returns>当前行的嵌套深度。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose">
      <summary>释放由 <see cref="T:System.Data.Common.DbDataReader" /> 类的当前实例占用的所有资源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Data.Common.DbDataReader" /> 使用的托管资源，还可以选择释放非托管资源。</summary>
      <param name="disposing">若要释放托管资源和非托管资源，则为 true；若要仅释放非托管资源，则为 false。</param>
    </member>
    <member name="P:System.Data.Common.DbDataReader.FieldCount">
      <summary>获取当前行中的列数。</summary>
      <returns>当前行中的列数。</returns>
      <exception cref="T:System.NotSupportedException">没有对 SQL Server 实例的当前连接。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBoolean(System.Int32)">
      <summary>获取指定列的布尔值形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetByte(System.Int32)">
      <summary>获取指定列的字节形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>从指定列读取一个字节流（从 <paramref name="dataOffset" /> 指示的位置开始），读到缓冲区中（从 <paramref name="bufferOffset" /> 指示的位置开始）。</summary>
      <returns>读取的实际字节数。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <param name="dataOffset">行中的索引，从其开始读取操作。</param>
      <param name="buffer">作为数据复制目标的缓冲区。</param>
      <param name="bufferOffset">具有作为数据复制目标的缓冲区的索引。</param>
      <param name="length">最多读取的字符数。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChar(System.Int32)">
      <summary>获取指定列的单个字符串形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>从指定列读取一个字符流，从 <paramref name="dataOffset" /> 指示的位置开始，读到缓冲区中，从 <paramref name="bufferOffset" /> 指示的位置开始。</summary>
      <returns>读取的实际字符数。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <param name="dataOffset">行中的索引，从其开始读取操作。</param>
      <param name="buffer">作为数据复制目标的缓冲区。</param>
      <param name="bufferOffset">具有作为数据复制目标的缓冲区的索引。</param>
      <param name="length">最多读取的字符数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetData(System.Int32)">
      <summary>返回被请求的列序号的 <see cref="T:System.Data.Common.DbDataReader" /> 对象。</summary>
      <returns>一个 <see cref="T:System.Data.Common.DbDataReader" /> 对象。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDataTypeName(System.Int32)">
      <summary>获取指定列的数据类型的名称。</summary>
      <returns>表示数据类型的名称的字符串。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDateTime(System.Int32)">
      <summary>获取指定列的 <see cref="T:System.DateTime" /> 对象形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDbDataReader(System.Int32)">
      <summary>返回被请求的列序号的 <see cref="T:System.Data.Common.DbDataReader" /> 对象，可以使用提供程序特定的实现对该对象进行重写。</summary>
      <returns>一个 <see cref="T:System.Data.Common.DbDataReader" /> 对象。</returns>
      <param name="ordinal">从零开始的列序号。</param>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDecimal(System.Int32)">
      <summary>获取指定列的 <see cref="T:System.Decimal" /> 对象形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDouble(System.Int32)">
      <summary>获取指定列的双精度浮点数形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetEnumerator">
      <summary>返回一个 <see cref="T:System.Collections.IEnumerator" />，可用于循环访问数据读取器中的行。</summary>
      <returns>一个 <see cref="T:System.Collections.IEnumerator" />，可用于循环访问数据读取器中的行。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldType(System.Int32)">
      <summary>获取指定列的数据类型。</summary>
      <returns>指定列的数据类型。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValue``1(System.Int32)">
      <summary>同步获取作为类型的指定列的值。</summary>
      <returns>要检索的列。</returns>
      <param name="ordinal">要检索的列。</param>
      <typeparam name="T">同步获取作为类型的指定列的值。</typeparam>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间<see cref="T:System.Data.SqlClient.SqlDataReader" /> 处于关闭状态。没有可读取的就绪数据 (例如，第一个 <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> 与 SQL Server 返回的类型不匹配或无法进行转换。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)">
      <summary>异步获取作为类型的指定列的值。</summary>
      <returns>要返回的值的类型。</returns>
      <param name="ordinal">要返回的值的类型。</param>
      <typeparam name="T">要返回的值的类型。有关更多信息，请参见备注部分。</typeparam>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> 与数据源返回的类型不匹配或无法进行强制转换。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>异步获取作为类型的指定列的值。</summary>
      <returns>要返回的值的类型。</returns>
      <param name="ordinal">要返回的值的类型。</param>
      <param name="cancellationToken">取消指示，用于对应该取消操作的通知进行传播。此构造函数不对取消做保证。设置为 CancellationToken.None 将此方法等效于 <see cref="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)" />。返回任务必须标记为已取消。</param>
      <typeparam name="T">要返回的值的类型。有关更多信息，请参见备注部分。</typeparam>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> 与数据源返回的类型不匹配或无法进行转换。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFloat(System.Int32)">
      <summary>获取指定列的单精度浮点数形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetGuid(System.Int32)">
      <summary>获取指定列的全局唯一标识符 (GUID) 形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt16(System.Int32)">
      <summary>获取指定列的 16 位有符号整数形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt32(System.Int32)">
      <summary>获取指定列的 32 位有符号整数形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt64(System.Int32)">
      <summary>获取指定列的 64 位有符号整数形式的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetName(System.Int32)">
      <summary>给定了从零开始的列序号时，获取列的名称。</summary>
      <returns>指定列的名称。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetOrdinal(System.String)">
      <summary>给定列名称时，获取列序号。</summary>
      <returns>从零开始的列序号。</returns>
      <param name="name">列的名称。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的名称不是有效的列名称。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>返回指定列的提供程序特定的字段类型。</summary>
      <returns>描述指定列的数据类型的 <see cref="T:System.Type" /> 对象。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>获取指定列的作为 <see cref="T:System.Object" /> 的实例的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>获取集合中当前行的所有提供程序特定的特性列。</summary>
      <returns>数组中 <see cref="T:System.Object" /> 的实例的数目。</returns>
      <param name="values">要将特性列复制到其中的 <see cref="T:System.Object" /> 数组。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetStream(System.Int32)">
      <summary>检索作为 <see cref="T:System.IO.Stream" /> 的数据。</summary>
      <returns>返回的对象。</returns>
      <param name="ordinal">检索作为 <see cref="T:System.IO.Stream" /> 的数据。</param>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
      <exception cref="T:System.InvalidCastException">返回的类型不属于以下类型之一：binaryimagevarbinaryUDT</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetString(System.Int32)">
      <summary>获取指定列的作为 <see cref="T:System.String" /> 的实例的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.InvalidCastException">指定的强制转换无效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)">
      <summary>检索作为 <see cref="T:System.IO.TextReader" /> 的数据。</summary>
      <returns>返回的对象。</returns>
      <param name="ordinal">检索作为 <see cref="T:System.IO.TextReader" /> 的数据。</param>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
      <exception cref="T:System.InvalidCastException">返回的类型不属于以下类型之一：charncharntextnvarchar文本varchar</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValue(System.Int32)">
      <summary>获取指定列的作为 <see cref="T:System.Object" /> 的实例的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValues(System.Object[])">
      <summary>使用当前行的列值来填充对象数组。</summary>
      <returns>数组中 <see cref="T:System.Object" /> 的实例的数目。</returns>
      <param name="values">要将特性列复制到其中的 <see cref="T:System.Object" /> 数组。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.HasRows">
      <summary>获取一个值，它指示此 <see cref="T:System.Data.Common.DbDataReader" /> 是否包含一个或多个行。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbDataReader" /> 包含一行或多行，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.IsClosed">
      <summary>获取一个值，该值指示 <see cref="T:System.Data.Common.DbDataReader" /> 是否已关闭。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbDataReader" /> 已关闭，则为 true；否则为 false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Data.SqlClient.SqlDataReader" /> 是关闭的。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)">
      <summary>获取一个值，该值指示列中是否包含不存在的或已丢失的值。</summary>
      <returns>如果指定的列与 <see cref="T:System.DBNull" /> 等效，则为 true；否则为 false。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> 的异步版本，其获取指示列是否包含不存在或缺失值的值。</summary>
      <returns>如果指定的列值与 DBNull 等效，则为 true；否则为 false。</returns>
      <param name="ordinal">从零开始的要检索的列。</param>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> 的异步版本，其获取指示列是否包含不存在或缺失值的值。可选地，发送操作应取消的注明。</summary>
      <returns>如果指定的列值与 DBNull 等效，则为 true；否则为 false。</returns>
      <param name="ordinal">从零开始的要检索的列。</param>
      <param name="cancellationToken">取消指示，用于对应该取消操作的通知进行传播。此构造函数不对取消做保证。设置 CancellationToken.None 使得这种方法等同于 <see cref="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)" />。返回任务必须标记为已取消。</param>
      <exception cref="T:System.InvalidOperationException">连接中断或在数据检索期间已关闭。数据检索期间，<see cref="T:System.Data.Common.DbDataReader" /> 处于关闭状态。没有要读取的就绪数据 (例如，第一个 <see cref="M:System.Data.Common.DbDataReader.Read" /> 未被调用或返回错误)。按顺序模式尝试读取上一步骤中读取的列。没有正在进行的异步操作。以顺序模式运行时，这适用于所有 Get* 方法，读取流时也可对其进行调用。</exception>
      <exception cref="T:System.IndexOutOfRangeException">尝试读取不存在的列。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.Int32)">
      <summary>获取指定列的作为 <see cref="T:System.Object" /> 的实例的值。</summary>
      <returns>指定列的值。</returns>
      <param name="ordinal">从零开始的列序号。</param>
      <exception cref="T:System.IndexOutOfRangeException">传递的索引位于 0 至 <see cref="P:System.Data.IDataRecord.FieldCount" /> 的范围之外。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.String)">
      <summary>获取指定列的作为 <see cref="T:System.Object" /> 的实例的值。</summary>
      <returns>指定列的值。</returns>
      <param name="name">列的名称。</param>
      <exception cref="T:System.IndexOutOfRangeException">未找到具有指定名称的列。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResult">
      <summary>读取批处理语句的结果时，使读取器前进到下一个结果。</summary>
      <returns>如果存在多个结果集，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 的异步版本，读取批处理语句的结果时，将读取器推进到下一个结果。通过 CancellationToken.None 调用 <see cref="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>这是 <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 的异步版本。提供程序应使用合适的实现进行重写。可选择性忽略 <paramref name="cancellationToken" />。默认实现调用同步 <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 方法并返回已完成任务，以便阻止调用线程。如果传递到已取消 <paramref name="cancellationToken" />，则默认实现将返回已取消的任务。<see cref="M:System.Data.Common.DbDataReader.NextResult" /> 引发的异常将通过任务异常属性传递。当返回任务还未完成时， DbDataReader 对象的其他方法和属性不应调用 。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">取消指示。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Read">
      <summary>将读取器前进到结果集中的下一个记录。</summary>
      <returns>如果存在多个行，则为 true；否则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.Read" />的异步版本，将读取器前移到结果集的下一条记录。此方法通过 CancellationToken.None 调用 <see cref="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示异步操作的任务。</returns>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>这是 <see cref="M:System.Data.Common.DbDataReader.Read" /> 的异步版本。提供程序应使用合适的实现进行重写。可选择性忽略的 cancellationToken。默认实现调用同步 <see cref="M:System.Data.Common.DbDataReader.Read" /> 方法并返回已完成任务，以便阻止调用线程。如果传递到已取消的取消标记，则默认实现将返回已取消的 cancellationToken。Read 引发的异常将通过返回的任务异常属性传递。在返回的任务完成前，不要调用 DbDataReader 对象的其他方法和属性。</summary>
      <returns>表示异步操作的任务。</returns>
      <param name="cancellationToken">取消指示。</param>
      <exception cref="T:System.Data.Common.DbException">执行命令文本时出现的一个错误。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.RecordsAffected">
      <summary>通过执行 SQL 语句获取更改、插入或删除的行数。</summary>
      <returns>更改、插入或删除的行数。对于 SELECT 语句为 -1；如果没影响任何行或该语句失败，则为 0。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.VisibleFieldCount">
      <summary>获取 <see cref="T:System.Data.Common.DbDataReader" /> 中未隐藏的字段的数目。</summary>
      <returns>未隐藏的字段的数目。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbException">
      <summary>代表数据源引发的所有异常的基类。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbException" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String)">
      <summary>使用指定的错误消息初始化 <see cref="T:System.Data.Common.DbException" /> 类的新实例。</summary>
      <param name="message">为此异常显示的消息。</param>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String,System.Exception)">
      <summary>使用指定的错误消息和对导致此异常的内部异常的引用来初始化 <see cref="T:System.Data.Common.DbException" /> 类的新实例。</summary>
      <param name="message">错误消息字符串。</param>
      <param name="innerException">内部异常引用。</param>
    </member>
    <member name="T:System.Data.Common.DbParameter">
      <summary>表示 <see cref="T:System.Data.Common.DbCommand" /> 的参数，还可表示该参数到一个 <see cref="T:System.Data.DataSet" /> 列的映射。有关参数的更多信息，请参见 配置参数和参数数据类型。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameter.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbParameter" /> 类的新实例。</summary>
    </member>
    <member name="P:System.Data.Common.DbParameter.DbType">
      <summary>获取或设置参数的 <see cref="T:System.Data.DbType" />。</summary>
      <returns>
        <see cref="T:System.Data.DbType" /> 值之一。默认值为 <see cref="F:System.Data.DbType.String" />。</returns>
      <exception cref="T:System.ArgumentException">该属性未设置为有效的 <see cref="T:System.Data.DbType" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Direction">
      <summary>获取或设置一个值，该值指示参数是只可输入、只可输出、双向还是存储过程返回值参数。</summary>
      <returns>
        <see cref="T:System.Data.ParameterDirection" /> 值之一。默认值为 Input。</returns>
      <exception cref="T:System.ArgumentException">该属性未设置为有效的 <see cref="T:System.Data.ParameterDirection" /> 值之一。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.IsNullable">
      <summary>获取或设置一个值，该值指示参数是否接受空值。</summary>
      <returns>如果接受 null 值，则为 true；否则为 false。默认值为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.ParameterName">
      <summary>获取或设置 <see cref="T:System.Data.Common.DbParameter" /> 的名称。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 的名称。默认值为空字符串 ("")。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Precision">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]获取或设置用来表示 <see cref="P:System.Data.Common.DbParameter.Value" /> 属性的最大位数。</summary>
      <returns>用于表示 <see cref="P:System.Data.Common.DbParameter.Value" /> 属性的最大位数。</returns>
    </member>
    <member name="M:System.Data.Common.DbParameter.ResetDbType">
      <summary>将 DbType 属性重置为其原始设置。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Scale">
      <summary>[在 .NET Framework 4.5.1 和更高版本中受支持]获取或设置 <see cref="P:System.Data.Common.DbParameter.Value" /> 解析为的小数位数。</summary>
      <returns>要将 <see cref="P:System.Data.Common.DbParameter.Value" /> 解析为的小数位数。</returns>
    </member>
    <member name="P:System.Data.Common.DbParameter.Size">
      <summary>获取或设置列中数据的最大大小（以字节为单位）。</summary>
      <returns>列中数据的最大大小（以字节为单位）。默认值是从参数值推导出的。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumn">
      <summary>获取或设置源列的名称，该源列映射到 <see cref="T:System.Data.DataSet" /> 并用于加载或返回 <see cref="P:System.Data.Common.DbParameter.Value" />。</summary>
      <returns>映射到 <see cref="T:System.Data.DataSet" /> 的源列的名称。默认值为空字符串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumnNullMapping">
      <summary>设置或获取一个值，该值指示源列是否可以为 null。这使得 <see cref="T:System.Data.Common.DbCommandBuilder" /> 能够正确地为可以为 null 的列生成 Update 语句。</summary>
      <returns>如果源列可以为 null，则为 true；如果不可以为 null，则为 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Value">
      <summary>获取或设置该参数的值。</summary>
      <returns>一个 <see cref="T:System.Object" />，它是该参数的值。默认值为 null。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbParameterCollection">
      <summary>与 <see cref="T:System.Data.Common.DbCommand" /> 相关的参数集合的基类。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbParameterCollection" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Add(System.Object)">
      <summary>将指定的 <see cref="T:System.Data.Common.DbParameter" /> 对象添加到 <see cref="T:System.Data.Common.DbParameterCollection" /> 中。</summary>
      <returns>集合中 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引。</returns>
      <param name="value">要添加到集合中的 <see cref="T:System.Data.Common.DbParameter" /> 的 <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.AddRange(System.Array)">
      <summary>将具有指定值的项的数组添加到 <see cref="T:System.Data.Common.DbParameterCollection" />。</summary>
      <param name="values">要添加到集合的 <see cref="T:System.Data.Common.DbParameter" /> 类型值的数组。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Clear">
      <summary>从 <see cref="T:System.Data.Common.DbParameterCollection" /> 中移除所有 <see cref="T:System.Data.Common.DbParameter" /> 值。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.Object)">
      <summary>指示集合中是否包含具有指定 <see cref="P:System.Data.Common.DbParameter.Value" /> 的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbParameter" /> 在集合中，则为 true；否则为 false。</returns>
      <param name="value">要在集合中查找的 <see cref="T:System.Data.Common.DbParameter" /> 的 <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.String)">
      <summary>指示集合中是否存在具有指定名称的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbParameter" /> 在集合中，则为 true；否则为 false。</returns>
      <param name="value">要在集合中查找的 <see cref="T:System.Data.Common.DbParameter" /> 的名称。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>从指定索引处开始，将项的数组复制到集合中。</summary>
      <param name="array">要复制到集合中的项的数组。</param>
      <param name="index">要将项复制到的集合中的索引。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Count">
      <summary>指定集合中项的数目。</summary>
      <returns>集合中的项数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetEnumerator">
      <summary>公开 <see cref="M:System.Collections.IEnumerable.GetEnumerator" /> 方法，该方法支持 .NET Framework 数据提供程序对集合进行简单的迭代。</summary>
      <returns>可用于循环访问集合的 <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.Int32)">
      <summary>返回集合中指定索引处的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</summary>
      <returns>集合中指定索引处的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</returns>
      <param name="index">集合中 <see cref="T:System.Data.Common.DbParameter" /> 的索引。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.String)">
      <summary>返回具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</summary>
      <returns>具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</returns>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 的名称。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.Object)">
      <summary>返回指定的 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引。</summary>
      <returns>指定的 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引。</returns>
      <param name="value">集合中的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.String)">
      <summary>返回具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引。</summary>
      <returns>具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引。</returns>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 对象的名称。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Insert(System.Int32,System.Object)">
      <summary>将具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象的指定索引插入到集合中指定的索引位置。</summary>
      <param name="index">插入 <see cref="T:System.Data.Common.DbParameter" /> 对象的索引位置。</param>
      <param name="value">要插入到集合中的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.Int32)">
      <summary>获取并设置指定索引处的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>位于指定索引处的 <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="index">参数的从零开始的索引。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的索引不存在。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.String)">
      <summary>获取并设置具有指定名称的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>具有指定名称的 <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="parameterName">参数名。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的索引不存在。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Remove(System.Object)">
      <summary>从集合中移除指定的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</summary>
      <param name="value">要移除的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.Int32)">
      <summary>从集合中移除位于指定索引位置的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> 对象所处的索引位置。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.String)">
      <summary>从集合中删除具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象。</summary>
      <param name="parameterName">要移除的 <see cref="T:System.Data.Common.DbParameter" /> 对象的名称。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
      <summary>将指定索引处的 <see cref="T:System.Data.Common.DbParameter" /> 对象设置为一个新值。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> 对象所处的索引位置。</param>
      <param name="value">新 <see cref="T:System.Data.Common.DbParameter" /> 值。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
      <summary>将具有指定名称的 <see cref="T:System.Data.Common.DbParameter" /> 对象设置为新的值。</summary>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 对象的名称。</param>
      <param name="value">新 <see cref="T:System.Data.Common.DbParameter" /> 值。</param>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.SyncRoot">
      <summary>指定要用于同步对集合访问的 <see cref="T:System.Object" />。</summary>
      <returns>要用于同步对 <see cref="T:System.Data.Common.DbParameterCollection" /> 访问的 <see cref="T:System.Object" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#Item(System.Int32)">
      <summary>获取或设置位于指定索引处的元素。</summary>
      <returns>位于指定索引处的元素。</returns>
      <param name="index">要获得或设置的元素从零开始的索引。</param>
    </member>
    <member name="T:System.Data.Common.DbProviderFactory">
      <summary>表示一组方法，这些方法用于创建提供程序对数据源类的实现的实例。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbProviderFactory" /> 类的新实例。</summary>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateCommand">
      <summary>返回实现 <see cref="T:System.Data.Common.DbCommand" /> 类的提供程序的类的一个新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 的新实例。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnection">
      <summary>返回实现 <see cref="T:System.Data.Common.DbConnection" /> 类的提供程序的类的一个新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnection" /> 的新实例。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnectionStringBuilder">
      <summary>返回实现 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 类的提供程序的类的一个新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 的新实例。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateParameter">
      <summary>返回实现 <see cref="T:System.Data.Common.DbParameter" /> 类的提供程序的类的一个新实例。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 的新实例。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbTransaction">
      <summary>事务的基类。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.#ctor">
      <summary>初始化新的 <see cref="T:System.Data.Common.DbTransaction" /> 对象。</summary>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Commit">
      <summary>提交数据库事务。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.Connection">
      <summary>指定与该事务关联的 <see cref="T:System.Data.Common.DbConnection" /> 对象。</summary>
      <returns>与该事务关联的 <see cref="T:System.Data.Common.DbConnection" /> 对象。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.DbConnection">
      <summary>指定与该事务关联的 <see cref="T:System.Data.Common.DbConnection" /> 对象。</summary>
      <returns>与该事务关联的 <see cref="T:System.Data.Common.DbConnection" /> 对象。</returns>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose">
      <summary>释放 <see cref="T:System.Data.Common.DbTransaction" /> 使用的非托管资源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose(System.Boolean)">
      <summary>释放由 <see cref="T:System.Data.Common.DbTransaction" /> 占用的非托管资源，还可以另外再释放托管资源。</summary>
      <param name="disposing">如果为 true，此方法将释放由此 <see cref="T:System.Data.Common.DbTransaction" /> 引用的任何托管对象所保留的全部资源。</param>
    </member>
    <member name="P:System.Data.Common.DbTransaction.IsolationLevel">
      <summary>指定该事务的 <see cref="T:System.Data.IsolationLevel" />。</summary>
      <returns>该事务的 <see cref="T:System.Data.IsolationLevel" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Rollback">
      <summary>从挂起状态回滚事务。</summary>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>