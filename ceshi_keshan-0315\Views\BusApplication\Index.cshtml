﻿@model List<ceshi_keshan_0315.ViewModels.BusApplicationVM>
@{
    ViewBag.Title = "公车申请管理";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>

    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - 公车管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/Site.css" rel="stylesheet" />

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        .main-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-header h1 {
            margin: 0;
            font-weight: 300;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .panel {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
        }
        .btn-group-custom {
            margin-bottom: 20px;
        }
        .footer-custom {
            margin-top: 50px;
            padding: 20px 0;
            background: #fff;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>

    <!-- 页面头部 -->
    <div class="main-header">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1><i class="glyphicon glyphicon-list"></i> @ViewBag.Title</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">查看和管理所有公车申请记录</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="container">
        <div class="breadcrumb-custom">
            <ol class="breadcrumb" style="margin: 0; background: none;">
                <li><a href="/home/<USER>"><i class="glyphicon glyphicon-home"></i> 首页</a></li>
                <li><a href="/Bus/Index">公车管理</a></li>
                <li class="active">申请管理</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-12">

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success">
                    <strong>成功：</strong> @TempData["SuccessMessage"]
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @TempData["ErrorMessage"]
                </div>
            }

                <!-- 功能按钮 -->
                <div class="btn-group-custom">
                    <a href="/BusApplication/Create" class="btn btn-primary btn-lg">
                        <i class="glyphicon glyphicon-plus"></i> 创建申请
                    </a>
                    <a href="/BusApplication/Review" class="btn btn-warning btn-lg">
                        <i class="glyphicon glyphicon-check"></i> 申请审批
                    </a>
                    <a href="/Bus/Index" class="btn btn-info btn-lg">
                        <i class="glyphicon glyphicon-list"></i> 公车列表
                    </a>
                    <a href="/home/<USER>" class="btn btn-default btn-lg">
                        <i class="glyphicon glyphicon-arrow-left"></i> 返回原系统
                    </a>
                </div>

            <!-- 数据表格 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        申请列表
                        <span class="badge">共 @(Model != null ? Model.Count : 0) 条</span>
                    </h4>
                </div>
                <div class="panel-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>申请ID</th>
                                        <th>车牌号</th>
                                        <th>车型</th>
                                        <th>申请人</th>
                                        <th>部门</th>
                                        <th>用车目的</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>状态</th>
                                        <th>申请时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model)
                                    {
                                        <tr>
                                            <td><strong>#@app.Id</strong></td>
                                            <td>@app.PlateNumber</td>
                                            <td>@app.Model</td>
                                            <td>@app.ApplicantName</td>
                                            <td>@app.Department</td>
                                            <td>@app.Purpose</td>
                                            <td>@app.StartTime.ToString("MM-dd HH:mm")</td>
                                            <td>@app.EndTime.ToString("MM-dd HH:mm")</td>
                                            <td>
                                                <span class="label label-info">
                                                    @app.StatusText
                                                </span>
                                            </td>
                                            <td>@app.CreateTime.ToString("MM-dd HH:mm")</td>
                                            <td>
                                                <a href="/BusApplication/Details/@app.Id"
                                                   class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="glyphicon glyphicon-eye-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h4>暂无数据</h4>
                            <p>没有找到申请记录</p>
                            <a href="/BusApplication/Create" class="btn btn-primary">
                                <i class="glyphicon glyphicon-plus"></i> 创建第一个申请
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <!-- 页面底部 -->
    <div class="footer-custom">
        <div class="container">
            <p>&copy; @DateTime.Now.Year 公车管理系统 - 让出行更便捷</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 表格行点击效果
            $('.table tbody tr').hover(
                function() { $(this).addClass('info'); },
                function() { $(this).removeClass('info'); }
            );

            // 自动隐藏提示消息
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
        });
    </script>
</body>
</html>