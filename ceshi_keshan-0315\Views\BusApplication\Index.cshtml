@model List<ceshi_keshan_0315.ViewModels.BusApplicationVM>
@{
    ViewBag.Title = "公车申请管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">查看和管理所有公车申请记录</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success">
                    <strong>成功：</strong> @TempData["SuccessMessage"]
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @TempData["ErrorMessage"]
                </div>
            }

            <!-- 功能按钮 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">功能操作</h4>
                </div>
                <div class="panel-body">
                    <a href="/BusApplication/Create" class="btn btn-primary">
                        <i class="glyphicon glyphicon-plus"></i> 创建申请
                    </a>
                    <a href="/BusApplication/Review" class="btn btn-warning">
                        <i class="glyphicon glyphicon-check"></i> 申请审批
                    </a>
                    <a href="/Bus/Index" class="btn btn-info">
                        <i class="glyphicon glyphicon-list"></i> 公车列表
                    </a>
                    <a href="/home/<USER>" class="btn btn-default">
                        <i class="glyphicon glyphicon-arrow-left"></i> 返回原系统
                    </a>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        申请列表
                        <span class="badge">共 @(Model != null ? Model.Count : 0) 条</span>
                    </h4>
                </div>
                <div class="panel-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>申请ID</th>
                                        <th>车牌号</th>
                                        <th>车型</th>
                                        <th>申请人</th>
                                        <th>部门</th>
                                        <th>用车目的</th>
                                        <th>开始时间</th>
                                        <th>结束时间</th>
                                        <th>状态</th>
                                        <th>申请时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var app in Model)
                                    {
                                        <tr>
                                            <td><strong>#@app.Id</strong></td>
                                            <td>@app.PlateNumber</td>
                                            <td>@app.Model</td>
                                            <td>@app.ApplicantName</td>
                                            <td>@app.Department</td>
                                            <td>@app.Purpose</td>
                                            <td>@app.StartTime.ToString("MM-dd HH:mm")</td>
                                            <td>@app.EndTime.ToString("MM-dd HH:mm")</td>
                                            <td>
                                                <span class="label label-info">
                                                    @app.StatusText
                                                </span>
                                            </td>
                                            <td>@app.CreateTime.ToString("MM-dd HH:mm")</td>
                                            <td>
                                                <a href="/BusApplication/Details/@app.Id"
                                                   class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="glyphicon glyphicon-eye-open"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h4>暂无数据</h4>
                            <p>没有找到申请记录</p>
                            <a href="/BusApplication/Create" class="btn btn-primary">
                                <i class="glyphicon glyphicon-plus"></i> 创建第一个申请
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>