<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ID="7a9b918e-7d52-44f1-bb53-b398ef759a48" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://************:4444/hx_xiaoshou_jiekou.asmx" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="hx_xiaoshou_jiekou.wsdl" MetadataType="Wsdl" ID="dd5be804-8712-4bc7-b68e-b4c28f876376" SourceId="1" SourceUrl="http://************:4444/hx_xiaoshou_jiekou.asmx?wsdl" />
    <MetadataFile FileName="hx_xiaoshou_jiekou.disco" MetadataType="Disco" ID="553985f5-aef1-4ead-9fb1-023ccab239e1" SourceId="1" SourceUrl="http://************:4444/hx_xiaoshou_jiekou.asmx?disco" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>