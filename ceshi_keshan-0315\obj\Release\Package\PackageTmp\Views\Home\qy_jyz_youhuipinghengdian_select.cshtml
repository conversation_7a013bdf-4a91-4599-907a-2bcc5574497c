﻿
@{
    ViewBag.Title = "优惠平衡点查询计算";
}



<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <link rel="stylesheet" href="styles.css">

    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title>优惠数据管理</title>
</head>
<body>
    <header>
        <h2>优惠平衡点查询计算</h2>
    </header>
    <form method="post" id="form1">
        <div class="form-group">
            <label for="youpin_name">油品名称：</label>
            <input type="text" name="youpin_name" id="youpin_name" size="15" />  
        </div>
        <div class="form-group">
            <button type="button" id="Button1">优惠平衡点查询</button>
    </form>
    <br />

    <div id="div1">
        <br />
    </div>
    <table id="shuju_1" border="1"></table>
    <footer>
        <p>&copy; 2022 优惠数据管理系统</p>
    </footer>
    <script src="scripts.js"></script>
</body>
</html>



<script>
    //$(document).ready(function)   当 DOM（document object model 文档对象模型）加载完毕且页面完全加载（包括图像）时发生 ready 事件
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/qy_jyz_youhuipinghengdian_select?youpin_name=" + $('#youpin_name').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><th>序号</th><th>油品编码</th><th>油品名称</th><th>优惠前销量升</th><th>优惠前销量吨</th><th>挂牌价（汽油-1.05）</th><th>销售吨价</th><th>销售收入（元）</th><th>优惠幅度（元/升）</th><th>优惠后销售量（吨）</th><th>优惠后销售量（升）</th><th>优惠后销售收入（元）</th><th>增幅比例</th><th>日期</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.id + '</td><td>' + item.youpin_code + '</td><td>'
                            + item.youpin_name + '</td><td>' + item.qty + '</td><td>' + item.qty_kg + '</td><td>'
                            + item.guaipai_price + '</td><td>' + item.xs_price + '</td><td>'
                            + item.xs_amount + '</td ><td>' 
                            + item.yh_fudu + '</td><td>' 
                            + item.yh_qty_kg + '</td ><td>'
                            + item.yh_qty + '</td><td>' + item.yh_xs_amount + '</td><td>' + item.zengfu_bili + '%</td ><td>' + item.workday  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });


    });
</script>
