﻿using ceshi_keshan_0315.Common;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Data.OleDb;
using MySql.Data.MySqlClient;
using System.Windows;
using System.Windows.Forms;
using MessageBox = System.Windows.MessageBox;
//using Rjb.Models;

namespace ceshi_keshan_0315.Controllers
{ 
    public class HomeController : Controller
    {
        public ActionResult Index()
        {
            ViewBag.Title = "Home Page";

            return View();
        }

        public ActionResult shouye()
        {
            ViewBag.Title = "首页";

            return View();
        }

        public ActionResult chaxun_daozhang_yujin()
        {
            ViewBag.Title = "财务到账预警报表";

            return View();
        }
        public ActionResult dianziqianbao_mingxi_chaxun()
        {
            ViewBag.Title = "电子钱包查询报表";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult caiwu_rangli_baobiao()
        {
            ViewBag.Title = "财务让利报表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_quyu_jykckblfb()
        {
            ViewBag.Title = "区域加油卡持卡比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_youzhan_jykckblfb()
        {
            ViewBag.Title = "加油站加油卡持卡比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_quyu_zffsblfb()
        {
            ViewBag.Title = "区域支付方式比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_pianqu_zffsblfb()
        {
            ViewBag.Title = "片区支付方式比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_shixian_zffsblfb()
        {
            ViewBag.Title = "市县公司支付方式比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_jiayouzhan_zffsblfb()
        {
            ViewBag.Title = "加油站支付方式比例附表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult quyu_xiaoshouribaobiao()
        {
            ViewBag.Title = "区域销售日报表（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult lingshou_jichuyuedujihuabiao()
        {
            ViewBag.Title = "加油站机出月度计划表导入（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult yingxiaojuece_yunpingtai_xscx()
        {
            ViewBag.Title = "云平台数据查询（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult yingxiaojuece_xscx()
        {
            ViewBag.Title = "营销决策日销售数据查询（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult lingshou_quyu_yejianxiaoshou()
        {
            ViewBag.Title = "区域夜间销售数据查询（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult caiwu_quyu_rangli_baobiao()
        {
            ViewBag.Title = "财务让利报表_区域片区（日期）";

            return View(); //新增界面，这里需要增加对应的方法
        }


        public ActionResult hailian_xinxishouji()
        {
            ViewBag.Title = "海炼信息收集";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult haixinquan_xiaofei_chaxun()
        {
            ViewBag.Title = "海信券销售明细查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult APP_zitidingdan_chaxun()
        {
            ViewBag.Title = "APP自提订单销售查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult tuangoudingdan_chaxun()
        {
            ViewBag.Title = "团购订单销售查询";

            return View(); //新增界面，这里需要增加对应的方法
        }
        public ActionResult haixin_xiche_zhifufangshi_huizongchaxun()
        {
            ViewBag.Title = "海信洗车商品支付方式汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }
        

        public ActionResult haixin_yanshoudan_chaxun()
        {
            ViewBag.Title = "海信验收单查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_xiaoshou_mingxi_read()
        {
            ViewBag.Title = "销售明细查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult lingshou_fengongsi_HS_baobiao()
        {
            ViewBag.Title = "分公司海上日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult FGS_tuangoudingdan_chaxun()
        {
            ViewBag.Title = "分公司团购订单查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_xiaoshou_pm()
        {
            ViewBag.Title = "海信商品销售排名（前20）";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_bianlidian_bishu()
        {
            ViewBag.Title = "海信便利店交易笔数";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_diaobodan()
        {
            ViewBag.Title = "海信调拨单";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_shangpin_xinxi()
        {
            ViewBag.Title = "海信商品信息属性查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult daoru_test()
        {
            ViewBag.Title = "财务导入测试";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult fengongsi_ribaobiao_huizong()
        {
            ViewBag.Title = "分公司日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }
        
        public ActionResult fengongsi_HS_ribaobiao_huizong()
        {
            ViewBag.Title = "分公司海上日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult HX_tuangoudan_chaxun()
        {
            ViewBag.Title = "海信团购单查询-TJ";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult shixiangongsi_ribaobiao_huizong()
        {
            ViewBag.Title = "市县公司日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult ls_youzhan_ribaobiao_huizong()
        {
            ViewBag.Title = "油站日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult ls_quansheng_ribaobiao_huizong()
        {
            ViewBag.Title = "全省日报表汇总查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult lingshou_xiumian_chaxun()
        {
            ViewBag.Title = "加油卡非忠诚用户查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult APP_shqb_xiumian()
        {
            ViewBag.Title = "APP石化钱包休眠用户查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult APP_yg_ticheng()
        {
            ViewBag.Title = "APP员工提成报表查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult youzhan_jiamanlv()
        {
            ViewBag.Title = "加油站加满率";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult youzhan_jiamanlv_baobiao()
        {
            ViewBag.Title = "加油站加满率";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult qy_fgs_youhuipinghengdian_select()
        {
            ViewBag.Title = "优惠平衡点查询";

            return View(); //新增界面，这里需要增加对应的方法
        }
        
        public ActionResult qy_jyz_youhuipinghengdian_select()
        {
            ViewBag.Title = "站级优惠平衡点查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult yehuaqi_address()
        {
            ViewBag.Title = "液化气地址信息修改";

            return View(); //新增界面，这里需要增加对应的方法
        }


        public ActionResult HX_cuxiaodan_TJ()
        {
            ViewBag.Title = "促销单销售查询";

            return View(); //新增界面，这里需要增加对应的方法
        }

        
        public ActionResult HX_cuxiaodan_time_TJ()
        {
            ViewBag.Title = "促销单销售查询-time";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult jiaokong_qyxs_baobiao()
        {
            ViewBag.Title = "交控轻油销售报表";

            return View(); //新增界面，这里需要增加对应的方法
        }

        public ActionResult yhq_lpg_jydd()
        {
            ViewBag.Title = "液化气订单查询";

            return View(); //新增界面，这里需要增加对应的方法
        }


        /// <summary>
        /// 财务到账预警报表导出
        /// </summary>
        /// <param name="workday"></param>
        /// <param name="code"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        public ActionResult ExportData(string workday = "", string code = "", string amount = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_yc;
                sql_yc = string.Format(@"
with cunk as 
  (select  youzhan.UniversalCode as Code,youzhan.ouname as name,youzhan.jine as jine,daoz.BatchDate,
  daoz.UniversalCode,(ISNULL(sum(daoz.CreditAmount),0))as amount
 from youzhan_jine_fazhi as youzhan
left join 
 ((select a.BatchDate,a.UniversalCode,a.Channel,a.CreditAmount  
	from TA_LGInputData_bak a where a.BatchDate='{0}' and  a.UniversalCode like '%333%')
		union all 	---邮储导入
	( select d.BatchDate,d.UniversalCode,d.BatchOrg,d.BatchAmount
	from TA_InputXYBankData_bak d where d.BatchDate='{1}'and  d.UniversalCode like '%333%')
union all( select convert(datetime,F1, 20)  as f1,'3335'+substring([F9],4,4) as f9 ,'中国工商银行',f4
 from  [Sheet1]  where  len(F9)<=9  and f9 not LIKE  '%[吖-座]%' and convert(datetime,F1, 20) = '{0}')
) as daoz	 --(case when convert(datetime,F1, 20)!=@@ERROR then convert(datetime,F1, 20) end)  = convert(datetime,'{0}', 20)
on youzhan.UniversalCode =daoz.UniversalCode 
 group by youzhan.UniversalCode,youzhan.ouname,youzhan.jine,daoz.BatchDate,daoz.UniversalCode
 ) 
select  cunk.Code,cunk.name,cunk.jine,isnull(cunk.BatchDate,'{2}') as BatchDate,cunk.UniversalCode,cunk.amount,(cunk.jine-cunk.amount) as chayi from cunk
 ---where (cunk.amount-cunk.jine)<0 and (cunk.jine-cunk.amount)<{3}
 ", workday, workday,workday,amount);//  -- 邮储导入
                SqlDataAdapter sda = new SqlDataAdapter(sql_yc, conn); //为存储过程名
                sda.Fill(ds, "ycdz");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "ceshi_Export";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 电子钱包销售查询导出
        /// </summary>
        /// <param name="workday"></param>
        /// <param name="code"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        public ActionResult dzqb_ExportData(string workday = "", string code = "", string amount = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
select (case when left(b.oucode,6) ='100047'then '海口'
				when left(b.oucode,6) ='100048'then  '三亚'
				when left(b.oucode,6) ='100049'then '儋州'
				when left(b.oucode,6) ='100050'then '琼海'
                when left(b.oucode,6) ='100051'then '万宁'
                when left(b.oucode,6) ='100052'then '澄迈'
				else 'zhong' end ) as quyu ,b.OUName,b.ShortName,a.workday,a.checkshift,a.gunno,a.posttc,a.qty,a.price,a.amount,a.OrderStatusTxt
   from  [Cloud].[dbo].[OSMS_Shift_SinopecWallet] a
  left join SYS_OrgUnit b on a.oucode =b.OUCode 
  where a.workday='{0}'", workday);//  -- 电子钱包导出
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "dzqb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "dzqb_Export";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 财务让利报表
        /// </summary>
        /// <param name="workday_s"></param>
        /// <param name="workday_d"></param>
        /// <param name="jinemin"></param>
        /// <param name="jinemax"></param>
        /// <param name="GasName"></param>
        /// <param name="nodename"></param>
        /// <param name="ouname"></param>
        /// <returns></returns>
        public ActionResult cwrl_ExportData(string workday_s = "", string workday_d = "", string jinemin = "", string jinemax = "", string GasName = "", string nodename = "", string ouname = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_cwrl = new DataSet();
            try
            {
                conn.Open();
                string sql_cwrl;
                sql_cwrl = string.Format(@"
                declare @workday_s datetime 
                declare @workday_d datetime
                declare @jinemin numeric
                declare @jinemax numeric
                set @workday_s='{0}'
                set @workday_d='{1}'
                set @jinemin='{2}'
                set @jinemax='{3}'
                begin
                if (@workday_s<=@workday_d)
                begin
	                SELECT kg.BigAreaName,kg.BranchName,kg.AreaName,ca.SFDAT,ca.OUName,ca.STNID,ca.MATNR,ca.POSNR,ca.QUANTITY,ca.PRICE,ca.quanpr,ca.AMOUNT,ca.chajia,ca.jine,ca.NodeName,ca.guapai
	                FROM caiwu_rangli as ca  inner join 
		                (select BigAreaName,BranchName,AreaName,UniversalCode from  OSMS_Daily_Sales_lg where WorkDay>=@workday_s and WorkDay<=@workday_d
		                group by BigAreaName,BranchName,AreaName,UniversalCode) as kg 
		                on  ca.STNID=kg.UniversalCode
	                where MATNR like '%{4}%'  and SFDAT >=@workday_s and SFDAT<=@workday_d and ca.jine>=@jinemin and ca.chajia<=@jinemax 
                         and nodename like '%{5}%'   and OUName like '%{6}%' 
                end
                end
                ", workday_s, workday_d, jinemin, jinemax, GasName, nodename, ouname);//  -- 电子钱包导出
                SqlDataAdapter sda = new SqlDataAdapter(sql_cwrl, conn); //为存储过程名
                sda.Fill(ds_cwrl, "cwrl");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_cwrl);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "cwrl_Export";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-1-**************
        /// <summary>
        /// 零售区域加油卡比例附表导出
        /// </summary>
        /// <param name="workday_s"></param>
        /// <param name="workday_d"></param>
        /// <returns></returns>
        public ActionResult lingshou_quyu_jykckblfb_ExportData(string workday_s = "", string workday_d = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
declare @V_KSRQ datetime
declare @V_JZRQ datetime
set @V_KSRQ = '{0}' /*开始日期*/
set @V_JZRQ = '{1}'/*截止日期*/
BEGIN
SELECT  -- ROW_NUMBER() OVER ( ORDER BY T.BigAreaName DESC ) 'rank' ,
                T.*
        FROM    ( SELECT    t1.BigAreaName BigAreaName,/*区域名称*/
                             ROUND(ISNULL(t1.ckxfl, 0),2) 'ckxfl' ,/*持卡消费量*/
                            ROUND(ISNULL(t2.zxl, 0),2) 'zxl' ,/*总销量*/
                            ROUND(ISNULL(t1.ckxfl / NULLIF(t2.zxl, 0), 0),2) 'ckbl' ,/*持卡比例*/
                            ROUND(ISNULL(t3.cyick, 0),2) 'cyick' ,/*柴油IC卡*/
                            ROUND(ISNULL(t4.cyzl, 0),2) 'cyzl' ,/*柴油总量*/
                            ROUND(ISNULL(t3.cyick / NULLIF(t4.cyzl, 0), 0),2) 'cyckbl' ,/*柴油持卡比例*/
                            ROUND(ISNULL(t5.qyick, 0),2) 'qyick' ,/*汽油IC卡*/
                            ROUND(ISNULL(t6.qyzl, 0),2) 'qyzl' ,/*汽油总量*/
                            ROUND(ISNULL(t5.qyick / NULLIF(t6.qyzl, 0), 0),2) 'qyckbl' ,/*汽油持卡比例*/
                            ROUND(ISNULL(t7.qyick92, 0),2) 'qyick92' ,/*92号汽油IC卡*/
                            ROUND(ISNULL(t8.qyzl92, 0),2) 'qyzl92' ,/*92号汽油总量*/
                            ROUND(ISNULL(t7.qyick92 / NULLIF(t8.qyzl92, 0), 0),2) 'qyckbl92' ,/*92号汽油持卡比例*/
                            ROUND(ISNULL(t9.qyick95, 0),2) 'qyick95' ,/*95号汽油IC卡*/
                            ROUND(ISNULL(t10.qyzl95, 0),2) 'qyzl95' ,/*95号汽油总量*/
                            ROUND(ISNULL(t9.qyick95 / NULLIF(t10.qyzl95, 0), 0),2) 'qyckbl95' ,/*95号汽油持卡比例*/
                            ROUND(ISNULL(t11.qyick98, 0),2) 'qyick98' ,/*98号汽油IC卡*/
                            ROUND(ISNULL(t12.qyzl98, 0),2) 'qyzl98' ,/*98号汽油总量*/
                            ROUND(ISNULL(t11.qyick98 / NULLIF(t12.qyzl98, 0), 0),2) 'qyckbl98'/*98号汽油持卡比例*/
                  FROM      ( ---持卡消费量
                              SELECT    BigAreaCode ,
                                        BigAreaName ,
                                        SUM(SalesVol) 'ckxfl'
                              FROM      dbo.OSMS_Daily_Sales_lg
                              WHERE     DataType = '01'
                                        AND CheckMode = '012007'
                                        AND WorkDay >= @V_KSRQ
                                        AND WorkDay <= @V_JZRQ
										--AND GasCode <> '60078231'
                                        AND SalesMode = '01'
										AND Category IN('01','02')
                              GROUP BY  BigAreaCode ,
                                        BigAreaName
                            ) t1
                            LEFT JOIN ( ---总销量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'zxl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
										        AND Category IN('01','02')
                                        GROUP BY BigAreaCode
                                      ) t2 ON t1.BigAreaCode = t2.BigAreaCode
                            LEFT JOIN ( ---柴油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'cyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t3 ON t1.BigAreaCode = t3.BigAreaCode
                            LEFT JOIN (---柴油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'cyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t4 ON t1.BigAreaCode = t4.BigAreaCode
                            LEFT JOIN ( ---汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t5 ON t1.BigAreaCode = t5.BigAreaCode
                            LEFT JOIN (---汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t6 ON t1.BigAreaCode = t6.BigAreaCode
                            LEFT JOIN ( ---92号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t7 ON t1.BigAreaCode = t7.BigAreaCode
                            LEFT JOIN ( ---92号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t8 ON t1.BigAreaCode = t8.BigAreaCode
                            LEFT JOIN ( ---95号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t9 ON t1.BigAreaCode = t9.BigAreaCode
                            LEFT JOIN ( ---95号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t10 ON t1.BigAreaCode = t10.BigAreaCode
                            LEFT JOIN ( ---98号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t11 ON t1.BigAreaCode = t11.BigAreaCode
                            LEFT JOIN ( ---98号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t12 ON t1.BigAreaCode = t12.BigAreaCode
                ) T;
				
                end", workday_s, workday_d);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_quyu_jykckblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_quyu_jykckblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-2-**************
        /// <summary>
        /// 零售加油站加油卡比例附表导出
        /// </summary>
        /// <param name="workday_s1"></param>
        /// <param name="workday_d1"></param>
        /// <returns></returns>
        public ActionResult lingshou_youzhan_jykckblfb_ExportData(string workday_s1 = "", string workday_d1 = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
declare @V_KSRQ datetime
declare @V_JZRQ datetime
set @V_KSRQ = '{0}' /*开始日期*/
set @V_JZRQ = '{1}'/*截止日期*/
BEGIN
SELECT  
                T.ShortName as '油站名称' ,T.ckxfl as '持卡消费量' ,T.zxl  as '总销量',T.ckbl as '持卡比例',T.cyick as '柴油IC卡' ,T.cyzl as '柴油总量' ,T.cyckbl as '柴油持卡比例' ,T.qyick as '汽油IC卡',T.qyzl as '汽油总量' ,T.qyckbl as '汽油持卡比例' ,T.qyick92 as '92号汽油IC卡',T.qyzl92 as '92号汽油总量' ,
                T.qyckbl92 as '92号汽油持卡比例',T.qyick95 as '95号汽油IC卡' ,T.qyzl95  as '95号汽油总量',T.qyckbl95 as '95号汽油持卡比例' ,T.qyick98 as '98号汽油IC卡',T.qyzl98 as '98号汽油总量' ,T.qyckbl98 as '98号汽油持卡比例'
        FROM    ( SELECT    t1.ShortName ShortName,/*区域名称*/
                            ROUND(ISNULL(t1.ckxfl, 0),2) 'ckxfl' ,/*持卡消费量*/
                            ROUND(ISNULL(t2.zxl, 0),2) 'zxl' ,/*总销量*/
                            ROUND(ISNULL(t1.ckxfl / NULLIF(t2.zxl, 0), 0),4)*100 'ckbl' ,/*持卡比例*/
                            ROUND(ISNULL(t3.cyick, 0),2) 'cyick' ,/*柴油IC卡*/
                            ROUND(ISNULL(t4.cyzl, 0),2) 'cyzl' ,/*柴油总量*/
                            ROUND(ISNULL(t3.cyick / NULLIF(t4.cyzl, 0), 0),4)*100 'cyckbl' ,/*柴油持卡比例*/
                            ROUND(ISNULL(t5.qyick, 0),2) 'qyick' ,/*汽油IC卡*/
                            ROUND(ISNULL(t6.qyzl, 0),2) 'qyzl' ,/*汽油总量*/
                            ROUND(ISNULL(t5.qyick / NULLIF(t6.qyzl, 0), 0),4)*100 'qyckbl' ,/*汽油持卡比例*/
                            ROUND(ISNULL(t7.qyick92, 0),2) 'qyick92' ,/*92号汽油IC卡*/
                            ROUND(ISNULL(t8.qyzl92, 0),2) 'qyzl92' ,/*92号汽油总量*/
                            ROUND(ISNULL(t7.qyick92 / NULLIF(t8.qyzl92, 0), 0),4)*100 'qyckbl92' ,/*92号汽油持卡比例*/
                            ROUND(ISNULL(t9.qyick95, 0),2) 'qyick95' ,/*95号汽油IC卡*/
                            ROUND(ISNULL(t10.qyzl95, 0),2) 'qyzl95' ,/*95号汽油总量*/
                            ROUND(ISNULL(t9.qyick95 / NULLIF(t10.qyzl95, 0), 0),4)*100 'qyckbl95' ,/*95号汽油持卡比例*/
                            ROUND(ISNULL(t11.qyick98, 0),2) 'qyick98' ,/*98号汽油IC卡*/
                            ROUND(ISNULL(t12.qyzl98, 0),2) 'qyzl98' ,/*98号汽油总量*/
                            ROUND(ISNULL(t11.qyick98 / NULLIF(t12.qyzl98, 0), 0),4)*100 'qyckbl98'/*98号汽油持卡比例*/
                  FROM      ( ---持卡消费量
                              SELECT    UniversalCode ,
                                        ShortName ,
                                        SUM(SalesVol) 'ckxfl'
                              FROM      dbo.OSMS_Daily_Sales_lg
                              WHERE     DataType = '01'
                                        AND CheckMode = '012007'
                                        AND WorkDay >= @V_KSRQ
                                        AND WorkDay <= @V_JZRQ
										--AND GasCode <> '60078231'
                                        AND SalesMode = '01'
										AND Category IN('01','02')
                              GROUP BY  UniversalCode ,
                                        ShortName
                            ) t1
                            LEFT JOIN ( ---总销量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'zxl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
										        AND Category IN('01','02')
                                        GROUP BY UniversalCode
                                      ) t2 ON t1.UniversalCode = t2.UniversalCode
                            LEFT JOIN ( ---柴油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'cyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t3 ON t1.UniversalCode = t3.UniversalCode
                            LEFT JOIN (---柴油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'cyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t4 ON t1.UniversalCode = t4.UniversalCode
                            LEFT JOIN ( ---汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t5 ON t1.UniversalCode = t5.UniversalCode
                            LEFT JOIN (---汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t6 ON t1.UniversalCode = t6.UniversalCode
                            LEFT JOIN ( ---92号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t7 ON t1.UniversalCode = t7.UniversalCode
                            LEFT JOIN ( ---92号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t8 ON t1.UniversalCode = t8.UniversalCode
                            LEFT JOIN ( ---95号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t9 ON t1.UniversalCode = t9.UniversalCode
                            LEFT JOIN ( ---95号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t10 ON t1.UniversalCode = t10.UniversalCode
                            LEFT JOIN ( ---98号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t11 ON t1.UniversalCode = t11.UniversalCode
                            LEFT JOIN ( ---98号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t12 ON t1.UniversalCode = t12.UniversalCode
                ) T where t.ShortName IS NOT NULL;
				
                end", workday_s1, workday_d1);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_youzhan_jykckblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_youzhan_jykckblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-3-3-3**************
        /// <summary>
        /// 零售分公司支付方式比例附表导出
        /// </summary>
        /// <param name="workday_shh"></param>
        /// <param name="workday_dhh"></param>
        /// <returns></returns>
        public ActionResult lingshou_quyu_zffsblfb_ExportData(string workday_shh = "", string workday_dhh = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
with kkk as (
select 
(case when left(o.oucode,6) ='100047'then '海口'
				when left(o.oucode,6) ='100048'then  '三亚'
				when left(o.oucode,6) ='100049'then '儋州'
				when left(o.oucode,6) ='100050'then '琼海'
                when left(o.oucode,6) ='100051'then '万宁'
                when left(o.oucode,6) ='100052'then '澄迈'
        else  '其他' 
end ) as quyu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl_1
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl_2
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl_3
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl

,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2) dzqbxl,sum( a.QUANTITY) zxl_5
,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) dzqbbl
,sum( a.QUANTITY) zxl_6
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052')
GROUP BY (case when left(o.oucode,6) ='100047'then '海口'
				when left(o.oucode,6) ='100048'then  '三亚'
				when left(o.oucode,6) ='100049'then '儋州'
				when left(o.oucode,6) ='100050'then '琼海'
                when left(o.oucode,6) ='100051'then '万宁'
                when left(o.oucode,6) ='100052'then '澄迈'
        else  '其他' 
end ) 
union all 
(select '汇总' quyu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl_1
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl_2
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl_3
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl
,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl_5
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY)) dzqbbl
,sum( a.QUANTITY) zxl_6  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052') )
)
select kkk.quyu as '区域名称',kkk.wxzfxl as '微信支付销量',kkk.zxl as '总销量',kkk.wxzfbl as '微信支付比例'
,kkk.zfbxl as '支付宝销量',kkk.zxl_1 as '总销量',kkk.zfbbl as '支付宝比例'
,kkk.yhkxl as '银行卡销量',kkk.zxl_2 as '总销量',kkk.yhkbl as '银行卡比例'
,kkk.jykxl as '加油卡销量',kkk.zxl_7 as '总销量',kkk.jykbl as '加油卡比例'
,kkk.yhqxl as '优惠券销量',kkk.zxl_3 as '总销量',kkk.yhqbl as '优惠券比例'
,kkk.xjxl as '现金销量',kkk.zxl_4 as '总销量',kkk.xjbl as '现金比例'
,kkk.dzqbxl as '电子钱包销量',kkk.zxl_5 as '总销量',kkk.dzqbbl as '电子钱包比例',kkk.zxl_6  as '总销量'
from  kkk
end   

                ", workday_shh, workday_dhh);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_quyu_zffsblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_quyu_zffsblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-4-4-4**************
        /// <summary>
        /// 零售市县支付方式比例附表导出
        /// </summary>
        /// <param name="workday_ssx"></param>
        /// <param name="workday_dsx"></param>
        /// <returns></returns>
        public ActionResult lingshou_shixian_zffsblfb_ExportData(string workday_ssx = "", string workday_dsx = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
  
declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
with kkk as (
select 
(case  when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end ) as shixian
,isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2),0) wxzfxl,sum( a.QUANTITY) zxl
,(case when sum(a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum(a.QUANTITY)=0 then 1 else sum(a.QUANTITY) end),4),0)*100 end) wxzfbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2),0) zfbxl,sum( a.QUANTITY) zxl_1
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) zfbbl
,isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2),0) yhkxl,sum( a.QUANTITY) zxl_2
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhkbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2),0) yhqxl,sum( a.QUANTITY) zxl_3
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhqbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) jykxl,sum( a.QUANTITY) zxl_4
,(case when sum( a.QUANTITY)=0 then 0 else  isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),2),0)*100 end) jykbl
,isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) xjxl,sum( a.QUANTITY) zxl_7
,(case when sum( a.QUANTITY) =0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY) =0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) xjbl
,isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2),0) dzqbxl,sum( a.QUANTITY) zxl_5
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end)  dzqbbl
,sum( a.QUANTITY) zxl_6
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = o.UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052')
GROUP BY (case  when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end ) 
union all 
(select '汇总' shixian
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl_1
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl_2
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl_3
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl

,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl_5
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl_6  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = o.UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052'))
)
select kkk.shixian as '区域名称',kkk.wxzfxl as '微信支付销量',kkk.zxl as '总销量',kkk.wxzfbl as '微信支付比例',kkk.zfbxl as '支付宝销量'
,kkk.zxl_1 as '总销量',kkk.zfbbl as '支付宝比例',kkk.yhkxl as '银行卡销量',kkk.zxl_2 as '总销量',kkk.yhkbl as '银行卡比例'
,kkk.yhqxl as '优惠券销量',kkk.zxl_3 as '总销量',kkk.yhqbl as '优惠券比例',kkk.xjxl as '现金销量',kkk.zxl_4 as '总销量'
,kkk.xjbl as '现金比例',kkk.jykxl as '加油卡销量',kkk.zxl_7 as '总销量',kkk.jykbl as '加油卡比例'
,kkk.dzqbxl as '电子钱包销量',kkk.zxl_5 as '总销量',kkk.dzqbbl as '电子钱包比例',kkk.zxl_6  as '总销量'
from  kkk
end    

                ", workday_ssx, workday_dsx);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_shixian_zffsblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_shixian_zffsblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-5-5-5**************
        /// <summary>
        /// 零售片区支付方式比例附表导出
        /// </summary>
        /// <param name="workday_spq"></param>
        /// <param name="workday_dpq"></param>
        /// <returns></returns>
        public ActionResult lingshou_pianqu_zffsblfb_ExportData(string workday_spq = "", string workday_dpq = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
with kkk as (
select 
(case when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end ) as pianqu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl_1
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl_2
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl_3
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl

,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2) dzqbxl,sum( a.QUANTITY) zxl_5
,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2)*100 dzqbbl
,sum( a.QUANTITY) zxl_6
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052')
GROUP BY (case when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end )
union all 
(select '汇总' pianqu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl_1
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl_2
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl_3
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) yhqbl


,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39')  then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl

,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl_5
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl_6  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052'))
)
select kkk.pianqu as '片区',kkk.wxzfxl as '微信支付销量',kkk.zxl as '总销量',kkk.wxzfbl as '微信支付比例',kkk.zfbxl as '支付宝销量'
,kkk.zxl_1 as '总销量',kkk.zfbbl as '支付宝比例',kkk.yhkxl as '银行卡销量',kkk.zxl_2 as '总销量',kkk.yhkbl as '银行卡比例'
,kkk.yhqxl as '优惠券销量',kkk.zxl_3 as '总销量',kkk.yhqbl as '优惠券比例'
,kkk.jykxl as '加油卡销量',kkk.zxl_7 as '总销量',kkk.jykbl as '加油卡比例'
,kkk.xjxl as '现金销量',kkk.zxl_4 as '总销量',kkk.xjbl as '现金比例'
,kkk.dzqbxl as '电子钱包销量',kkk.zxl_5 as '总销量',kkk.dzqbbl as '电子钱包比例',kkk.zxl_6  as '总销量'
from  kkk
end   
     
                ", workday_spq, workday_dpq);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_pianqu_zffsblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_pianqu_zffsblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }
        
        //***********-6-6-**************
        /// <summary>
        /// 零售加油站支付方式比例附表导出
        /// </summary>
        /// <param name="workday_sjyz"></param>
        /// <param name="workday_djyz"></param>
        /// <returns></returns>
        public ActionResult lingshou_jiayouzhan_zffsblfb_ExportData(string workday_sjyz = "", string workday_djyz = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
with kkk as (  
select 
a.STNID as STNID,
o.ShortName as youzhan
,isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2),0) wxzfxl,sum( a.QUANTITY) zxl
,(case when sum(a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum(a.QUANTITY)=0 then 1 else sum(a.QUANTITY) end),4),0)*100 end) wxzfbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2),0) zfbxl,sum( a.QUANTITY) zxl_1
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) zfbbl
,isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2),0) yhkxl,sum( a.QUANTITY) zxl_2
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhkbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2),0) yhqxl,sum( a.QUANTITY) zxl_3
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhqbl
,isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) jykxl,sum( a.QUANTITY) zxl_4
,(case when sum( a.QUANTITY)=0 then 0 else  isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),2),0)*100 end) jykbl
,isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) xjxl,sum( a.QUANTITY) zxl_7
,(case when sum( a.QUANTITY) =0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY) =0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) xjbl
,isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2),0) dzqbxl,sum( a.QUANTITY) zxl_5
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end)  dzqbbl
,sum( a.QUANTITY) zxl_6
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052')
GROUP BY a.STNID,o.ShortName
union all 
(select  ' ' as STNID,'汇总' youzhan,
sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) wxzfxl,sum( a.QUANTITY) zxl
,(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 wxzfbl
,sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) zfbxl,sum( a.QUANTITY) zxl_1
,(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 zfbbl
,sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) yhkxl,sum( a.QUANTITY) zxl_2
,(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 yhkbl
,sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) yhqxl,sum( a.QUANTITY) zxl_3
,(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl_7
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl_4
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2) xjbl

,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl_5
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl_6  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052'))
)
select kkk.STNID as '油站编码',kkk.youzhan as '加油站名称',kkk.wxzfxl as '微信支付销量',kkk.zxl as '总销量',kkk.wxzfbl as '微信支付比例',kkk.zfbxl as '支付宝销量'
,kkk.zxl_1 as '总销量',kkk.zfbbl as '支付宝比例',kkk.yhkxl as '银行卡销量',kkk.zxl_2 as '总销量',kkk.yhkbl as '银行卡比例'
,kkk.yhqxl as '优惠券销量',kkk.zxl_3 as '总销量',kkk.yhqbl as '优惠券比例'
,kkk.jykxl as '加油卡销量',kkk.zxl_7 as '总销量',kkk.jykbl as '加油卡比例'
,kkk.xjxl as '现金销量',kkk.zxl_4 as '总销量',kkk.xjbl as '现金比例'
,kkk.dzqbxl as '电子钱包销量',kkk.zxl_5 as '总销量',kkk.dzqbbl as '电子钱包比例',kkk.zxl_6  as '总销量'
from  kkk
end            
                ", workday_sjyz, workday_djyz);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_jiayouzhan_zffsblfb");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_jiayouzhan_zffsblfb_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-7-**************
        /// <summary>
        /// 区域销售日报表导出
        /// </summary>
        /// <param name="workday_sqyxsrbb"></param>
        /// <returns></returns>
        public ActionResult quyu_xiaoshouribaobiao_ExportData(string workday_sqyxsrbb = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
  
declare @V_RQ datetime
declare @V_OUCode VARCHAR(30)
 set  @V_RQ = '{0}' 
 set  @V_OUCode = '' --查看数据级别OUCode

    BEGIN

        DECLARE @v_jsrq DATE ,
            @v_scts DECIMAL ,/*生产天数*/
            @v_dyzts DECIMAL ,/*总天数*/
            @v_ycrq DATE ,/*开始日期月份的月初日期*/
            @v_ymrq DATE ,/*结束日期月份的月末日期*/
            @v_where_jyfs VARCHAR(50) = '-1' ,/*经营方式where条件变量*/
            @v_where_hzfs VARCHAR(50)= '-1' ,/*汇总方式where条件变量*/
            @v_ksny VARCHAR(6) , /*开始年月*/
            @v_jsny VARCHAR(6) ,/*结束年月*/
            @v_ncrq DATE ,/*年初日期*/
            @v_tbrq DATE ,
            @v_hbrq DATE; 

        SET @v_jsrq = CONVERT(DATE, @V_RQ); 
        SET @v_ncrq = DATEADD(yy, DATEDIFF(yy, 0, @v_jsrq), 0);  /*年初日期*/
        SET @v_ycrq = DATEADD(DD, -DAY(@v_jsrq) + 1, @v_jsrq); /*月初日期*/
        SET @v_ymrq = DATEADD(DD, -DAY(DATEADD(M, 1, @v_jsrq)),
                              DATEADD(M, 1, @v_jsrq));/*月末日期*/
        SET @v_scts = DAY(@v_jsrq);--DATEDIFF(DAY, @V_KSRQ, @V_JSRQ) + 1; /*生产天数*/
        SET @v_dyzts = DAY(@v_ymrq); --DATEDIFF(DAY, @v_ksrq_ycrq, @v_jsrq_ymrq) + 1;/*总天数*/

        SET @v_ksny = CONVERT(VARCHAR(6), @v_ncrq, 112); /*开始年月*/
        SET @v_jsny = CONVERT(VARCHAR(6), @v_jsrq, 112);/*结束年月*/
        SET @v_tbrq = DATEADD(YEAR, -1, @v_jsrq);
        SET @v_hbrq = DATEADD(MONTH, -1, @v_jsrq);

        SET @V_OUCode = @V_OUCode + '%';

        SELECT  --ROW_NUMBER() OVER ( ORDER BY T.dysjwcjd DESC ) 'rank' ,
                case when T.BigAreaName='海口分公司' then '1' 
													when T.BigAreaName='三亚分公司' then '2' 
													when T.BigAreaName='儋州分公司' then '3'
													when T.BigAreaName='琼海分公司' then '4'
													when T.BigAreaName='万宁分公司' then '5'
													when T.BigAreaName='澄迈分公司' then '6'
													else '0' end as hhk,
                T.BigAreaName AS '区域名称',T.ywcjd as 'ywcjd',T.dyMonSalesPlan as '当月计划量',T.dyyrj as '当月应日均',T.dysjrj as '当月实际日均',T.dyywcjd as '应完成进度',T.dysjwcjd as '实际完成进度',T.dyqjd as '欠进度',T.lrMonthlySales as '当月累日销量合计',T.lr92 as '当月累日销量92#',T.lr95 as '当月累日销量95#',T.lr98 as '当月累日销量98#',T.lr0 as '当月累日销量0#',T.lrrly as '当月累日销量燃料油',T.lrCNG as '当月累日销量CNG',
                T.lrLNG as '当月累日销量LNG',T.dyqy as '当月汽油总销量',T.dycy as '当月柴油总销量',T.drSalesVol as '当日销量合计',T.dr92 as '当日销量92',T.dr95 as '当日销量95',T.dr98 as '当日销量98',T.dr0 as '当日销量',T.drrly as '当日销量燃料油',T.drCNG as '当日销量CNG',T.drLNG as '当日销量LNG',T.tqMonthlySales as '同比情况同期销量总量',T.tq92 as '同比情况同期销量92',
                T.tq95 as '同比情况同期销量95',T.tq98 as '同比情况同期销量98',T.tq0 as '同比情况同期销量0',T.tqrly as '同比情况同期销量燃料油',T.tbzfzl as '同比增幅总量',T.tbzfqy as '同比增幅汽油',T.tbzfcy as '同比增幅柴油',T.tbzfrly as '同比增幅燃料油',T.tqqy as '同期汽油总销量',T.tqcy as '同期柴油总销量',T.tbzl as '同比总量',T.tbqy as '同比汽油',
                T.tbcy as '同比柴油',T.tbrly as '同比燃料油',T.syMonthlySales as '上月环比总销量',T.sy92 as '上月环比92#销量',T.sy95 as '上月环比95#销量',T.sy98 as '上月环比92#销量',T.sy0 as '上月环比92#销量',T.syrly as '上月环比燃料油销量',T.hbzfzl as '环比增幅总量',T.hbzfqy as '环比增幅汽油',T.hbzfcy as '环比增幅柴油',T.hbzfrly as '环比增幅燃料油',
                T.syqy as '上月汽油总销量',T.sycy as '上月柴油总销量',T.hbzl as '环比总量',T.hbqy as '环比汽油',T.hbcy as '环比柴油',T.hbrly as '环比燃料油'
        FROM    ( SELECT  DISTINCT  T5.BigAreaName BigAreaName ,/*片区名称*/
                            
                            ROUND(@v_scts / @v_dyzts,6) 'ywcjd' ,
                            ROUND(ISNULL(t2.dyMonSalesPlan, 0),6) 'dyMonSalesPlan' ,/*当月计划量*/
                            ROUND(ISNULL(t2.dyyrj, 0),6) 'dyyrj' ,/*当月应日均*/
                            ROUND(ISNULL(t1.dysjrj, 0),6) 'dysjrj' ,/*当月实际日均*/
                            ROUND(ISNULL(t2.dyywcjd, 0),6) 'dyywcjd' ,/*应完成进度*/
                            ROUND(ISNULL(t1.dyMonthlySales
                                   / NULLIF(t2.dyMonSalesPlan, 0), 0),6) 'dysjwcjd' ,/*实际完成进度*/
                            ROUND(ISNULL(t1.dyMonthlySales
                                   / NULLIF(t2.dyMonSalesPlan, 0) - t2.dyywcjd,
                                   0),6) 'dyqjd' , /*欠进度*/
                            ROUND(ISNULL(t1.dyMonthlySales, 0),6) 'lrMonthlySales' ,/*当月累日销量合计*/
                            ROUND(ISNULL(t1.lr92, 0),6) 'lr92' ,/*当月累日销量92#*/
                            ROUND(ISNULL(t1.lr95, 0),6) 'lr95' ,/*当月累日销量95#*/
                            ROUND(ISNULL(t1.lr98, 0),6) 'lr98' ,/*当月累日销量98#*/
                            ROUND(ISNULL(t1.lr0, 0),6) 'lr0' ,/*当月累日销量0#*/
                            ROUND(ISNULL(t1.lrrly, 0),6) 'lrrly' ,/*当月累日销量燃料油*/
                            ROUND(ISNULL(t1.lrCNG, 0),6) 'lrCNG' ,/*当月累日销量CNG*/
                            ROUND(ISNULL(t1.lrLNG, 0),6) 'lrLNG' ,/*当月累日销量LNG*/
                            ROUND(ISNULL(t1.dyqy, 0),6) 'dyqy' ,/*当月汽油总销量*/
                            ROUND(ISNULL(t1.dycy, 0),6) 'dycy' ,/*当月柴油总销量*/
                            ROUND(ISNULL(t1.drSalesVol, 0),6) 'drSalesVol' ,/*当日销量合计*/
                            ROUND(ISNULL(t1.dr92, 0),6) 'dr92' ,/*当日销量92#*/
                            ROUND(ISNULL(t1.dr95, 0),6) 'dr95' ,/*当日销量95#*/
                            ROUND(ISNULL(t1.dr98, 0),6) 'dr98' ,/*当日销量98#*/
                            ROUND(ISNULL(t1.dr0, 0),6) 'dr0' ,/*当日销量dr0*/
                            ROUND(ISNULL(t1.drrly, 0),6) 'drrly' ,/*当日销量燃料油*/
                            ROUND(ISNULL(t1.drCNG, 0),6) 'drCNG' ,/*当日销量CNG*/
                            ROUND(ISNULL(t1.drLNG, 0),6) 'drLNG' ,/*当日销量LNG*/
                            ROUND(ISNULL(t3.dyMonthlySales, 0),6) 'tqMonthlySales' ,/*同比情况同期销量总量*/
                            ROUND(ISNULL(t3.lr92, 0),6) 'tq92' ,/*同比情况同期销量92#*/
                            ROUND(ISNULL(t3.lr95, 0),6) 'tq95' ,/*同比情况同期销量95#*/
                            ROUND(ISNULL(t3.lr98, 0),6) 'tq98' ,/*同比情况同期销量98#*/
                            ROUND(ISNULL(t3.lr0, 0),6) 'tq0' ,/*同比情况同期销量0#*/
                            ROUND(ISNULL(t3.lrrly, 0),6) 'tqrly' ,/*同比情况同期销量燃料油*/
                            ROUND(ISNULL(t1.dyMonthlySales - t3.dyMonthlySales, 0),6) 'tbzfzl' ,/*同比增幅总量*/
                            ROUND(ISNULL(t1.dyqy - t3.dyqy, 0),6) 'tbzfqy' ,/*同比增幅汽油*/
                            ROUND(ISNULL(t1.dycy - t3.dycy, 0),6) 'tbzfcy' ,/*同比增幅柴油*/
                            ROUND(ISNULL(t1.lrrly - t3.lrrly, 0),6) 'tbzfrly' , /*同比增幅燃料油*/
                            ROUND(ISNULL(t3.dyqy, 0),6) 'tqqy' ,/*同期汽油总销量*/
                            ROUND(ISNULL(t3.dycy, 0),6) 'tqcy' ,/*同期柴油总销量*/
                            ROUND(ISNULL(( t1.dyMonthlySales - t3.dyMonthlySales )
                                   / NULLIF(t3.dyMonthlySales, 0), 0),6) 'tbzl' ,/*同比总量*/
                            ROUND(ISNULL(( t1.dyqy - t3.dyqy ) / NULLIF(t3.dyqy, 0),
                                   0),6) 'tbqy' ,/*同比汽油*/
                            ROUND(ISNULL(( t1.dycy - t3.dycy ) / NULLIF(t3.dycy, 0),
                                   0),6) 'tbcy' ,/*同比柴油*/
                            ROUND(ISNULL(( t1.lrrly - t3.lrrly ) / NULLIF(t3.lrrly,
                                                              0), 0),6) 'tbrly' , /*同比燃料油*/
                            ROUND(ISNULL(t4.dyMonthlySales, 0),6) 'syMonthlySales' ,/*上月环比总销量*/
                            ROUND(ISNULL(t4.lr92, 0),6) 'sy92' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lr95, 0),6) 'sy95' ,/*上月环比95#销量*/
                            ROUND(ISNULL(t4.lr98, 0),6) 'sy98' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lr0, 0),6) 'sy0' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lrrly, 0),6) 'syrly' ,/*上月环比燃料油销量*/
                            ROUND(ISNULL(t1.dyMonthlySales - t4.dyMonthlySales, 0),6) 'hbzfzl' ,/*环比增幅总量*/
                            ROUND(ISNULL(t1.dyqy - t4.dyqy, 0),6) 'hbzfqy' ,/*环比增幅汽油*/
                            ROUND(ISNULL(t1.dycy - t4.dycy, 0),6) 'hbzfcy' ,/*环比增幅柴油*/
                            ROUND(ISNULL(t1.lrrly - t4.lrrly, 0),6) 'hbzfrly' ,/*环比增幅燃料油*/
                            ROUND(ISNULL(t4.dyqy, 0),6) 'syqy' ,/*上月汽油总销量*/
                            ROUND(ISNULL(t4.dycy, 0),6) 'sycy' ,/*上月柴油总销量*/
                            ROUND(ISNULL(( t1.dyMonthlySales - t4.dyMonthlySales )
                                   / NULLIF(t4.dyMonthlySales, 0), 0),6) 'hbzl' ,/*环比总量*/
                            ROUND(ISNULL(( t1.dyqy - t4.dyqy ) / NULLIF(t4.dyqy, 0),
                                   0),6) 'hbqy' ,/*环比汽油*/
                            ROUND(ISNULL(( t1.dycy - t4.dycy ) / NULLIF(t4.dycy, 0),
                                   0),6) 'hbcy' ,/*环比柴油*/
                            ROUND(ISNULL(( t1.lrrly - t4.lrrly ) / NULLIF(t4.lrrly,
                                                              0), 0),6) 'hbrly'/*环比燃料油*/
                  FROM      ( --当月销售情况
                              SELECT    BigAreaCode ,
                                        SUM(CASE WHEN Category IN ( '01', '02' )
                                                 THEN MonthlySales
                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                        SUM(CASE WHEN Category IN ( '01' )
                                                 THEN MonthlySales
                                            END) 'dyqy' , /*当月汽油*/
                                        SUM(CASE WHEN b.Extend1 IN ( '0#' )
                                                 THEN MonthlySales
                                            END) 'dycy' ,/*当月柴油*/
                                        SUM(CASE WHEN Category IN ( '01', '02' )
                                                 THEN MonthlySales
                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                        SUM(CASE WHEN b.Extend1 = '92#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr92' ,
                                        SUM(CASE WHEN b.Extend1 = '95#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr95' ,
                                        SUM(CASE WHEN b.Extend1 = '98#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr98' ,
                                        SUM(CASE WHEN b.Extend1 = '0#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr0' ,
                                        SUM(CASE WHEN b.Extend1 = 'RLY'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrrly' ,
                                        SUM(CASE WHEN b.Extend1 = 'CNG'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrCNG' ,
                                        SUM(CASE WHEN b.Extend1 = 'LNG'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrLNG' ,
                                        SUM(CASE WHEN a.Category IN ( '01',
                                                              '02' )
                                                 THEN SalesVol
                                            END) 'drSalesVol' ,
                                        SUM(CASE WHEN b.Extend1 = '92#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr92' ,
                                        SUM(CASE WHEN b.Extend1 = '95#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr95' ,
                                        SUM(CASE WHEN b.Extend1 = '98#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr98' ,
                                        SUM(CASE WHEN b.Extend1 = '0#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr0' ,
                                        SUM(CASE WHEN b.Extend1 = 'RLY'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drrly' ,
                                        SUM(CASE WHEN b.Extend1 = 'CNG'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drCNG' ,
                                        SUM(CASE WHEN b.Extend1 = 'LNG'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drLNG'
                              FROM      dbo.OSMS_Daily_Sales_lg a ,
                                        dbo.ERP_MaterialClass b
                              WHERE     WorkDay = @v_jsrq
                                        AND a.GasCode = b.MaterialCode
                                        AND a.DataType = '01'
                                        AND a.SalesMode = '01'
                                        AND a.Category <> '03'
                                        AND a.OUCode LIKE @V_OUCode
                              GROUP BY  BigAreaCode
                            ) t1
                            LEFT JOIN ( ---当月计划
                                        --SELECT  UnitCode ,
                                        --        SUM(MonSalesPlan) 'dyMonSalesPlan' ,
                                        --        SUM(MonSalesPlan) / @v_dyzts 'dyyrj' , /*应日均*/
                                        --        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
                                        --        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
                                        --FROM    dbo.MonthlySalesPlan
                                        --WHERE   Monthly = @v_jsny
                                        --        AND DistributionChannel = '01'
                                        --        AND Category IN ( '01', '02' )
                                        --GROUP BY UnitCode
										SELECT  b.BigAreaCode ,
        SUM(a.MonSalesPlan) 'dyMonSalesPlan' ,
        SUM(a.MonSalesPlan) / @v_dyzts 'dyyrj' , /*应日均*/
        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
FROM    MonthlySalesPlan a ,
        (select distinct OUCode,BigAreaCode from OSMS_Daily_Sales_lg )b
WHERE   a.UnitCode = b.OUCode
        AND a.Monthly = @v_jsny
        AND a.DistributionChannel = '01'
        AND a.Category IN ( '01', '02' )
        AND LEN(a.UnitCode) = 15
GROUP BY b.BigAreaCode
                                      ) t2 ON t2.BigAreaCode = t1.BigAreaCode
                            LEFT JOIN ( --去年同期销售情况
                                        SELECT  BigAreaCode ,
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                SUM(CASE WHEN Category IN (
                                                              '01' )
                                                         THEN MonthlySales
                                                    END) 'dyqy' , /*当月汽油*/
                                                SUM(CASE WHEN b.Extend1 IN (
                                                              '0#' )
                                                         THEN MonthlySales
                                                    END) 'dycy' ,/*当月柴油*/
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) / @v_scts 'dysjrj' , /*实际日均*/
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrLNG' ,
                                                SUM(CASE WHEN a.Category IN (
                                                              '01', '02' )
                                                         THEN SalesVol
                                                    END) 'drSalesVol' ,
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drLNG'
                                        FROM    dbo.OSMS_Daily_Sales_lg a ,
                                                dbo.ERP_MaterialClass b
                                        WHERE   WorkDay = @v_tbrq
                                                AND a.GasCode = b.MaterialCode
                                                AND a.DataType = '01'
                                                AND a.SalesMode = '01'
                                                AND a.Category <> '03'
                                                AND a.OUCode LIKE @V_OUCode
                                        GROUP BY BigAreaCode
                                      ) t3 ON t1.BigAreaCode = t3.BigAreaCode
                            LEFT JOIN ( --同年上月期销售情况
                                        SELECT  BigAreaCode ,
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                SUM(CASE WHEN Category IN (
                                                              '01' )
                                                         THEN MonthlySales
                                                    END) 'dyqy' , /*当月汽油*/
                                                SUM(CASE WHEN b.Extend1 IN (
                                                              '0#' )
                                                         THEN MonthlySales
                                                    END) 'dycy' ,/*当月柴油*/
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) / @v_scts 'dysjrj' , /*实际日均*/
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrLNG' ,
                                                SUM(CASE WHEN a.Category IN (
                                                              '01', '02' )
                                                         THEN SalesVol
                                                    END) 'drSalesVol' ,
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drLNG'
                                        FROM    dbo.OSMS_Daily_Sales_lg a ,
                                                dbo.ERP_MaterialClass b
                                        WHERE   WorkDay = @v_hbrq
                                                AND a.GasCode = b.MaterialCode
                                                AND a.DataType = '01'
                                                AND a.SalesMode = '01'
                                                AND a.Category <> '03'
                                                AND a.OUCode LIKE @V_OUCode
                                        GROUP BY BigAreaCode
                                      ) t4 ON t1.BigAreaCode = t4.BigAreaCode
                            LEFT JOIN OSMS_YPT_PQDYGX T5 ON t1.BigAreaCode = T5.BigAreaCode
                ) T
                order by hhk ;
        
    END;
                ", workday_sqyxsrbb);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "quyu_xiaoshouribaobiao");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "quyu_xiaoshouribaobiao_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-8-8-**************
        /// <summary>
        /// 零售区域夜间销售汇总
        /// </summary>
        /// <param name="workday_sjyz"></param>
        /// <param name="workday_djyz"></param>
        /// <returns></returns>
        public ActionResult lingshou_quyu_yejianxiaoshou_ExportData(string workday_sqyyjxs = "", string workday_dqyyjxs = "",string oilno="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_sqyyjxs);
            try  
            {
                conn.Open(); 
                string sql_dzqb;
                
                sql_dzqb = string.Format(@"   
 	                         declare @strat datetime
declare @end datetime
declare @oilno varchar(20)

set @strat=CONVERT(datetime,'{0}')
set @end =CONVERT(datetime,'{1}')
set @oilno='%{2}%'
select  hhhk.oilno,sum(hhhk.tqs) as 'tqs',sum(hhhk.xsje) as 'xsje',sum(hhhk.xsl) as 'xsl',hhhk.yzmc as 'yzmc',hhhk.quyu as 'quyu',hhhk.nodeno   from 
		                            (

                (select (case 
                        when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                        when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                        when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                        when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                        when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                        end) as oilno, count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu' 
				           from his_oilvouch_bycash{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                                    inner join oiltype k on  k.sinopec_code=a.oilno
				                             where   a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' 
				                            group by (case 
                                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                                end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) 
			                            )
			      union all 
			                (select (case 
                                    when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                    when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                    when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                    when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                    when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                    end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end ) as 'quyu'
					              from oildetail{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                           inner join oiltype k on  k.sinopec_code=a.oilno                                 
		                    where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' 
		                group by (case 
                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                end),b.nodeName,b.sinopec_nodeno, 
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end )
		                )
                  union all
			                (select (case 
                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end)  as 'quyu'
					                    from unlocal_detail{3}  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                            inner join oiltype k on  k.sinopec_code=a.oilno
		                    where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047'
		                group by (case 
                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                end),b.nodeName,b.sinopec_nodeno,
				                (case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end )
		                            )

                             ) as hhhk
                            group by hhhk.yzmc,hhhk.quyu,hhhk.nodeno,hhhk.oilno
                            order by hhhk.yzmc,hhhk.quyu
                ", workday_sqyyjxs, workday_dqyyjxs,oilno,ko_s.ToString("yyyy"));//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_quyu_yejianxiaoshou");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_quyu_yejianxiaoshou_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 零售油站区域打桶销售查询
        /// </summary>
        /// <param name="workday_sqyyjxs"></param>
        /// <param name="workday_dqyyjxs"></param>
        /// <returns></returns>
        public ActionResult lingshou_quyu_datongxiaoshou_chaxun_ExportData(string workday_sqyyjxs = "", string workday_dqyyjxs = "",string oilno="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_sqyyjxs);//此处必须，否则转化报错
            try
            { 
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
 	                         declare @strat datetime
                            declare @end datetime
							declare @oilno varchar(20)
                            set @strat=CONVERT(datetime,'{0}')
                            set @end =CONVERT(datetime,'{1}')
							set @oilno='%{2}%'
                            select  hhhk.oilno,sum(hhhk.tqs) as 'tqs',sum(hhhk.xsje) as 'xsje',sum(hhhk.xsl) as 'xsl',hhhk.yzmc as 'yzmc',hhhk.quyu as 'quyu',hhhk.nodeno   from 
		                            (

                (select (case 
                        when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                        when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                        when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                        when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                        when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                        end) as oilno, count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu' 
				                             from his_oilvouch_bycash{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                                                inner join oiltype k on  k.sinopec_code=a.oilno
				                             where   a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
				                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) 
			                            )
			                            union all 
			                            (select (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) as 'quyu'
					                             from oildetail{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                                          inner join oiltype k on  k.sinopec_code=a.oilno
		                              where  a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
		                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno, 
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end )
		                            )
                                     union all
			                            (select (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu'
					                             from unlocal_detail{3}  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                                        inner join oiltype k on  k.sinopec_code=a.oilno
		                              where  a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047'and a.oilno like @oilno 
		                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end )
		                            )

                             ) as hhhk
                            group by hhhk.yzmc,hhhk.quyu,hhhk.nodeno,hhhk.oilno
                            order by hhhk.yzmc,hhhk.quyu




                ", workday_sqyyjxs, workday_dqyyjxs,oilno, ko_s.ToString("yyyy"));//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_quyu_datongxiaoshou_chaxun");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_quyu_yejianxiaoshou_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }


        //***********-9-9-**************
        /// <summary>
        /// 海信券销售明细查询
        /// </summary>
        /// <param name="workday_sjyz"></param>
        /// <param name="workday_djyz"></param>
        /// <returns></returns>
        public ActionResult haixinquan_xiaofei_chaxun_ExportData(string workday_shxqxfcx = "", string workday_dhxqxfcx = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_jk_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                //string sql_dzqb;
                //sql_dzqb = string.Format(@"   

                //", workday_shxqxfcx, workday_dhxqxfcx);//  -- 加油站加油卡占比查询
                //SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                //sda.Fill(ds_dzqb, "lingshou_quyu_yejianxiaoshou");//邮储到账表名，ycdz
                
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                DateTime ko_s = Convert.ToDateTime(workday_shxqxfcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxqxfcx);
                dt_jk_xs = hx_hk.quan_xiaoshou_mingxi(ko_s, ko_e);   //接口返回数据_实时流水
                dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_jk_xs_kk);    //这里有问题
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "haixinquan_xiaofei_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        //***********-10-10-**************
        /// <summary>
        /// APP自提订单查询-导出
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="djzbillno"></param>
        /// <param name="orgcode"></param>
        /// <returns></returns>
        public ActionResult APP_zitidingdan_chaxun_ExportData(string workday_sappddcx = "", string workday_dappddcx = "",string orgname="", string djzbillno = "",string orgcode="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {

                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                dt_jk_xs = hx_hk.APP_ziti_xiaoshou_chaxun(ko_s, ko_e,orgname, djzbillno, orgcode);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "APP_zitidingdan_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 海信验收单查询导出
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <returns></returns>
        public ActionResult haixin_yanshoudan_chaxun_ExportData(string workday_shxysdcx = "", string workday_dhxysdcx = "",string SUPNAME="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {

                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                DateTime ko_s = Convert.ToDateTime(workday_shxysdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxysdcx);
                dt_jk_xs = hx_hk.HX_yanshoudan_chaxun(ko_s, ko_e, SUPNAME);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "haixin_yanshoudan_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }
         
        /// <summary>
        /// 海信团购订单明细导出查询
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <returns></returns>
        public ActionResult haixin_tuangoumingxi_chaxun_ExportData(string billno ="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {

                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.APP_ziti_xiaoshoumingxi_chaxun(billno);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "haixin_tuangoumingxi_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 洗车支付方式汇总查询
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <returns></returns>
        public ActionResult haixin_xiche_zhifufangshi_huizongchaxun_ExportData(string workday_shxysdcx = "", string workday_dhxysdcx = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_shxysdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxysdcx);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_xiche_zhifufangshi_huizongchaxun(ko_s, ko_e);  //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "haixin_tuangoumingxi_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 同步海信团购单--不是导出
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult  HX_tuangoudan_tongbu_Exta(string billno = "")
        {
            Huhk_tongbu huhk_Tongbu = new Huhk_tongbu();
            huhk_Tongbu.tuangoudingdan_mingxi_tongbu(billno);//订单明细同步
            huhk_Tongbu.tuangoudingdan_tongbu(billno);//订单信息同步
            huhk_Tongbu.tuangoudingdan_ler_tongbu(billno);//查询订单入库tuangoudingdan_ler
            return null;
        }

        /// <summary>
        /// 海信销售明细云平台查询_导出
        /// </summary>
        /// <param name="fengongsi"></param>
        /// <param name="shixian"></param>
        /// <param name="orgcode"></param>
        /// <param name="orgname"></param>
        /// <param name="weizhi"></param>
        /// <param name="leixing"></param>
        /// <param name="xxlx"></param>
        /// <param name="rptdate_sta"></param>
        /// <param name="rptdate_end"></param>
        /// <param name="dalei"></param>
        /// <param name="zhonglei"></param>
        /// <param name="xiaolei"></param>
        /// <param name="plucode"></param>
        /// <param name="pluname"></param>
        /// <param name="jhjymode"></param>
        /// <param name="splx1"></param>
        /// <returns></returns>
        public ActionResult HX_xiaoshou_mingxi_read_ExportData(string fengongsi = "", string shixian = "", string orgcode = "", string orgname = "", string weizhi = "", string leixing = "", string xxlx = "", string rptdate_sta = "", string rptdate_end = "", string dalei = "", string zhonglei = "", string xiaolei = "", string plucode = "", string pluname = "", string splx3 = "", string splx1 = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                  --jymode 0-经销；1-代销；2-联销   IsSelfBrand   统采   IsImpPlu   非重点商品
                    select khkn.fengongsi,khkn.shixian,khkn.orgcode,khkn.orgname,khkn.weizhi,khkn.leixing,
                       khkn.xxlx,khkn.rptdate,khkn.dalei,khkn.zhonglei,khkn.xiaolei,khkn.plucode,khkn.pluname
                       ,khkn.splx1,khkn.splx2,khkn.splx3,khkn.unit,khkn.spec,khkn.xscount,khkn.price,khkn.hxtotal
                       ,khkn.xtaxrate,khkn.wxtotal,khkn.wjcost,khkn.wmtotal,khkn.wlmll 
                  from 
                  (
                       select b.fengongsi,b.shixian,b.orgcode,b.orgname, b.weizhi,b.leixing
					      ,case  when  a.datatype != 'D'  then '零售'
							    when a.datatype='D' and a.cxdatatype='5' then '团购'
							    when a.datatype='D' and a.cxdatatype='6' then ' '
							    when a.datatype='D' and a.cxdatatype='7' then '名特优销售'
							    when a.datatype='D' and a.cxdatatype='4' then '内部销售'
							    when a.datatype='D' and a.cxdatatype='13' then '一体化提货'
							    when a.datatype='D' and a.cxdatatype in ('8','H') then '提货'
						     end as xxlx 
					      ,a.rptdate   --
                          ,(select hnk.clsname  from HX_shangpin_pinlei as hnk 
							    where hnk.clscode= left(a.clscode,2) ) as dalei  
					      ,(select hnk_z.clsname  from HX_shangpin_pinlei as hnk_z 
							    where hnk_z.clscode= left(a.clscode,4) ) as zhonglei
					      ,cb.clsname  as xiaolei ,a.plucode,a.pluname
					      ,'统采' as splx1
					      ,'非重点商品' as splx2    --
					      ,case  when a.jymode='0' then '经销' 
							     when a.jymode= '1' then '联销' 
							     end as splx3
                          ,a.unit,a.spec,a.xscount,a.price,a.hxtotal,a.xtaxrate,a.wxtotal,a.wjcost,a.wmtotal
                          , ROUND((convert(float,a.wmtotal)/convert(float,a.wxtotal))*100,2)  as wlmll

                      from HX_tSalPluDetail a left join HX_shop_leixing b on a.orgcode= b.orgcode    
                          left join HX_shangpin_pinlei cb on a.clscode=  cb.clscode

                      where  a.wxtotal!='0' and  b.fengongsi like '%{0}%' and shixian like '%{1}%' and b.orgcode like '%{2}%' 
                             and b.orgname like '%{3}%' and b.weizhi like '%{4}%' and b.leixing like '%{5}%' 
                             and a.rptdate >='{6}' and a.rptdate <='{7}'and a.plucode like '%{8}%' and a.pluname like '%{9}%' 

                    ) khkn where khkn.xxlx like '%{10}%'
                         and khkn.dalei like '%{11}%'  and khkn.zhonglei like '%{12}%'  and khkn.xiaolei like '%{13}%'
                          and khkn.splx3 like '%{14}%'  and khkn.splx1 like '%{15}%'
                             
                ", fengongsi, shixian, orgcode, orgname, weizhi, leixing, rptdate_sta, rptdate_end, plucode, pluname, xxlx, dalei,
                    zhonglei, xiaolei, splx3, splx1);//  -- 加油站加油卡占比查询

                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "HX_xiaoshou_mingxi_read");//邮储到账表名，ycdz
                
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_xiaoshou_mingxi_read_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 云平台团购订单导出
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult HX_tuangoudan_ExportData(string billno = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                /*
                 declare @billno varchar(100)
set @billno='{0}'   -- 1PFXS202207200066
SELECT A.ID,A.BILLNO,A.SERIALNO,A.PLUID,A.PLUCODE,B.LEIBIE_YAN 
      ,A.PLUNAME,A.BARCODE,B.xiaolei_name,B.zhonglei_name,B.dalei_name
      ,A.SPEC ,A.UNIT,A.PACKUNIT ,A.PACKQTY ,A.PFCOUNT ,A.HJPRICE
	  ,B.HETONG_price ,A.PRICE ,A.PFPRICE,B.shiji_price  
	  ,ROUND(convert(float,A.PFPRICE)-convert(float,B.shiji_price),2) SGJC   -- 申请价-规定价>0
	  ,ROUND(convert(float,A.PFPRICE)-convert(float,A.HJPRICE)/(1+convert(float,A.XTAXRATE)/100),2)  U
	  ,ROUND((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100),2)  V
	  ,A.YSTOTAL      ,A.SSTOTAL
	  ,ROUND((convert(float,A.PFCOUNT)*convert(float,B.shiji_price))/(1+convert(float,A.XTAXRATE)/100),2)   W
	  ,ROUND(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100),2)  X
	  ,ROUND((convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100) -convert(float,A.PFCOUNT)*convert(float,B.shiji_price)/(1+convert(float,A.XTAXRATE)/100)),2)  Y
	  ,ROUND(convert(float,A.PFCOUNT)*(convert(float,A.PFPRICE)-convert(float,A.HJPRICE)/(1+convert(float,A.XTAXRATE)/100)),2)  Z
	  ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100)),2)  AA
	  ,ROUND(convert(float,A.PFCOUNT)*(convert(float,A.PFPRICE)-convert(float,A.HJPRICE)/(1+convert(float,A.XTAXRATE)/100))/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100)),2) AB
	  ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100))*(1+convert(float,A.XTAXRATE)/100)/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)),2) AC   -- AA/X
      ,A.XTAXRATE      ,A.XTAXTOTAL      ,A.HJTOTAL      ,A.HMLTOTAL      ,A.HMLRATE      ,A.HCOST 
  FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan] A INNER JOIN HX_shangpin_xinxi B ON A.PLUCODE=B.PLUCODE
  WHERE A.BILLNO=@billno order by A.ID 
                 */
                sql_dzqb = string.Format(@"   

                   declare @billno varchar(100)
                    set @billno='{0}'   -- 1PFXS202207200066
                    SELECT  b.orgcode,b.orgname,
                            A.BILLNO,A.SERIALNO,A.PLUID,A.PLUCODE,A.LEIBIE_YAN 
                          ,A.PLUNAME,A.BARCODE,A.xiaolei_name,A.zhonglei_name,A.dalei_name
                          ,A.SPEC ,A.UNIT,A.PACKUNIT ,A.PACKQTY ,A.PFCOUNT ,A.HJPRICE
                       ,A.HETONG_price ,A.PRICE ,A.PFPRICE,A.guiding_TG_price,A.shiji_price   
                       , A.TGJC   -- 申请价-规定价>0
                       , A.U
                       , A.V
                       , A.YSTOTAL      ,A.SSTOTAL
                       , A.W
                       , A.X
                       , (A.x-A.w) Y   -- A.Y  -- x-w
                       , A.Z   -- o-u
                       , A.AA   --o*v
                       , A.AB    -- Z/X
                       , A.AC   -- AA/X
                       ,A.XTAXRATE  ,A.XTAXTOTAL  ,A.HJTOTAL  ,A.HMLTOTAL  ,A.HMLRATE  ,A.HCOST 
                      FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan_ler] A left join  HX_tuangoudan_head b on a.billno=b.billno
                      WHERE A.BILLNO=@billno order by A.ID

                ", billno);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "HX_tuangoudan_chaxun");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_tuangoudan_tongbu_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// rpt_cloud_daily_hn_StationOnWaterDailySales    海上日报表-导出
        /// lingshou_fengongsi_HS_baobiao
        /// </summary>
        /// <param name="workday"></param>
        /// <returns></returns>
        public ActionResult lingshou_fengongsi_HS_baobiao_ExportData(string workday = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            //string workday_A = workday.Replace("-", string.Empty).Remove(8);  //把2022-10-22  调整为20221022 
            string workday_A = workday.Replace("-", string.Empty);  //把2022-10-22  调整为20221022 
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
    -----海上加油站销售日报表
--ALTER PROC [dbo].[rpt_cloud_daily_hn_StationOnWaterDailySales]
DECLARE @v_jsrq DATE ,
            @v_scts DECIMAL ,/*生产天数*/
            @v_dyzts DECIMAL ,/*总天数*/
            @v_ycrq DATE ,/*开始日期月份的月初日期*/
            @v_ymrq DATE ,/*结束日期月份的月末日期*/
            @v_where_jyfs VARCHAR(50) = '-1' ,/*经营方式where条件变量*/
            @v_where_hzfs VARCHAR(50)= '-1' ,/*汇总方式where条件变量*/
            @v_ksny VARCHAR(6) , /*开始年月*/
            @v_jsny VARCHAR(6) ,/*结束年月*/
            @v_ncrq DATE ,/*年初日期*/
            @v_tbrq DATE ,
            @v_hbrq DATE, 
			@V_RQ VARCHAR(8);

		set @V_RQ = '{0}' ; /*日期*/ 

        SET @v_jsrq = CONVERT(DATE, @V_RQ); 
        SET @v_ncrq = DATEADD(yy, DATEDIFF(yy, 0, @v_jsrq), 0);  /*年初日期*/
        SET @v_ycrq = DATEADD(DD, -DAY(@v_jsrq) + 1, @v_jsrq); /*月初日期*/
        SET @v_ymrq = DATEADD(DD, -DAY(DATEADD(M, 1, @v_jsrq)),
                              DATEADD(M, 1, @v_jsrq));/*月末日期*/
        SET @v_scts = DAY(@v_jsrq);--DATEDIFF(DAY, @V_KSRQ, @V_JSRQ) + 1; /*生产天数*/
        SET @v_dyzts = DAY(@v_ymrq); --DATEDIFF(DAY, @v_ksrq_ycrq, @v_jsrq_ymrq) + 1;/*总天数*/

        SET @v_ksny = CONVERT(VARCHAR(6), @v_ncrq, 112); /*开始年月*/
        SET @v_jsny = CONVERT(VARCHAR(6), @v_jsrq, 112);/*结束年月*/
        SET @v_tbrq = DATEADD(YEAR, -1, @v_jsrq);
        SET @v_hbrq = DATEADD(MONTH, -1, @v_jsrq);


SELECT  -- ROW_NUMBER() OVER ( ORDER BY T.dysjwcjd DESC ) 'rank' ,/*排名*/
                        
						case T.BigAreaname when  '海口分公司' then '1.海口'
						when '三亚分公司' then '2.三亚'
						when '儋州分公司' then '3.儋州'
						when '琼海分公司' then '4.琼海'
						when '万宁分公司' then '5.万宁'
						when '澄迈分公司' then '6.澄迈'
						end '分公司', --分公司
						sum(T.dyMonSalesPlan) '当月计划量',--当月计划量
						sum(t.dyyrj) '当月应日均',       -- 当月应日均
						sum(t.dysjrj) '当月实际日均',       -- 当月实际日均
						t.dyywcjd '当月应完成进度' ,  --sum(T.dyMonSalesPlan)/@v_dyzts dyywcjd ,--当月应完成进度
						-- t.dysjwcjd '当月实际完成进度', --当月实际完成进度
						-- t.dyqjd ,    --当月欠进度
						ISNULL(ISNULL(sum(t.MonthlySales), 0)
                                           / NULLIF(sum(T.dyMonSalesPlan), 0), 0) '当月实际完成进度' , --当月实际完成进度
                                    ISNULL(ISNULL(sum(t.MonthlySales), 0)
                                           / NULLIF(sum(T.dyMonSalesPlan), 0)
                                           - ISNULL(t.dyywcjd, 0), 0) '当月欠进度' , --当月欠进度
						sum(t.MonthlySales) '当月累日销量合计',   /*当月累日销量合计*/
						sum(t.lr92)  '当月累日销量92#',         /*当月累日销量92#*/
						sum(t.lr95)  '当月累日销量95#',          /*当月累日销量95#*/
						sum(t.lr98)  '当月累日销量98#',          /*当月累日销量98#*/
						sum(t.lr0)  '当月累日销量0#',           /*当月累日销量0#*/
						sum(t.lrrly) '当月累日销量燃料油',          /*当月累日销量燃料油*/
						sum(t.lrCNG)  '当月累日销量CNG',        /*当月累日销量CNG*/
						sum(t.lrLNG)  '当月累日销量LNG',        /*当月累日销量LNG*/
						-- sum(t.dyqy) dyqy,         /*当月汽油总销量*/   --有问题
						-- sum(t.dycy) dycy,		/*当月柴油总销量*/
						sum(t.SalesVol) '当日销量合计',		/*当日销量合计*/
						sum(t.dr92)  '当日销量92#',	/*当日销量92#*/
                        sum(t.dr95) '当日销量95#',	/*当日销量95#*/
                        sum(t.dr98) '当日销量98#',	/*当日销量98#*/
                        sum(t.dr0 ) '当日销量dr0',		/*当日销量dr0*/
                        sum(t.drrly) '当日销量燃料油',	/*当日销量燃料油*/
                        sum(t.drCNG) '当日销量CNG',	/*当日销量CNG*/
                        sum(t.drLNG) '当日销量LNG',	/*当日销量LNG*/
                        sum(t.tqMonthlySales) '同比情况同期销量总量',/*同比情况同期销量总量*/
                        sum(t.tq92) '同比情况同期销量92#',	/*同比情况同期销量92#*/
                        sum(t.tq95) '同比情况同期销量95#',	/*同比情况同期销量95#*/
                        sum(t.tq98) '同比情况同期销量98#',	/*同比情况同期销量98#*/
                        sum(t.tq0) '同比情况同期销量0#',		/*同比情况同期销量0#*/
                        sum(t.tqrly) '同比情况同期销量燃料油',	/*同比情况同期销量燃料油*/
                        sum(t.tbzfzl) '同比增幅总量',	/*同比增幅总量*/
                        sum(t.tbzfqy) '同比增幅汽油',	/*同比增幅汽油*/
                        sum(t.tbzfcy) '同比增幅柴油',	/*同比增幅柴油*/
                        sum(t.tbzfrly) '同比增幅燃料油', /*同比增幅燃料油*/
                        -- sum(t.tqqy) tqqy,	/*同期汽油总销量*/
                        -- sum(t.tqcy) tqcy,	/*同期汽油总销量*/
                        -- t.tbzl ,	/*同比总量*/
                        -- t.tbqy ,	/*同比汽油*/
                        -- t.tbcy ,	/*同比柴油*/
                        -- t.tbrly ,	/*同比燃料油*/
						ISNULL(( ISNULL(sum(t.MonthlySales), 0)
                                             - ISNULL(sum(t.tqMonthlySales), 0) )
                                           / NULLIF(sum(t.tqMonthlySales), 0), 0) '同比总量' ,/*同比总量*/
                                    ISNULL(( ISNULL(sum(t.dyqy), 0)
                                             - ISNULL(sum(t.tqqy ), 0) )
                                           / NULLIF(sum(t.tqqy ), 0), 0) '同比汽油' ,/*同比汽油*/
                                    ISNULL(( ISNULL(sum(t.dycy), 0)
                                             - ISNULL(sum(t.tqcy), 0) )
                                           / NULLIF(sum(t.tqcy), 0), 0) '同比柴油' ,/*同比柴油*/
                                    ISNULL(( ISNULL(sum(t.lrrly), 0)
                                             - ISNULL(sum(t.tqrly), 0) )
                                           / NULLIF(sum(t.tqrly), 0), 0) '同比燃料油' , /*同比燃料油*/
                        sum(t.syMonthlySales) '上月环比总销量',/*上月环比总销量*/
                        sum(t.sy92) '上月环比92#销量',	/*上月环比92#销量*/
                        sum(t.sy95) '上月环比95#销量',	/*上月环比95#销量*/
                        sum(t.sy98) '上月环比92#销量',	/*上月环比92#销量*/
                        sum(t.sy0)  '上月环比92#销量',		/*上月环比92#销量*/
                        sum(t.syrly) '上月环比燃料油销量',	/*上月环比燃料油销量*/
                        sum(t.hbzfzl) '环比增幅总量',	/*环比增幅总量*/
                        sum(t.hbzfqy) '环比增幅汽油',	/*环比增幅汽油*/
                        sum(t.hbzfcy) '环比增幅柴油',	/*环比增幅柴油*/
                        sum(t.hbzfrly) '环比增幅燃料油',	/*环比增幅燃料油*/
                        -- sum(t.syqy) syqy,	/*上月汽油总销量*/
                        -- sum(t.sycy) sycy,	/*上月柴油总销量*/
                        -- t.hbzl ,	/*环比总量*/
                        -- t.hbqy ,	/*环比汽油*/
                        -- t.hbcy ,	/*环比柴油*/
                        -- t.hbrly		/*环比燃料油*/
						ISNULL(( ISNULL(sum(t.MonthlySales), 0)
                                             - ISNULL(sum(t.syMonthlySales), 0) )
                                           / NULLIF(sum(t.syMonthlySales), 0), 0) '环比总量' ,/*环比总量*/
                                    ISNULL(( ISNULL(sum(t.dyqy), 0)
                                             - ISNULL(sum(t.syqy), 0) )
                                           / NULLIF(sum(t.syqy), 0), 0) '环比汽油' ,/*环比汽油*/
                                    ISNULL(( ISNULL(sum(t.dycy), 0)
                                             - ISNULL(sum(t.sycy), 0) )
                                           / NULLIF(sum(t.sycy), 0), 0) '环比柴油' ,/*环比柴油*/
                                    ISNULL(( ISNULL(sum(t.lrrly), 0)
                                             - ISNULL(sum(t.syrly), 0) )
                                           / NULLIF(sum(t.syrly), 0), 0) '环比燃料油'/*环比燃料油*/



                FROM    ( SELECT    -- t6.AreaName ,/*片区名称*/
									t6.BigAreaname,
                                     -- t6.ShortName ,/*加油站名称*/
                                    ISNULL(t5.dyMonSalesPlan, 0) 'dyMonSalesPlan' ,--当月计划量
                                    ISNULL(t5.dyyrj, 0) 'dyyrj' ,--当月应日均
                                    ISNULL(t1.dyMonthlySales / @v_scts, 0) 'dysjrj' , --当月实际日均
                                    ISNULL(t5.dyywcjd, 0) 'dyywcjd' , --当月应完成进度
                                    ISNULL(ISNULL(t1.dyMonthlySales, 0)
                                           / NULLIF(t5.dyMonSalesPlan, 0), 0) 'dysjwcjd' , --当月实际完成进度
                                    ISNULL(ISNULL(t1.dyMonthlySales, 0)
                                           / NULLIF(t5.dyMonSalesPlan, 0)
                                           - ISNULL(t5.dyywcjd, 0), 0) 'dyqjd' , --当月欠进度
                                    ISNULL(t1.dyMonthlySales, 0) MonthlySales ,/*当月累日销量合计*/
                                    ISNULL(t1.lr92, 0) 'lr92' ,/*当月累日销量92#*/
                                    ISNULL(t1.lr95, 0) 'lr95' ,/*当月累日销量95#*/
                                    ISNULL(t1.lr98, 0) 'lr98' ,/*当月累日销量98#*/
                                    ISNULL(t1.lr0, 0) 'lr0' ,/*当月累日销量0#*/
                                    ISNULL(t1.lrrly, 0) 'lrrly' ,/*当月累日销量燃料油*/
                                    ISNULL(t1.lrCNG, 0) 'lrCNG' ,/*当月累日销量CNG*/
                                    ISNULL(t1.lrLNG, 0) 'lrLNG' ,/*当月累日销量LNG*/
                                    ISNULL(t1.dyqy, 0) 'dyqy' ,/*当月汽油总销量*/
                                    ISNULL(t1.dycy, 0) 'dycy' ,/*当月柴油总销量*/
                                    ISNULL(t1.drSalesVol, 0) 'SalesVol' ,/*当日销量合计*/
                                    ISNULL(t1.dr92, 0) 'dr92' ,/*当日销量92#*/
                                    ISNULL(t1.dr95, 0) 'dr95' ,/*当日销量95#*/
                                    ISNULL(t1.dr98, 0) 'dr98' ,/*当日销量98#*/
                                    ISNULL(t1.dr0, 0) 'dr0' ,/*当日销量dr0*/
                                    ISNULL(t1.drrly, 0) 'drrly' ,/*当日销量燃料油*/
                                    ISNULL(t1.drCNG, 0) 'drCNG' ,/*当日销量CNG*/
                                    ISNULL(t1.drLNG, 0) 'drLNG' ,/*当日销量LNG*/
                                    ISNULL(t3.dyMonthlySales, 0) 'tqMonthlySales' ,/*同比情况同期销量总量*/
                                    ISNULL(t3.lr92, 0) 'tq92' ,/*同比情况同期销量92#*/
                                    ISNULL(t3.lr95, 0) 'tq95' ,/*同比情况同期销量95#*/
                                    ISNULL(t3.lr98, 0) 'tq98' ,/*同比情况同期销量98#*/
                                    ISNULL(t3.lr0, 0) 'tq0' ,/*同比情况同期销量0#*/
                                    ISNULL(t3.lrrly, 0) 'tqrly' ,/*同比情况同期销量燃料油*/
                                    ISNULL(t1.dyMonthlySales, 0)
                                    - ISNULL(t3.dyMonthlySales, 0) 'tbzfzl' ,/*同比增幅总量*/
                                    ISNULL(t1.dyqy, 0) - ISNULL(t3.dyqy, 0) 'tbzfqy' ,/*同比增幅汽油*/
                                    ISNULL(t1.dycy, 0) - ISNULL(t3.dycy, 0) 'tbzfcy' ,/*同比增幅柴油*/
                                    ISNULL(t1.lrrly, 0) - ISNULL(t3.lrrly, 0) 'tbzfrly' , /*同比增幅燃料油*/
                                    ISNULL(t3.dyqy, 0) 'tqqy' ,/*同期汽油总销量*/
                                    ISNULL(t3.dycy, 0) 'tqcy' ,/*同期柴油总销量*/
                                    ISNULL(( ISNULL(t1.dyMonthlySales, 0)
                                             - ISNULL(t3.dyMonthlySales, 0) )
                                           / NULLIF(t3.dyMonthlySales, 0), 0) 'tbzl' ,/*同比总量*/
                                    ISNULL(( ISNULL(t1.dyqy, 0)
                                             - ISNULL(t3.dyqy, 0) )
                                           / NULLIF(t3.dyqy, 0), 0) 'tbqy' ,/*同比汽油*/
                                    ISNULL(( ISNULL(t1.dycy, 0)
                                             - ISNULL(t3.dycy, 0) )
                                           / NULLIF(t3.dycy, 0), 0) 'tbcy' ,/*同比柴油*/
                                    ISNULL(( ISNULL(t1.lrrly, 0)
                                             - ISNULL(t3.lrrly, 0) )
                                           / NULLIF(t3.lrrly, 0), 0) 'tbrly' , /*同比燃料油*/
                                    ISNULL(t4.dyMonthlySales, 0) 'syMonthlySales' ,/*上月环比总销量*/
                                    ISNULL(t4.lr92, 0) 'sy92' ,/*上月环比92#销量*/
                                    ISNULL(t4.lr95, 0) 'sy95' ,/*上月环比95#销量*/
                                    ISNULL(t4.lr98, 0) 'sy98' ,/*上月环比92#销量*/
                                    ISNULL(t4.lr0, 0) 'sy0' ,/*上月环比92#销量*/
                                    ISNULL(t4.lrrly, 0) 'syrly' ,/*上月环比燃料油销量*/
                                    ISNULL(t1.dyMonthlySales, 0)
                                    - ISNULL(t4.dyMonthlySales, 0) 'hbzfzl' ,/*环比增幅总量*/
                                    ISNULL(t1.dyqy, 0) - ISNULL(t4.dyqy, 0) 'hbzfqy' ,/*环比增幅汽油*/
                                    ISNULL(t1.dycy, 0) - ISNULL(t4.dycy, 0) 'hbzfcy' ,/*环比增幅柴油*/
                                    ISNULL(t1.lrrly, 0) - ISNULL(t4.lrrly, 0) 'hbzfrly' ,/*环比增幅燃料油*/
                                    ISNULL(t4.dyqy, 0) 'syqy' ,/*上月汽油总销量*/
                                    ISNULL(t4.dycy, 0) 'sycy' ,/*上月柴油总销量*/
                                    ISNULL(( ISNULL(t1.dyMonthlySales, 0)
                                             - ISNULL(t4.dyMonthlySales, 0) )
                                           / NULLIF(t4.dyMonthlySales, 0), 0) 'hbzl' ,/*环比总量*/
                                    ISNULL(( ISNULL(t1.dyqy, 0)
                                             - ISNULL(t4.dyqy, 0) )
                                           / NULLIF(t4.dyqy, 0), 0) 'hbqy' ,/*环比汽油*/
                                    ISNULL(( ISNULL(t1.dycy, 0)
                                             - ISNULL(t4.dycy, 0) )
                                           / NULLIF(t4.dycy, 0), 0) 'hbcy' ,/*环比柴油*/
                                    ISNULL(( ISNULL(t1.lrrly, 0)
                                             - ISNULL(t4.lrrly, 0) )
                                           / NULLIF(t4.lrrly, 0), 0) 'hbrly'/*环比燃料油*/
                          FROM      ( SELECT    tb1.OUCode ,
                                                tb1.UniversalCode ,
                                                tb1.ShortName ,
                                                tb2.AreaCode ,
                                                tb2.AreaName,
												tb2.BigAreaCode,
												tb2.BigAreaName 
                                      FROM      ( SELECT    t1.OUCode ,
                                                            t1.ParentOUCode ,
                                                            t1.UniversalCode ,
                                                            t2.ShortName
                                                  FROM      ( SELECT
																  OUCode ,
																  UniversalCode ,
																  OUName ,
																  ParentOUCode
                                                              FROM
																dbo.SYS_OrgUnit
                                                              WHERE
																ParentOUCode IN (
																		SELECT DISTINCT
																		YPT_AREACODE
																		FROM
																		dbo.OSMS_YPT_PQDYGX
																		WHERE
																		YPT_AREACODE <> '' )
                                                              AND OULevel = 5
                                                              AND Status = 1
                                                            ) t1
                                                            JOIN ( SELECT
                                                              OUCode ,
                                                              ShortName
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Remark IN (
                                                              SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006' )
                                                              ) t2 ON t2.OUCode = t1.OUCode
                                                ) tb1   
												-------- tb1***
                                                JOIN ( SELECT DISTINCT
                                                              LG_AREANAME AS 'AreaName' ,
                                                              YPT_AREACODE AS 'AreaCode',
															  BigAreaCode,
															  BigAreaName 
                                                       FROM   dbo.OSMS_YPT_PQDYGX
                                                       WHERE  YPT_AREACODE <> ''
                                                     ) tb2 ON tb2.AreaCode = tb1.ParentOUCode
												-------- tb2***
												
                                    ) t6    --**********- t6 -*********
                                    LEFT JOIN ( ---当月计划
                                                SELECT  UnitCode ,
                                                        SUM(MonSalesPlan) 'dyMonSalesPlan' ,
                                                        SUM(MonSalesPlan)
                                                        / @v_dyzts 'dyyrj' , /*应日均*/
                                                        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
                                                        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
                                                FROM    dbo.MonthlySalesPlan
                                                WHERE   Monthly = @v_jsny
                                                        AND DistributionChannel = '01'
                                                        AND Category IN ( '01',
                                                              '02' )
                                                      --  AND LEN(UnitCode) = 8
                                                GROUP BY UnitCode
                                              ) t5 ON t5.UnitCode = t6.OUCode
										--**********- t5 -*********
                                    LEFT JOIN ( --当月销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												 SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   WorkDay = @v_jsrq
                                                        AND DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND SalesMode = '01'
                                                        AND Category <> '03'
                                                GROUP BY AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName
                                              ) t1 ON t1.UniversalCode = t6.UniversalCode
										 --**********- t1 -*********
                                    LEFT JOIN ( --去年同期销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   a.WorkDay = @v_tbrq
                                                        AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND a.SalesMode = '01'
                                                        AND a.Category <> '03'
                                                GROUP BY a.AreaCode ,
                                                        a.AreaName ,
                                                        a.UniversalCode ,
                                                        a.ShortName
                                              ) t3 ON t6.UniversalCode = t3.UniversalCode
											  --**********- t3 -*********
                                    LEFT JOIN ( --同年上月期销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                         SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												 SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   a.WorkDay = @v_hbrq
                                                        AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND a.SalesMode = '01'
                                                        AND a.Category <> '03'
                                                GROUP BY a.AreaCode ,
                                                        a.AreaName ,
                                                        a.UniversalCode ,
                                                        a.ShortName
                                              ) t4 ON t6.UniversalCode = t4.UniversalCode
											  --**********- t4 -*********
                        ) T
						group by  T.BigAreaname,t.dyywcjd
						order by
						case T.BigAreaname when  '海口分公司' then '1.海口'
						when '三亚分公司' then '2.三亚'
						when '儋州分公司' then '3.儋州'
						when '琼海分公司' then '4.琼海'
						when '万宁分公司' then '5.万宁'
						when '澄迈分公司' then '6.澄迈'
						end;


                ", workday_A);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_fengongsi_HS_baobiao");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_fengongsi_HS_baobiao_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 销售排名-前20名   HX_xiaoshou_pm_ExportData 当月
        /// </summary>
        /// <param name="workday_shxxspm"></param>
        /// <param name="workday_dhxxspm"></param>
        /// <returns></returns>
        public ActionResult HX_xiaoshou_pm_ExportData(string workday_shxxspm = "", string workday_dhxxspm = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_shxxspm);
                DateTime ko_e = Convert.ToDateTime(workday_dhxxspm);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.xiaoshou_pm(ko_s, ko_e);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_xiaoshou_pm_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 海信调拨单   HX_diaobodan_ExportData
        /// </summary>
        /// <param name="workday_shxdbdcx"></param>
        /// <param name="workday_dhxdbdcx"></param>
        /// <returns></returns>
        public ActionResult HX_diaobodan_ExportData(string workday_shxdbdcx = "", string workday_dhxdbdcx = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_shxdbdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxdbdcx);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_diaobodan(ko_s, ko_e);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_diaobodan_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 海信便利店交易笔数
        /// </summary>
        /// <param name="workday_shxbscx"></param>
        /// <param name="workday_dhxbscx"></param>
        /// <param name="orgcode"></param>
        /// <param name="orgname"></param>
        /// <returns></returns>
        public ActionResult HX_bianlidian_bishu_ExportData(string workday_shxbscx = "", string workday_dhxbscx = "", string orgcode = "", string orgname = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_bianlidian_bishu(ko_s, ko_e, orgcode, orgname);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_bianlidian_bishu_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 海信商品信息导出
        /// </summary>
        /// <param name="plucode"></param>
        /// <param name="pluname"></param>
        /// <param name="orgcode"></param>
        /// <param name="barcode"></param>
        /// <returns></returns>
        public ActionResult HX_shangpin_xinxi_ExportData(string plucode = "", string pluname = "", string orgcode = "", string barcode = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                //DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
                //DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.shangpin_shuxing(plucode, orgcode, pluname, barcode);   //接口返回数据_商品信息
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_shangpin_xinxi_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 分公司日报表汇总查询导出
        /// 已调整
        /// </summary>
        /// <param name="workday_sqyxsrbb"></param>
        /// <returns></returns>
        public ActionResult fengongsi_ribaobiao_huizong_ExportData(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    declare @workday_s datetime
                            declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                            declare @workday_sl datetime
                            declare @workday_dl datetime
                            set @workday_sl= DATEADD(year,-1,@workday_s) 
                            set @workday_dl= DATEADD(year,-1,@workday_d) 

                            begin
                            select a.BigAreaCode,a.BigAreaName
							,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
							
							from 
                            (SELECT  a.BigAreaCode , a.BigAreaName,
                                            SUM(CASE WHEN b.Extend1 = '92#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'SalesVola' ,
                                            SUM(CASE WHEN b.Extend1 = '95#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVolb' ,
                                            SUM(CASE WHEN b.Extend1 = '98#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVolc' ,
                                            SUM(CASE WHEN b.Extend1 = '0#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVold' ,
                                            SUM(CASE WHEN b.Extend1 = 'RLY'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'rly' ,
                                            SUM(CASE WHEN b.Extend1 = 'CNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'CNG' ,
                                            SUM(CASE WHEN b.Extend1 = 'LNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'LNG'
                                FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
	                           --  join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                                WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                        AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                        AND a.SalesMode = '01' and a.BigAreaCode != '100045' 
                                        AND a.Category <> '03'
                                        and a.BigAreaCode <> '*'
			                    --         and c.Geography = '004006'
                                GROUP BY a.BigAreaCode ,  a.BigAreaName)  a 
	                            full join 
	                            (SELECT  aa.BigAreaCode as qn_BigAreaCode , aa.BigAreaName as qn_BigAreaName,
                                            SUM(CASE WHEN bb.Extend1 = '92#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_SalesVola' ,
                                            SUM(CASE WHEN bb.Extend1 = '95#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVolb' ,
                                            SUM(CASE WHEN bb.Extend1 = '98#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVolc' ,
                                            SUM(CASE WHEN bb.Extend1 = '0#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVold' ,
                                            SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_rly' ,
                                            SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_CNG' ,
                                            SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_LNG'
                                FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
	                           --  join SYS_OrgProperty c ON aa.UniversalCode = c.Remark
                                WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl
                                        AND aa.DataType = '01'  and aa.UniversalCode not in ('33350266','33350432','33350040')
                                        AND aa.SalesMode = '01'  and aa.BigAreaCode != '100045'
                                        AND aa.Category <> '03'
                                        and aa.BigAreaCode <> '*'
			                         --    and c.Geography = '004006'
                                GROUP BY aa.BigAreaCode ,  aa.BigAreaName) b on a.BigAreaCode=b.qn_BigAreaCode
                                -- order by a.BigAreaCode
							union all
							select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj 
                    order by a.BigAreaCode
	                            end;



                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "fengongsi_ribaobiao_huizong_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "fengongsi_ribaobiao_huizong_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 分公司海上日报表汇总查询导出
        /// 已调整
        /// </summary>
        /// <param name="workday_sfgsrbb"></param>
        /// <param name="workday_efgsrbb"></param>
        /// <returns></returns>
        public ActionResult fengongsi_HS_ribaobiao_huizong_ExportData(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
                begin
                select a.BigAreaCode , a.BigAreaName 
				,ROUND(cast(a.SalesVola as float),2) 'SalesVol_92',ROUND(cast(a.SalesVolb as float),2) 'SalesVol_95'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVol_98',ROUND(cast(a.SalesVold as float),2) 'SalesVol_0',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVol_92'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVol_95',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVol_98'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVol_0',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'

				from 
				(SELECT  a.BigAreaCode , a.BigAreaName,
                                SUM(CASE WHEN b.Extend1 = '92#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'SalesVola' ,
                                SUM(CASE WHEN b.Extend1 = '95#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVolb' ,
                                SUM(CASE WHEN b.Extend1 = '98#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVolc' ,
                                SUM(CASE WHEN b.Extend1 = '0#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVold' ,
                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'rly' ,
                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'CNG' ,
                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'LNG'
                    FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
					join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                    WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                            AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
                    GROUP BY a.BigAreaCode ,  a.BigAreaName) a
					inner join 
					(
						SELECT  a.BigAreaCode , a.BigAreaName,
                                SUM(CASE WHEN b.Extend1 = '92#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_SalesVola' ,
                                SUM(CASE WHEN b.Extend1 = '95#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVolb' ,
                                SUM(CASE WHEN b.Extend1 = '98#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVolc' ,
                                SUM(CASE WHEN b.Extend1 = '0#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVold' ,
                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_rly' ,
                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_CNG' ,
                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_LNG'
                    FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
					join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                    WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                            AND a.DataType = '01'   and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
                    GROUP BY a.BigAreaCode ,  a.BigAreaName
					) b on a.BigAreaCode=b.BigAreaCode

	union all
					select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
						join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
						join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
							) b on a.lj = b.lj    
                    order by a.BigAreaCode 
	                end;

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "fengongsi_HS_ribaobiao_huizong_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "fengongsi_HS_ribaobiao_huizong_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 海信团购单head导出-于天骄用   
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="workday_dtgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult HX_tuangoudan_TJ_ExportData(string workday_stgdcx = "", string workday_dtgdcx = "", string billno = "")
        {
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dtgdcx);
                DataTable dt_jk_xs = new DataTable();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_tuangoudan_TJ(billno, ko_s, ko_e);   //接口返回数据_实时流水
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_tuangoudan_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 市县公司日报表汇总
        /// 已调整
        /// </summary>
        /// <param name="workday_sfgsrbb"></param>
        /// <param name="workday_efgsrbb"></param>
        /// <returns></returns>
        public ActionResult ls_shixiangongsi_ribaobiao_huizong_ExportData(string workday_ssxgsrbb = "", string workday_esxgsrbb = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_ssxgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_esxgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d= '{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 

                    begin
                    select a.BranchCode,a.BranchName
					,ROUND(cast(a.SalesVola as float),2) 'SalesVol_92',ROUND(cast(a.SalesVolb as float),2) 'SalesVol_95'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVol_98',ROUND(cast(a.SalesVold as float),2) 'SalesVol_0',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVol_92'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVol_95',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVol_98'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVol_0',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
					from 
                    (SELECT  a.BranchCode , a.BranchName,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
	                   --  join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*'
			                 --    and c.Geography = '004006'
                        GROUP BY a.BranchCode ,  a.BranchName)  a 
	                    left join 
	                    (SELECT  aa.BranchCode as qn_BranchCode , aa.BranchName as qn_BranchName,
                                    SUM(CASE WHEN bb.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN bb.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN bb.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN bb.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
	                   --  join SYS_OrgProperty c ON aa.UniversalCode = c.Remark
                        WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl
                                AND aa.DataType = '01'  and aa.UniversalCode not in ('33350266','33350432','33350040')
                                AND aa.SalesMode = '01'  and aa.BigAreaCode != '100045001'
                                AND aa.Category <> '03'
                                and aa.BigAreaCode <> '*'
			                   --  and c.Geography = '004006'
                        GROUP BY aa.BranchCode ,  aa.BranchName) b on a.BranchCode=b.qn_BranchCode
				union all
				select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'合计'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj   
                        order by  a.BranchCode
	                    end;

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "shixiangongsi_ribaobiao_huizong_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "shixiangongsi_ribaobiao_huizong_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 加油站日报表汇总
        /// 已调整
        /// </summary>
        /// <param name="workday_syzrbb"></param>
        /// <param name="workday_eyzrbb"></param>
        /// <returns></returns>
        public ActionResult ls_youzhan_ribaobiao_huizong_ExportData(string workday_syzrbb = "", string workday_eyzrbb = "",string shortname="")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_syzrbb);
                DateTime ko_e = Convert.ToDateTime(workday_eyzrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                        -- 含有同比环比数据 去掉3个海上站点，并增加了合计
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
                    begin
                    select a.ShortName,a.UniversalCode
					,ROUND(cast(a.SalesVola as float),2) 'SalesVol_92',ROUND(cast(a.SalesVolb as float),2) 'SalesVol_95'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVol_98',ROUND(cast(a.SalesVold as float),2) 'SalesVol_0',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
					from 
                    (SELECT  a.ShortName , a.UniversalCode,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d and a.shortname like '%{2}%'
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*'
                        GROUP BY a.ShortName , a.UniversalCode) a
	                    left  join
	                    (SELECT  aa.ShortName as qn_ShortName , aa.UniversalCode as qn_UniversalCode,
                                    SUM(CASE WHEN bb.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN bb.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN bb.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN bb.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
                        WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl and aa.shortname like '%{2}%' 
                                AND aa.DataType = '01' and aa.UniversalCode not in ('33350266','33350432','33350040')
                                AND aa.SalesMode = '01'
                                AND aa.Category <> '03'
                                and aa.BigAreaCode <> '*'
                        GROUP BY aa.ShortName , aa.UniversalCode) b  on a.UniversalCode=b.qn_UniversalCode
	                    
						union all
						
                    select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'合计'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d and a.shortname like '%{2}%'
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl and a.shortname like '%{2}%'
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj     
                            end;

                ", ko_s, ko_e, shortname);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "ls_youzhan_ribaobiao_huizong_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "ls_youzhan_ribaobiao_huizong_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 全省日报表汇总
        /// 已调整
        /// </summary>
        /// <param name="workday_sqsrbb"></param>
        /// <param name="workday_eqsrbb"></param>
        /// <returns></returns>
        public ActionResult ls_quansheng_ribaobiao_huizong_ExportData(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
					-- cast((qy_10.MonSalesPlan) as Float)
                    begin
                    select a.quansheng
                    ,ROUND(cast(a.SalesVola as float),2) 'SalesVol_92',ROUND(cast(a.SalesVolb as float),2) 'SalesVol_95'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVol_98',ROUND(cast(a.SalesVold as float),2) 'SalesVol_0',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVol_92'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVol_95',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVol_98'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVol_0',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj     
                            end;

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "ls_quansheng_ribaobiao_huizong_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "ls_quansheng_ribaobiao_huizong_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 零售柴油笔数
        /// </summary>
        /// <param name="workday_shxbscx"></param>
        /// <param name="workday_dhxbscx"></param>
        /// <returns></returns>
        public ActionResult ls_chaiyou_bishu_ExportData(string workday_shxbscx = "", string workday_dhxbscx = "")
        {
            
            //MySqlCommand SQLCmd;
            //1 根据查询条件查询数据库数据,存入ds
            string connetStr = "server=************;user=sbxquery;password=*********;database=oilstation;sslMode=none;Allow User Variables=True"; //localhost不支持ssl连接时，最后一句一定要加！！！
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            try
            {
                SQLCon.Open();  //连接数据库
                string sql_dzqb;
                sql_dzqb = string.Format(@"select ID from sm_manoilvouch where sinopecnodeno='33350265' LIMIT 10");//  -- 加油卡比例附表区域
                MySqlDataAdapter adapter = new MySqlDataAdapter(sql_dzqb, SQLCon); //为存储过程名
                adapter.Fill(ds, "ls_chaiyou_bishu_ExportData");//邮储到账表名，ycdz
            }
            catch (MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
                //MessageBox.Show(ex.Message, "提示");     //显示错误信息
            }
            finally
            {
                SQLCon.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "ls_chaiyou_bishu_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        /// 加油卡休眠客户导出
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        public ActionResult lingshou_xiumian_chaxun_ExportData(string workday_slsxm = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);//此处必须，否则转化报错
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
 	                    declare @workday_sx datetime
                        declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sx = Dateadd(MONTH, -2, '{0}')
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                            and b.cardno not in (select a.cardno from oildetail a where a.opetime>=@workday_sx and a.opetime <='{0}')
                            and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_xiumian_chaxun_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_xiumian_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 加油卡低频客户导出
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        public ActionResult lingshou_dipin_chaxun_ExportData(string workday_slsxm = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);//此处必须，否则转化报错
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
 	                    declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                             and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno
                        having count(b.cardno)>=1 and  count(b.cardno)<3;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_dipin_chaxun_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_dipin_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 加油卡摇摆客户导出
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        public ActionResult lingshou_yaobai_chaxun_ExportData(string workday_slsxm = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_dzqb = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);//此处必须，否则转化报错
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
 	                    declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                             and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno
                        having count(b.cardno)>=3 and  count(b.cardno)<5;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_dzqb, "lingshou_yaobai_chaxun_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_dzqb);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "lingshou_yaobai_chaxun_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 石化钱包休眠用户导出
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        public ActionResult APP_shqb_xiumian_ExportData(string end_time = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;";//sunbox库
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            DateTime stat_time = Convert.ToDateTime(end_time);
            DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
            DateTime stat_d_time = stat_time.AddMonths(-2); //. AddDays(-1);

            try
            {
                SQLCon.Open(); //连接数据库
                               //string searchStr = "select * from user_emp_base";   //student表中数据
                string searchStr = string.Format(@"
               select kk.user_code from 
                    -- 一年内有交易
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null
                    ) kk
                    where 
                    -- 3个月内无交易
                    kk.user_code not in (
                    select user_code from 
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null
                    ) hh group by hh.user_code
                    )
                    group by kk.user_code;

                ", stat_x_time, stat_d_time, end_time);//  -- 邮储导入
                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_xiumian_ExportData");
                // result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);
            }
            catch 
            {
                
            }
            finally
            {
                SQLCon.Close();
            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "APP_shqb_xiumian_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 石化钱包低频用户导出
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        public ActionResult APP_shqb_dipin_ExportData(string end_time = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;";//sunbox库
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            DateTime stat_time = Convert.ToDateTime(end_time);
            //DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
            DateTime stat_d_time = stat_time.AddMonths(-1); //. AddDays(-1);

            try
            {
                SQLCon.Open(); //连接数据库
                               //string searchStr = "select * from user_emp_base";   //student表中数据
                string searchStr = string.Format(@"
                
                    select kk.user_code 
                    from 
                    -- 一年内有交易
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null
                    ) kk
                    group by kk.user_code
                    having count(kk.user_code)>=1 and  count(kk.user_code)<3;

                ", stat_d_time, end_time);//  -- 邮储导入  3和5 是单位时间内的次数

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_dipin_ExportData");
                // result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);
            }
            catch
            {

            }
            finally
            {
                SQLCon.Close();
            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "APP_shqb_dipin_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 石化钱包摇摆用户导出
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        public ActionResult APP_shqb_yaobai_ExportData(string end_time = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;";//sunbox库
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            DateTime stat_time = Convert.ToDateTime(end_time);
            //DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
            DateTime stat_d_time = stat_time.AddMonths(-1); //. AddDays(-1);

            try
            {
                SQLCon.Open(); //连接数据库
                               //string searchStr = "select * from user_emp_base";   //student表中数据
                string searchStr = string.Format(@"
                
                    select kk.user_code 
                    from 
                    -- 一年内有交易
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null
                    ) kk
                    group by kk.user_code
                    having count(kk.user_code)>=3 and  count(kk.user_code)<5;
                   

                ", stat_d_time, end_time );//  -- 邮储导入  3和5 是单位时间内的次数 

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_yaobai_ExportData");
                // result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);
            }
            catch
            {

            }
            finally
            {
                SQLCon.Close();
            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "APP_shqb_yaobai_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// APP提成
        /// </summary>
        /// <param name="bill_date_month"></param>
        /// <param name="emp_name"></param>
        /// <param name="emp_phone"></param>
        /// <param name="store_ou_name"></param>
        /// <param name="standard_code"></param>
        /// <returns></returns>
        public ActionResult APP_yg_ticheng_ExportData(string bill_date_month = "", string emp_name = "", string emp_phone = "", string store_ou_name = "", string standard_code = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;";//sunbox库
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=stat;sslMode=none;";//STAT库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            //DateTime stat_time = Convert.ToDateTime(bill_date_month);
            //string stat_d_time = stat_time.ToString("YYYY-MM"); //. AddDays(-1);

            try
            {
                SQLCon.Open(); //连接数据库
                               //string searchStr = "select * from user_emp_base";   //student表中数据
                string searchStr = string.Format(@"
                    select a.id,a.emp_name,a.emp_id,a.emp_phone,a.store_ou_code,a.store_ou_name,
                    a.standard_code,a.bill_date_month,a.emp_self_lifting_rewards_num,a.emp_delivery_rewards_num  
                    from  shopping_noil_emp_distribution_month_sum as a
                    where a.bill_date_month = '{0}'
                    and a.emp_name like '%{1}%' 
                    and a.emp_phone like '%{2}%' 
                    and a.store_ou_name like '%{3}%'  
                    and a.standard_code like '%{4}%' 
                    AND a.emp_self_lifting_rewards_num IS NOT NULL
                    AND a.emp_delivery_rewards_num IS NOT NULL
                ", bill_date_month, emp_name, emp_phone, store_ou_name, standard_code);//  -- 邮储导入  3和5 是单位时间内的次数 

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_yg_ticheng_ExportData");
                // result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);

            }
            catch
            {

            }
            finally
            {
                SQLCon.Close();
            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "APP_yg_ticheng_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 加满率报表数据导出
        /// </summary>
        /// <param name="UniversalCode"></param>
        /// <param name="workday_jml"></param>
        /// <returns></returns>
        public ActionResult youzhan_jiamanlv_baobiao_ExportData(string UniversalCode = "", string workday_jml = "", string workday_d_jml = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_hml = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_jml);
                string ks = ko_s.ToString("yyyy-MM-dd");    //workday1是datatime
                //DateTime ko_e = Convert.ToDateTime(workday_esxgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                    -- 加满率
                   
                    -- 加满率
                    with 
                    hk as (select count(id) id,b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end) as fengongsi, left(b.ShortName,2) as shixian
	                    from CheckDetail_{1} a inner join sys_OrgUnit b 
                        on a.oucode=b.oucode where b.UniversalCode like '%{0}%' and a.WorkDay>='{2}' and a.WorkDay<='{3}' 
                        and a.Amount not in ('100','200','300','400','500','600','700','800','900','1000','1100','1200','1300','1400','1500')
	                    and a.Amount>=100 
                        group by b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end),left(b.ShortName,2)),
                    hk_1 as (select count(id) id,b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end) as fengongsi,left(b.ShortName,2) as shixian,b.OUCode 
	                    from CheckDetail_{1} a inner join sys_OrgUnit b 
                        on a.oucode=b.oucode where b.UniversalCode like '%{0}%' and a.WorkDay='{2}' and a.WorkDay<='{3}'  
                        group by b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end),left(b.ShortName,2),b.OUCode)
                    select hk.fengongsi,hk.shixian,hk.ShortName,hk.UniversalCode,hk.id,round(cast(hk.id as float)/cast(hk_1.id as float)*100,2) as jml 
                    from hk inner join hk_1 on hk.UniversalCode=hk_1.UniversalCode

                ", UniversalCode, ko_s.ToString("yyyy"), workday_jml,  workday_d_jml);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_hml, "youzhan_jiamanlv_baobiao_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_hml);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "youzhan_jiamanlv_baobiao_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }

        /// <summary>
        ///  优惠平衡点输入插入--不是导出
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult qy_fgs_youhuipinghengdian_insert_ExportData(string qty = "", string guaipai_price = "",  string guiding_midu = "", string guiding_maoli = "", string yh_fudu = "",string youpin_name = "")
        {
            
            Huhk_tongbu huhk_Tongbu = new Huhk_tongbu();
            //huhk_Tongbu.tuangoudingdan_mingxi_tongbu(billno);//订单明细同步
            //huhk_Tongbu.tuangoudingdan_tongbu(billno);//订单信息同步
            //huhk_Tongbu.tuangoudingdan_ler_tongbu(billno);//查询订单入库tuangoudingdan_ler
            huhk_Tongbu.qy_youhui_pinghengdian_insert(qty, guaipai_price, guiding_midu, guiding_maoli, yh_fudu, youpin_name);
            // 添加执行成功提示
            TempData["SuccessMessage"] = "数据插入成功！";

            return null;
        }

        //HX_cuxiaodan_TJ(string billno, DateTime workday_k)


        /// <summary>
        /// 液化气订单同步地址和电话
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult APP_yyhq_address_Exta(string billno = "")
        {
            Huhk_tongbu huhk_Tongbu = new Huhk_tongbu();
            huhk_Tongbu.APP_yyhq_address(billno);//订单明细同步
            return null;
        }

        /// <summary>
        /// 促销单查询--于天骄,HX_cuxiaodan_TJ
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult HX_cuxiaodan_TJ_ExportData(string workday_stgdcx = "", string billno = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                //DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
                //DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);
                DataTable dt_jk_xs = new DataTable();
                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);

                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_cuxiaodan_TJ(billno, ko_s);   //接口返回数据_商品信息
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_cuxiaodan_TJ_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="workday_edgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        public ActionResult HX_cuxiaodan_time_TJ_ExportData(string workday_stgdcx = "", string workday_edgdcx = "", string billno = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";
            //SqlConnection conn = new SqlConnection(shujuku);
            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                //DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
                //DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);
                DataTable dt_jk_xs = new DataTable();
                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);
                DateTime ko_d = Convert.ToDateTime(workday_edgdcx);
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                dt_jk_xs = hx_hk.HX_cuxiaodan_yue_TJ(billno, ko_s, ko_d);   //接口返回数据_商品信息
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
            }
            catch
            {
            }
            finally
            {
                //conn.Close();

            }

            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(dt_zt_xs_kk);    //
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "HX_cuxiaodan_TJ_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }

        /// <summary>
        /// 交控轻油销售--生产
        /// </summary>
        /// <param name="workday_jml"></param>
        /// <param name="workday_d_jml"></param>
        /// <returns></returns>
        public ActionResult jiaokong_qyxs_baobiao_ExportData(string workday_jml = "", string workday_d_jml = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds_hml = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_jml);
                string ks = ko_s.ToString("yyyy-MM-dd");    //workday1是datatime
                //DateTime ko_e = Convert.ToDateTime(workday_esxgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                   
                    -- 交控轻油报表导出  CAST(qy_11.xiaoshoushuliang_kg AS FLOAT)
                    select '海南省交控石化有限公司' as '销售组织','成品油销售' as '订单类型','' as '唯一标识号',
                    hk.WorkDay as '单据日期',hk.kh as '客户','' as '业务员' ,hk.bm as '部门',hk.kpkh as '开票客户','' as '行号',hk.jsfs as '结算方式',
                    hk.fhck as '发货仓库',hk.fhwl as '物料编码',hk.xssl as '体积',   -- 体积  M5
                    round(cast(hk.m_kg as float)/cast(hk.xssl as float),2)*1000 as '密度',  -- 密度
                    hk.xsje as '销售金额',cast(hk.m_kg as float)*1000 as '销售数量','13' as '税率',    -- 销售数量、税率
                    round(cast(hk.xssl as float)*cast(hk.fjfdj as float),2) as '汽油附加通行费',   -- 汽油附加通行费
                    hk.fjfdj as '附加费单价*',
                    round(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)),2) as '营业收入（含税）',   -- '营业收入（含税）'
                    round(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float))-(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/1.13,2) as '税额',
                    round((cast(hk.xsje as float) - (cast(hk.xssl as float) * cast(hk.fjfdj as float)))/1.13,2) as '不含税金额',   --  '不含税金额'
                    round(((cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/cast(hk.xssl as float)/1.13),2) as '无税单价',
                    round((cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/cast(hk.xssl as float),2) as '含税单价',
                    '' as '单品折扣','' as '折扣额'  
                    from (
	                    select a.WorkDay ,
			                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end as kh,
			                    a.ShortName as bm,
			                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end  as kpkh,
			                    b.cloudCheckName as jsfs,
			                    a.ShortName as fhck,a.GasCode as fhwl,
			                    a.SalesVol as m_kg,a.amount as xsje,a.qty as xssl,
			                    case when Category = '02' then 0  -- 柴油
				                     when Category = '01' then 1.05  -- 汽油  'L*附加费单价',
				                     end as fjfdj
	                    from  [dbo].[OSMS_Daily_Sales_BW] a inner join OSMS_PAYTYPE b on a.CheckMode=b.cloudCheckMode
	                    where a.WorkDay>='{0}' and a.WorkDay>='{1}' 
                            and a.BigAreaName='交控' and a.SalesVol>0
	                    group by a.WorkDay,b.cloudCheckName,a.ShortName,a.GasCode,a.SalesVol,a.amount,a.qty,
	                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end,
	                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end,
	                    case when Category = '02' then 0  -- 柴油
				                     when Category = '01' then 1.05  -- 汽油  'L*附加费单价',
				                     end) hk ;


                ", workday_jml, workday_d_jml);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds_hml, "jiaokong_qyxs_baobiao_ExportData");//邮储到账表名，ycdz
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            //2步 ds_dzqb转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds_hml);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "jiaokong_qyxs_baobiao_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");
        }




        //*************导入测试***********
        /// <summary>
        /// 导入测试
        /// </summary>
        /// <param name="file"></param>
        /// <returns></returns>
        [HttpPost]
        public JsonResult Import(HttpPostedFileBase file)
        {

            List<JObject> objList = new List<JObject>();
            file = Request.Files["file"];
            try
            {
                if (file != null)
                {
                    // 存本地
                    string suffixName = Path.GetFileName(file.FileName.Substring(file.FileName.LastIndexOf(".") + 1));
                    //string fileNameNew = DateTime.Now.ToString("yyyyMMddHHmmss") + "." + suffixName;
                    string fileNameNew = "云平台工行模板(1)" + "." + suffixName; 
                    var fileName = Path.Combine(Request.MapPath("~/Upload/Template/"), fileNameNew);

                    file.SaveAs(fileName);

                    //**********自建的导入类******
                    ExcelHelper_daoru eHelper = new ExcelHelper_daoru(fileName);
                    DataTable dt = eHelper.ExcelToDataTable("", true);//数据存DT表
                    string result = string.Empty;
                    string connectionString = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
                    //string connectionString = "Data Source=127.0.0.1;Initial Catalog=SinopecGSMIS_test;uid=sa;pwd=*********";


                    //如果目标表不存在则创建,excel文件的第一行为列标题,从第二行开始全部都是数据记录     
                    string strSql = string.Format("if not exists(select * from sysobjects  where name = '{0}') create table {0}(", "Sheet1");   //以sheetName为表名     

                    foreach (System.Data.DataColumn c in dt.Columns)
                    {
                        strSql += string.Format("[{0}] varchar(255),", c.ColumnName);
                    }
                    strSql = strSql.Trim(',') + ")";

                    SqlConnection sqlconn = new SqlConnection(connectionString);
                    sqlconn.Open();
                    SqlCommand command = sqlconn.CreateCommand();
                    command.CommandText = strSql;//这里就是插入语句，写的是合成语句
                    command.ExecuteNonQuery();
                    sqlconn.Close();

                    foreach (DataRow dr in dt.Rows)
                    {
                        // 数据保存数据库
                    }
                    // ...用foreach把tab中数据添加到数据库 省略了
                    // 如果是多表插入,可以调用存储过程.呵呵   //用bcp导入数据  
                    //excel文件中列的顺序必须和数据表的列顺序一致，因为数据导入时，是从excel文件的第二行数据开始，不管数据表的结构是什么样的，反正就是第一列的数据会插入到数据表的第一列字段中，第二列的数据插入到数据表的第二列字段中，以此类推，它本身不会去判断要插入的数据是对应数据表中哪一个字段的     
                    SqlBulkCopy bcp = new SqlBulkCopy(connectionString);
                    //bcp.SqlRowsCopied += new SqlRowsCopiedEventHandler(bcp_SqlRowsCopied);
                    bcp.BatchSize = 100;//每次传输的行数        
                    bcp.NotifyAfter = 100;//进度提示的行数        
                    bcp.DestinationTableName = "Sheet1";//目标表        
                    bcp.WriteToServer(dt);

                    result = "导入成功！";
                    JsonResult json = new JsonResult();
                    json.Data = result;


                }
            }
            catch (Exception ex)
            {
                return Json(new { IsSuccess = 0, Msg = $"上传失败:{ex.Message}" });
            }

            return Json(new { IsSuccess = 1, Msg = "上传成功" });
        }

        /// <summary>
        /// 代码有问题
        /// </summary>
        /// <param name="filebase"></param>
        /// <returns></returns>
        //public rjbEntities ddb = new rjbEntities();
        [HttpPost]
        public ActionResult StationImport(HttpPostedFileBase filebase)
        {
            HttpPostedFileBase file = Request.Files["files"];//获取excel文件
            if (file == null || file.ContentLength <= 0)//判断是否为空
            {
                ViewBag.error = "文件不能为空";
                return View();
            }
            string fileExtenSion; //获取上传文件的扩展名
            fileExtenSion = Path.GetExtension(file.FileName);
            if (fileExtenSion.ToLower() != ".xls" && fileExtenSion.ToLower() != ".xlsx")
            {
                ViewBag.error = "文件类型不对，只能导入xls和xlsx格式的文件";
                return View();
            }
            string FileName = "../Content/excel" + Path.GetFileName(file.FileName);
            //删除服务器里上传的文件 
            if (System.IO.File.Exists(Server.MapPath(FileName)))
            {
                System.IO.File.Delete(Server.MapPath(FileName));
            }
            file.SaveAs(Server.MapPath(FileName));
            //HDR=Yes，这代表第一行是标题，不做为数据使用 ，如果用HDR=NO，则表示第一行不是标题，做为数据来使用。系统默认的是YES  
            string connstr2003 = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + Server.MapPath(FileName) + ";Extended Properties='Excel 8.0;HDR=Yes;IMEX=1;'";
            string connstr2007 = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + Server.MapPath(FileName) + ";Extended Properties=\"Excel 12.0;HDR=YES\"";
            OleDbConnection conn;
            if (fileExtenSion.ToLower() == ".xls")
            {
                conn = new OleDbConnection(connstr2003);
            }
            else
            {
                conn = new OleDbConnection(connstr2007);
            }
            conn.Open();
            string sql = "select * from [Sheet1$]";
            OleDbCommand cmd = new OleDbCommand(sql, conn);
            DataTable dt = new DataTable();
            OleDbDataReader sdr = cmd.ExecuteReader();

            dt.Load(sdr);
            sdr.Close();
            conn.Close();
            //删除服务器里上传的文件  
            if (System.IO.File.Exists(Server.MapPath(FileName)))
            {
                System.IO.File.Delete(Server.MapPath(FileName));
            }
            try
            {
                int insertcount = 0;//记录插入成功条数  
                for (int i = 0; i < dt.Rows.Count; i++)  //列数
                {
                    string e_name = dt.Rows[i][0].ToString();//获取编号
                  
                }
                Response.Write(insertcount + "条数据导入成功！");
            }
            catch (Exception ex)
            {
            }
            return RedirectToAction("Index");
        }



        public ActionResult fyhq_lpg_jydd_ExportData(string start_time = "", string end_time = "")
        {
            //1 根据查询条件查询数据库数据,存入ds
            //以下为测试环境
            //string connetStr = "server=10.193.63.145;port=5581;user=root;password=****************;database=lpg;sslMode=none;";//lpg库
            string connetStr = "server=10.193.63.95;port=3306;user=admin;password=**********;database=lpg;sslMode=none;";//lpg库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();

            try
            {
                SQLCon.Open(); //连接数据库
                               //string searchStr = "select * from user_emp_base";   //student表中数据
                string searchStr = string.Format(@"
                 select id_,AREA_,dept_,no_,time_,cust_type_,cust_id_,AMOUNT_,PAY_TYPE_,PAY_NO_,TRADE_NO_,FLAG_ ,BUY_NAME_,PHONE_  
                     from lpg.lpg_bill where TIME_ >='{0}'  and TIME_ <='{1}'
                   
                ", start_time, end_time);//  -- 邮储导入  3和5 是单位时间内的次数 

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "fyhq_lpg_jydd_ExportData");
                // result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);
            }
            catch
            {

            }
            finally
            {
                SQLCon.Close();
            }
            //2步 ds转化文件流文件
            MemoryStream stream = ExcelHelper.ExportDatasetToExcel(ds);
            stream.Seek(0, SeekOrigin.Begin);
            string filename = "fyhq_lpg_jydd_ExportData";
            return File(stream, "application/vnd.ms-excel", filename + ".xls");

        }



    }
}
