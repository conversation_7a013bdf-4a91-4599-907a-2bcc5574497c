using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Web.Http;
using System.Data.SqlClient;//新增加的数据库命名空间
using System.Data;
using Newtonsoft.Json;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using MySql.Data.MySqlClient;
using System.Windows;
using System.Windows.Forms;
using MessageBox = System.Windows.MessageBox;


//using System.Data.OracleClient;


namespace ceshi_keshan_0315.Controllers
{
    public class ValuesController : ApiController
    {
        //// GET api/values
        //public IEnumerable<string> Get()
        //{
        //    return new string[] { "value1", "value2" };
        //}

        //// GET api/values/5
        //public string Get(int id)
        //{
        //    return "value";
        //}

        //// POST api/values
        //public void Post([FromBody]string value)
        //{
        //}

        //// PUT api/values/5
        //public void Put(int id, [FromBody]string value)
        //{
        //}

        //// DELETE api/values/5
        //public void Delete(int id)
        //{
        //}


        /// <summary>
        /// 财务到账预警系统
        /// </summary>
        /// <param name="workday"></param>
        /// <param name="code"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string chaxun_daozhang_yujin(string workday = "", string code = "", string amount = "")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_yc;
                sql_yc = string.Format(@"
with cunk as 
  (select  youzhan.UniversalCode as Code,youzhan.ouname as name,youzhan.jine as jine,daoz.BatchDate,
  daoz.UniversalCode,(ISNULL(sum(daoz.CreditAmount),0))as amount
 from youzhan_jine_fazhi as youzhan
left join 
 ((select a.BatchDate,a.UniversalCode,a.Channel,a.CreditAmount  
	from TA_LGInputData_bak a where a.BatchDate='{0}' and  a.UniversalCode like '%333%')
		union all 	---邮储导入
	( select d.BatchDate,d.UniversalCode,d.BatchOrg,d.BatchAmount
	from TA_InputXYBankData_bak d where d.BatchDate='{1}'and  d.UniversalCode like '%333%')
union all( select convert(datetime,F1, 20)  as f1,'3335'+substring([F9],4,4) as f9 ,'中国工商银行',f4
 from  [Sheet1]  where  len(F9)<=9  and f9 not LIKE  '%[吖-座]%' and convert(datetime,F1, 20) = '{0}')
) as daoz	
on youzhan.UniversalCode =daoz.UniversalCode 
 group by youzhan.UniversalCode,youzhan.ouname,youzhan.jine,daoz.BatchDate,daoz.UniversalCode
 ) 
select  cunk.Code,cunk.name,cunk.jine,isnull(cunk.BatchDate,'{2}') as BatchDate,cunk.UniversalCode,cunk.amount,(cunk.jine-cunk.amount) as chayi from cunk
 where (cunk.amount-cunk.jine)<0 and (cunk.jine-cunk.amount)<{3}
 ", workday, workday, workday, amount);//  -- 邮储导入
                SqlDataAdapter sda = new SqlDataAdapter(sql_yc, conn); //为存储过程名
                sda.Fill(ds, "ycdz");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["ycdz"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 电子钱包明细查询
        /// </summary>
        /// <param name="workday"></param>
        /// <param name="code"></param>
        /// <param name="amount"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string dianziqianbao_mingxi_chaxun(string workday = "", string code = "", string amount = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
  select (case when left(b.oucode,6) ='100047'then '海口'
				when left(b.oucode,6) ='100048'then  '三亚'
				when left(b.oucode,6) ='100049'then '儋州'
				when left(b.oucode,6) ='100050'then '琼海'
                when left(b.oucode,6) ='100051'then '万宁'
                when left(b.oucode,6) ='100052'then '澄迈'
				else 'zhong' end ) as quyu ,b.OUName,b.ShortName,a.workday,a.checkshift,a.gunno,a.posttc,a.qty,a.price,a.amount,a.OrderStatusTxt
   from  [Cloud].[dbo].[OSMS_Shift_SinopecWallet] a
  left join SYS_OrgUnit b on a.oucode =b.OUCode 
  where a.workday='{0}'", workday);//  -- 电子钱包导出
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 财务让利报表-以五指山油站为依据
        /// </summary>
        /// <param name="workday_s"></param>
        /// <param name="workday_d"></param>
        /// <param name="jinemin"></param>
        /// <param name="jinemax"></param>
        /// <param name="GasName"></param>
        /// <param name="nodename"></param>
        /// <param name="ouname"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string caiwu_rangli_baobiao(string workday_s = "", string workday_d = "", string jinemin = "", string jinemax = "", string GasName = "", string nodename = "", string ouname = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                declare @workday_s datetime 
                declare @workday_d datetime
                declare @jinemin numeric
                declare @jinemax numeric
                set @workday_s='{0}'
                set @workday_d='{1}'
                set @jinemin={2} 
                set @jinemax={3}
                begin
                if (@workday_s<=@workday_d)
                begin
                with jinri as (
                    select ouname,stnid,matnr,nodename,sum(amount) as amount,sum(QUANTITY)as QUANTITY,sum(jine)as jine from caiwu_rangli 
                    where sfdat>=@workday_s and sfdat<=@workday_d 
                    group by ouname,stnid,matnr,nodename ),
                    zuori as 
                    (select ouname,stnid,matnr,nodename,sum(amount) as amount,sum(QUANTITY)as QUANTITY,sum(jine)as jine from caiwu_rangli 
                    where sfdat>=CONVERT(char(10),DateAdd (DAY,-1,@workday_s),120) and sfdat<=CONVERT(char(10),DateAdd (DAY,-1,@workday_d),120)	
                    group by ouname,stnid,matnr,nodename )
                    	
                    select ka.ouname,ka.stnid,kc.GasName,ka.nodename,ka.amount,ka.aqty,ka.jine,ka.bqty,(ka.aqty-ka.bqty)/ka.bqty as  tongbi
			        from ( select a.ouname,a.stnid,a.matnr,a.nodename,a.amount,a.QUANTITY as aqty,a.jine,b.QUANTITY as bqty  from jinri as a inner join zuori as b  on a.stnid=b.stnid and a.matnr=b.matnr ) ka 
		        inner join (select GasCode,GasName 
                    FROM [Cloud].[dbo].[C_StationOilRelation]
                    group by GasCode,GasName )  kc on ka.matnr =kc.GasCode
                     where kc.GasName like '%{4}%' and ka.nodename like '%{5}%' and ka.ouname like '%{6}%' and ka.aqty>=@jinemin and ka.aqty<=@jinemax 
                    end
                    end", workday_s, workday_d, jinemin, jinemax, GasName, nodename, ouname);//  -- 电子钱包导出
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 财务区域让利报表-以五指山油站为依据
        /// </summary>
        /// <param name="workday_s"></param>
        /// <param name="workday_d"></param>
        /// <param name="jinemin"></param>
        /// <param name="jinemax"></param>
        /// <param name="GasName"></param>
        /// <param name="nodename"></param>
        /// <param name="ouname"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string caiwu_quyu_rangli_baobiao(string workday_s = "", string workday_d = "", string jinemin = "", string jinemax = "", string GasName = "", string nodename = "", string ouname = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                declare @workday_s datetime 
                declare @workday_d datetime
                declare @jinemin numeric
                declare @jinemax numeric
                set @workday_s='{0}'
                set @workday_d='{1}'
                set @jinemin='{2}'
                set @jinemax='{3}'
                begin
                if (@workday_s<=@workday_d)
                begin
	                SELECT kg.BigAreaName,kg.BranchName,kg.AreaName,ca.SFDAT,ca.OUName,ca.STNID,ca.MATNR,ca.POSNR,ca.QUANTITY,ca.PRICE,ca.quanpr,ca.AMOUNT,ca.chajia,ca.jine,ca.NodeName,ca.guapai
	                FROM caiwu_rangli as ca  inner join 
		                (select BigAreaName,BranchName,AreaName,UniversalCode from  OSMS_Daily_Sales_lg where WorkDay>=@workday_s and WorkDay<=@workday_d
		                group by BigAreaName,BranchName,AreaName,UniversalCode) as kg 
		                on  ca.STNID=kg.UniversalCode
	                where MATNR like '%{4}%'  and SFDAT >=@workday_s and SFDAT<=@workday_d and ca.jine>=@jinemin and ca.chajia<=@jinemax 
                         and nodename like '%{5}%'   and OUName like '%{6}%' 
                end
                end
                ", workday_s, workday_d, jinemin, jinemax, GasName, nodename, ouname);//  -- 电子钱包导出
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 区域加油卡比例附表
        /// </summary>
        /// <param name="workday_s"></param>
        /// <param name="workday_d"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string lingshou_quyu_jykckblfb(string workday_s = "", string workday_d = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"
                declare @V_KSRQ datetime
declare @V_JZRQ datetime
set @V_KSRQ = '{0}' /*开始日期*/
set @V_JZRQ = '{1}'/*截止日期*/
BEGIN
SELECT  ROW_NUMBER() OVER ( ORDER BY T.BigAreaName DESC ) 'rank' ,
                T.*
        FROM    ( SELECT    t1.BigAreaName BigAreaName,/*区域名称*/
                             ROUND(ISNULL(t1.ckxfl, 0),2) 'ckxfl' ,/*持卡消费量*/
                            ROUND(ISNULL(t2.zxl, 0),2) 'zxl' ,/*总销量*/
                            ROUND(ISNULL(t1.ckxfl / NULLIF(t2.zxl, 0), 0),4)*100 'ckbl' ,/*持卡比例*/
                            ROUND(ISNULL(t3.cyick, 0),2) 'cyick' ,/*柴油IC卡*/
                            ROUND(ISNULL(t4.cyzl, 0),2) 'cyzl' ,/*柴油总量*/
                            ROUND(ISNULL(t3.cyick / NULLIF(t4.cyzl, 0), 0),4)*100 'cyckbl' ,/*柴油持卡比例*/
                            ROUND(ISNULL(t5.qyick, 0),2) 'qyick' ,/*汽油IC卡*/
                            ROUND(ISNULL(t6.qyzl, 0),2) 'qyzl' ,/*汽油总量*/
                            ROUND(ISNULL(t5.qyick / NULLIF(t6.qyzl, 0), 0),4)*100 'qyckbl' ,/*汽油持卡比例*/
                            ROUND(ISNULL(t7.qyick92, 0),2) 'qyick92' ,/*92号汽油IC卡*/
                            ROUND(ISNULL(t8.qyzl92, 0),2) 'qyzl92' ,/*92号汽油总量*/
                            ROUND(ISNULL(t7.qyick92 / NULLIF(t8.qyzl92, 0), 0),4)*100 'qyckbl92' ,/*92号汽油持卡比例*/
                            ROUND(ISNULL(t9.qyick95, 0),2) 'qyick95' ,/*95号汽油IC卡*/
                            ROUND(ISNULL(t10.qyzl95, 0),2) 'qyzl95' ,/*95号汽油总量*/
                            ROUND(ISNULL(t9.qyick95 / NULLIF(t10.qyzl95, 0), 0),4)*100 'qyckbl95' ,/*95号汽油持卡比例*/
                            ROUND(ISNULL(t11.qyick98, 0),2) 'qyick98' ,/*98号汽油IC卡*/
                            ROUND(ISNULL(t12.qyzl98, 0),2) 'qyzl98' ,/*98号汽油总量*/
                            ROUND(ISNULL(t11.qyick98 / NULLIF(t12.qyzl98, 0), 0),4)*100 'qyckbl98'/*98号汽油持卡比例*/
                  FROM      ( ---持卡消费量
                              SELECT    BigAreaCode ,
                                        BigAreaName ,
                                        SUM(SalesVol) 'ckxfl'
                              FROM      dbo.OSMS_Daily_Sales_lg
                              WHERE     DataType = '01'
                                        AND CheckMode = '012007'
                                        AND WorkDay >= @V_KSRQ
                                        AND WorkDay <= @V_JZRQ
										--AND GasCode <> '60078231'
                                        AND SalesMode = '01'
										AND Category IN('01','02')
                              GROUP BY  BigAreaCode ,
                                        BigAreaName
                            ) t1
                            LEFT JOIN ( ---总销量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'zxl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
										        AND Category IN('01','02')
                                        GROUP BY BigAreaCode
                                      ) t2 ON t1.BigAreaCode = t2.BigAreaCode
                            LEFT JOIN ( ---柴油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'cyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t3 ON t1.BigAreaCode = t3.BigAreaCode
                            LEFT JOIN (---柴油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'cyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t4 ON t1.BigAreaCode = t4.BigAreaCode
                            LEFT JOIN ( ---汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t5 ON t1.BigAreaCode = t5.BigAreaCode
                            LEFT JOIN (---汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t6 ON t1.BigAreaCode = t6.BigAreaCode
                            LEFT JOIN ( ---92号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t7 ON t1.BigAreaCode = t7.BigAreaCode
                            LEFT JOIN ( ---92号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t8 ON t1.BigAreaCode = t8.BigAreaCode
                            LEFT JOIN ( ---95号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t9 ON t1.BigAreaCode = t9.BigAreaCode
                            LEFT JOIN ( ---95号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t10 ON t1.BigAreaCode = t10.BigAreaCode
                            LEFT JOIN ( ---98号汽油IC卡
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyick98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t11 ON t1.BigAreaCode = t11.BigAreaCode
                            LEFT JOIN ( ---98号汽油总量
                                        SELECT  BigAreaCode ,
                                                SUM(SalesVol) 'qyzl98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY BigAreaCode
                                      ) t12 ON t1.BigAreaCode = t12.BigAreaCode
                ) T;
				
                end", workday_s, workday_d);//  -- 区域加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 油站加油卡比例附表
        /// </summary>
        /// <param name="workday_s1"></param>
        /// <param name="workday_d1"></param>
        /// <returns></returns>
        //**************1************
        [HttpGet]
        public string lingshou_youzhan_jykckblfb(string workday_s1 = "", string workday_d1 = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"           
                declare @V_KSRQ datetime
declare @V_JZRQ datetime
set @V_KSRQ = '{0}' /*开始日期*/
set @V_JZRQ = '{1}'/*截止日期*/
BEGIN
SELECT  ROW_NUMBER() OVER ( ORDER BY T.ShortName DESC ) 'rank' ,
                T.*
        FROM    ( SELECT    t1.ShortName ShortName,/*区域名称*/
                            ROUND(ISNULL(t1.ckxfl, 0),2) 'ckxfl' ,/*持卡消费量*/
                            ROUND(ISNULL(t2.zxl, 0),2) 'zxl' ,/*总销量*/
                            ROUND(ISNULL(t1.ckxfl / NULLIF(t2.zxl, 0), 0),4)*100 'ckbl' ,/*持卡比例*/
                            ROUND(ISNULL(t3.cyick, 0),2) 'cyick' ,/*柴油IC卡*/
                            ROUND(ISNULL(t4.cyzl, 0),2) 'cyzl' ,/*柴油总量*/
                            ROUND(ISNULL(t3.cyick / NULLIF(t4.cyzl, 0), 0),4)*100 'cyckbl' ,/*柴油持卡比例*/
                            ROUND(ISNULL(t5.qyick, 0),2) 'qyick' ,/*汽油IC卡*/
                            ROUND(ISNULL(t6.qyzl, 0),2) 'qyzl' ,/*汽油总量*/
                            ROUND(ISNULL(t5.qyick / NULLIF(t6.qyzl, 0), 0),4)*100 'qyckbl' ,/*汽油持卡比例*/
                            ROUND(ISNULL(t7.qyick92, 0),2) 'qyick92' ,/*92号汽油IC卡*/
                            ROUND(ISNULL(t8.qyzl92, 0),2) 'qyzl92' ,/*92号汽油总量*/
                            ROUND(ISNULL(t7.qyick92 / NULLIF(t8.qyzl92, 0), 0),4)*100 'qyckbl92' ,/*92号汽油持卡比例*/
                            ROUND(ISNULL(t9.qyick95, 0),2) 'qyick95' ,/*95号汽油IC卡*/
                            ROUND(ISNULL(t10.qyzl95, 0),2) 'qyzl95' ,/*95号汽油总量*/
                            ROUND(ISNULL(t9.qyick95 / NULLIF(t10.qyzl95, 0), 0),4)*100 'qyckbl95' ,/*95号汽油持卡比例*/
                            ROUND(ISNULL(t11.qyick98, 0),2) 'qyick98' ,/*98号汽油IC卡*/
                            ROUND(ISNULL(t12.qyzl98, 0),2) 'qyzl98' ,/*98号汽油总量*/
                            ROUND(ISNULL(t11.qyick98 / NULLIF(t12.qyzl98, 0), 0),4)*100 'qyckbl98'/*98号汽油持卡比例*/
                  FROM      ( ---持卡消费量
                              SELECT    UniversalCode ,
                                        ShortName ,
                                        SUM(SalesVol) 'ckxfl'
                              FROM      dbo.OSMS_Daily_Sales_lg
                              WHERE     DataType = '01'
                                        AND CheckMode = '012007'
                                        AND WorkDay >= @V_KSRQ
                                        AND WorkDay <= @V_JZRQ
										--AND GasCode <> '60078231'
                                        AND SalesMode = '01'
										AND Category IN('01','02')
                              GROUP BY  UniversalCode ,
                                        ShortName
                            ) t1
                            LEFT JOIN ( ---总销量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'zxl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
										        AND Category IN('01','02')
                                        GROUP BY UniversalCode
                                      ) t2 ON t1.UniversalCode = t2.UniversalCode
                            LEFT JOIN ( ---柴油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'cyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t3 ON t1.UniversalCode = t3.UniversalCode
                            LEFT JOIN (---柴油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'cyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '02'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t4 ON t1.UniversalCode = t4.UniversalCode
                            LEFT JOIN ( ---汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t5 ON t1.UniversalCode = t5.UniversalCode
                            LEFT JOIN (---汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t6 ON t1.UniversalCode = t6.UniversalCode
                            LEFT JOIN ( ---92号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t7 ON t1.UniversalCode = t7.UniversalCode
                            LEFT JOIN ( ---92号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl92'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206059',
                                                              '60509752',
                                                              '60506277' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t8 ON t1.UniversalCode = t8.UniversalCode
                            LEFT JOIN ( ---95号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t9 ON t1.UniversalCode = t9.UniversalCode
                            LEFT JOIN ( ---95号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl95'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60206060',
                                                              '60509753' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t10 ON t1.UniversalCode = t10.UniversalCode
                            LEFT JOIN ( ---98号汽油IC卡
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyick98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND CheckMode = '012007'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t11 ON t1.UniversalCode = t11.UniversalCode
                            LEFT JOIN ( ---98号汽油总量
                                        SELECT  UniversalCode ,
                                                SUM(SalesVol) 'qyzl98'
                                        FROM    dbo.OSMS_Daily_Sales_lg
                                        WHERE   DataType = '01'
                                                AND Category = '01'
                                                AND GasCode IN ( '60209058',
                                                              '60509754' )
                                                AND WorkDay >= @V_KSRQ
                                                AND WorkDay <= @V_JZRQ
												--AND GasCode <> '60078231'
                                                AND SalesMode = '01'
                                        GROUP BY UniversalCode
                                      ) t12 ON t1.UniversalCode = t12.UniversalCode
                ) T;
				
                end", workday_s1, workday_d1);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 区域支付比例报表
        /// </summary>
        /// <param name="workday_shh"></param>
        /// <param name="workday_dhh"></param>
        /// <returns></returns>
        //**************2-2-2************
        [HttpGet]
        public string lingshou_quyu_zffsblfb(string workday_shh = "", string workday_dhh = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@" 

declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
select 
(case when left(o.oucode,6) ='100047'then '海口'
				when left(o.oucode,6) ='100048'then  '三亚'
				when left(o.oucode,6) ='100049'then '儋州'
				when left(o.oucode,6) ='100050'then '琼海'
                when left(o.oucode,6) ='100051'then '万宁'
                when left(o.oucode,6) ='100052'then '澄迈'
        else  '其他' 
end ) as quyu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2)*100 jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 xjbl

,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2) dzqbxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 dzqbbl
,sum( a.QUANTITY) zxl
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1  and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052')
GROUP BY (case when left(o.oucode,6) ='100047'then '海口'
				when left(o.oucode,6) ='100048'then  '三亚'
				when left(o.oucode,6) ='100049'then '儋州'
				when left(o.oucode,6) ='100050'then '琼海'
                when left(o.oucode,6) ='100051'then '万宁'
                when left(o.oucode,6) ='100052'then '澄迈'
        else  '其他'  
end ) 
union all 
(select '汇总' quyu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2)*100 jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 xjbl


,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052'))
end   

                ", workday_shh, workday_dhh);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 市县支付比例报表
        /// </summary>
        /// <param name="workday_ssx"></param>
        /// <param name="workday_dsx"></param>
        /// <returns></returns>
        //***************3-3-3************
        [HttpGet]
        public string lingshou_shixian_zffsblfb(string workday_ssx = "", string workday_dsx = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"     
      
declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
select 
(case when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end ) as shixian
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl

,(case when sum(a.QUANTITY)=0 then 0 
	else isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum(a.QUANTITY)=0 then 1 else sum(a.QUANTITY) end),4),0)*100 end) wxzfbl

,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else 
	isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) zfbbl

,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else  isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),2),0)*100 end) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY) =0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY) =0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) xjbl

,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2) dzqbxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end)  dzqbbl
,sum( a.QUANTITY) zxl
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = o.UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052')
GROUP BY (case  when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end )  
union all 
(select '汇总' pianqu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2)*100 jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 xjbl

,sum(case when a.BNSID_CUST in ( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = o.UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in  ('100047','100048','100049','100050','100051','100052') )
end  

                ", workday_ssx, workday_dsx);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 片区支付比例报表
        /// </summary>
        /// <param name="workday_spq"></param>
        /// <param name="workday_dpq"></param>
        /// <returns></returns>
        //***************4-4-4************
        [HttpGet]
        public string lingshou_pianqu_zffsblfb(string workday_spq = "", string workday_dpq = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@" 

declare @workday_s1 datetime
declare @workday_d1 datetime
set @workday_s1='{0}'
set @workday_d1='{1}'
begin
select 
(case when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'
 
        else '其他' end ) as pianqu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,(case when sum(a.QUANTITY)=0 then 0 
	else isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum(a.QUANTITY)=0 then 1 else sum(a.QUANTITY) end),4),0)*100 end) wxzfbl

,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else 
	isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) zfbbl

,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else  isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),2),0)*100 end) jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY) =0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY) =0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) xjbl

,ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2) dzqbxl,sum( a.QUANTITY) zxl
,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end)  dzqbbl
,sum( a.QUANTITY) zxl
from I_ZRMS_EXDAT_001 a 
left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052')
GROUP BY (case when left(o.oucode,9) ='100047001'then '1海口市县公司'
        when left(o.oucode,9) ='100048001'then  '2三亚市县公司'
        when left(o.oucode,9) ='100048002'then '3乐东市县公司'
        when left(o.oucode,9) ='100048003'then '4保亭市县公司'
		when left(o.oucode,9) ='100048004'then '5五指山市县公司'
		when left(o.oucode,9) ='100049001'then '6儋州市县公司'
		when left(o.oucode,9) ='100049002'then '7东方市县公司'
		when left(o.oucode,9) ='100049003'then '8昌江市县公司'
		when left(o.oucode,9) ='100049004'then '90白沙市县公司'
		when left(o.oucode,9) ='100050001'then '91琼海市县公司'
		when left(o.oucode,9) ='100050002'then '92文昌市县公司'
		when left(o.oucode,9) ='100050003'then '93定安市县公司'
		when left(o.oucode,9) ='100051001'then '94万宁市县公司'
		when left(o.oucode,9) ='100051002'then '95陵水市县公司'
		when left(o.oucode,9) ='100051003'then '96琼中市县公司'
        when left(o.oucode,9) ='100052001'then '97澄迈市县公司'
		when left(o.oucode,9) ='100052002'then '98临高市县公司'
        when left(o.oucode,9) ='100052003'then '99屯昌市县公司'

        else '其他' end )
union all 
(select '汇总' pianqu
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2) wxzfxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 wxzfbl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2) zfbxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 zfbbl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2) yhkxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhkbl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2) yhqxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 yhqbl

,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2) jykxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),2)*100 jykbl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2) xjxl,sum( a.QUANTITY) zxl
,ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /sum( a.QUANTITY),4)*100 xjbl

,sum(case when a.BNSID_CUST in ( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl
,(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /sum( a.QUANTITY))*100 dzqbbl
,sum( a.QUANTITY) zxl  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o
on a.STNID = UniversalCode
where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052') )
end   
     
                ", workday_spq, workday_dpq);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 加油站支付比例报表
        /// </summary>
        /// <param name="workday_sjyz"></param>
        /// <param name="workday_djyz"></param>
        /// <returns></returns>
        //***************5-5-5************
        [HttpGet]
        public string lingshou_jiayouzhan_zffsblfb(string workday_sjyz = "", string workday_djyz = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
declare @workday_s1 datetime

declare @workday_d1 datetime

set @workday_s1='2020-11-01'

set @workday_d1='2020-11-03'

begin

select 

o.ShortName as youzhan

,isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end),2),0) wxzfxl,sum( a.QUANTITY) zxl

,(case when sum(a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum(a.QUANTITY)=0 then 1 else sum(a.QUANTITY) end),4),0)*100 end) wxzfbl

,isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end),2),0) zfbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) zfbbl

,isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end),2),0) yhkxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhkbl

,isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end),2),0) yhqxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) yhqbl

,isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) jykxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else  isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),2),0)*100 end) jykbl

,isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) xjxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY) =0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY) =0 then 1 else sum( a.QUANTITY) end),4),0)*100 end) xjbl

,isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end),2),0) dzqbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),4),0)*100 end)  dzqbbl

,sum( a.QUANTITY) zxl

from I_ZRMS_EXDAT_001 a 

left join SYS_OrgUnit o

on a.STNID = UniversalCode

where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052')

GROUP BY a.STNID,o.ShortName

union all 

(select '汇总' youzhan,

sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) wxzfxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(sum(case when a.BNSID_CUST in ( '86','95') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),0)*100 end) wxzfbl

,sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) zfbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(sum(case when a.BNSID_CUST in ( '89','96') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end ),0)*100 end) zfbbl

,sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) yhkxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(sum(case when a.BNSID_CUST = '81' then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end),0)*100 end) yhkbl

,sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) yhqxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(sum(case when a.BNSID_CUST in ('92','91') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end ),0)*100 end) yhqbl

,isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) jykxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST in ('02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end ),2),0)*100 end) jykbl

,isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end),2),0) xjxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else isnull(ROUND(sum(case when a.BNSID_CUST NOT IN ('86','89','81','91','92','94','Q2','95','96','02','14','34','26','38','39') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end ),4),0)*100 end) xjbl

,sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) dzqbxl,sum( a.QUANTITY) zxl

,(case when sum( a.QUANTITY)=0 then 0 else (sum(case when a.BNSID_CUST in( '94','Q2') then a.QUANTITY else 0 end) /(case when sum( a.QUANTITY)=0 then 1 else sum( a.QUANTITY) end ))*100 end) dzqbbl

,sum( a.QUANTITY) zxl  from  I_ZRMS_EXDAT_001 a  left join SYS_OrgUnit o

on a.STNID = UniversalCode

where a.SFDAT>=@workday_s1 and a.SFDAT<=@workday_d1 and left(o.oucode,6) in ('100047','100048','100049','100050','100051','100052'))  

end               
                ", workday_sjyz, workday_djyz);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 分公司销售日报表
        /// 已调整，需要去掉3个海上站点 
        /// </summary>
        /// <param name="workday_sqyxsrbb"></param>
        /// <returns></returns>
        //*******6*****************
        [HttpGet]
        public string quyu_xiaoshouribaobiao(string workday_sqyxsrbb = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
            


declare @V_RQ datetime
declare @V_OUCode VARCHAR(30)
 set  @V_RQ = '{0}' 
 set  @V_OUCode = '' --查看数据级别OUCode

    BEGIN

        DECLARE @v_jsrq DATE ,
            @v_scts DECIMAL ,/*生产天数*/
            @v_dyzts DECIMAL ,/*总天数*/
            @v_ycrq DATE ,/*开始日期月份的月初日期*/
            @v_ymrq DATE ,/*结束日期月份的月末日期*/
            @v_where_jyfs VARCHAR(50) = '-1' ,/*经营方式where条件变量*/
            @v_where_hzfs VARCHAR(50)= '-1' ,/*汇总方式where条件变量*/
            @v_ksny VARCHAR(6) , /*开始年月*/
            @v_jsny VARCHAR(6) ,/*结束年月*/
            @v_ncrq DATE ,/*年初日期*/
            @v_tbrq DATE ,
            @v_hbrq DATE; 

        SET @v_jsrq = CONVERT(DATE, @V_RQ); 
        SET @v_ncrq = DATEADD(yy, DATEDIFF(yy, 0, @v_jsrq), 0);  /*年初日期*/
        SET @v_ycrq = DATEADD(DD, -DAY(@v_jsrq) + 1, @v_jsrq); /*月初日期*/
        SET @v_ymrq = DATEADD(DD, -DAY(DATEADD(M, 1, @v_jsrq)),
                              DATEADD(M, 1, @v_jsrq));/*月末日期*/
        SET @v_scts = DAY(@v_jsrq);--DATEDIFF(DAY, @V_KSRQ, @V_JSRQ) + 1; /*生产天数*/
        SET @v_dyzts = DAY(@v_ymrq); --DATEDIFF(DAY, @v_ksrq_ycrq, @v_jsrq_ymrq) + 1;/*总天数*/

        SET @v_ksny = CONVERT(VARCHAR(6), @v_ncrq, 112); /*开始年月*/
        SET @v_jsny = CONVERT(VARCHAR(6), @v_jsrq, 112);/*结束年月*/
        SET @v_tbrq = DATEADD(YEAR, -1, @v_jsrq);
        SET @v_hbrq = DATEADD(MONTH, -1, @v_jsrq);

        SET @V_OUCode = @V_OUCode + '%';

        SELECT  ROW_NUMBER() OVER ( ORDER BY T.dysjwcjd DESC ) 'rank' ,case when T.BigAreaName='海口分公司' then '1' 
													when T.BigAreaName='三亚分公司' then '2' 
													when T.BigAreaName='儋州分公司' then '3'
													when T.BigAreaName='琼海分公司' then '4'
													when T.BigAreaName='万宁分公司' then '5'
													when T.BigAreaName='澄迈分公司' then '6'
													else '0' end as hhk,
                T.*
        FROM    ( SELECT  DISTINCT  T5.BigAreaName BigAreaName ,/*片区名称*/
                            
                            ROUND(@v_scts / @v_dyzts,6) 'ywcjd' ,
                            ROUND(ISNULL(t2.dyMonSalesPlan, 0),6) 'dyMonSalesPlan' ,/*当月计划量*/
                            ROUND(ISNULL(t2.dyyrj, 0),6) 'dyyrj' ,/*当月应日均*/
                            ROUND(ISNULL(t1.dysjrj, 0),6) 'dysjrj' ,/*当月实际日均*/
                            ROUND(ISNULL(t2.dyywcjd, 0),6) 'dyywcjd' ,/*应完成进度*/
                            ROUND(ISNULL(t1.dyMonthlySales
                                   / NULLIF(t2.dyMonSalesPlan, 0), 0),6) 'dysjwcjd' ,/*实际完成进度*/
                            ROUND(ISNULL(t1.dyMonthlySales
                                   / NULLIF(t2.dyMonSalesPlan, 0) - t2.dyywcjd,
                                   0),6) 'dyqjd' , /*欠进度*/
                            ROUND(ISNULL(t1.dyMonthlySales, 0),6) 'lrMonthlySales' ,/*当月累日销量合计*/
                            ROUND(ISNULL(t1.lr92, 0),6) 'lr92' ,/*当月累日销量92#*/
                            ROUND(ISNULL(t1.lr95, 0),6) 'lr95' ,/*当月累日销量95#*/
                            ROUND(ISNULL(t1.lr98, 0),6) 'lr98' ,/*当月累日销量98#*/
                            ROUND(ISNULL(t1.lr0, 0),6) 'lr0' ,/*当月累日销量0#*/
                            ROUND(ISNULL(t1.lrrly, 0),6) 'lrrly' ,/*当月累日销量燃料油*/
                            ROUND(ISNULL(t1.lrCNG, 0),6) 'lrCNG' ,/*当月累日销量CNG*/
                            ROUND(ISNULL(t1.lrLNG, 0),6) 'lrLNG' ,/*当月累日销量LNG*/
                            ROUND(ISNULL(t1.dyqy, 0),6) 'dyqy' ,/*当月汽油总销量*/
                            ROUND(ISNULL(t1.dycy, 0),6) 'dycy' ,/*当月柴油总销量*/
                            ROUND(ISNULL(t1.drSalesVol, 0),6) 'drSalesVol' ,/*当日销量合计*/
                            ROUND(ISNULL(t1.dr92, 0),6) 'dr92' ,/*当日销量92#*/
                            ROUND(ISNULL(t1.dr95, 0),6) 'dr95' ,/*当日销量95#*/
                            ROUND(ISNULL(t1.dr98, 0),6) 'dr98' ,/*当日销量98#*/
                            ROUND(ISNULL(t1.dr0, 0),6) 'dr0' ,/*当日销量dr0*/
                            ROUND(ISNULL(t1.drrly, 0),6) 'drrly' ,/*当日销量燃料油*/
                            ROUND(ISNULL(t1.drCNG, 0),6) 'drCNG' ,/*当日销量CNG*/
                            ROUND(ISNULL(t1.drLNG, 0),6) 'drLNG' ,/*当日销量LNG*/
                            ROUND(ISNULL(t3.dyMonthlySales, 0),6) 'tqMonthlySales' ,/*同比情况同期销量总量*/
                            ROUND(ISNULL(t3.lr92, 0),6) 'tq92' ,/*同比情况同期销量92#*/
                            ROUND(ISNULL(t3.lr95, 0),6) 'tq95' ,/*同比情况同期销量95#*/
                            ROUND(ISNULL(t3.lr98, 0),6) 'tq98' ,/*同比情况同期销量98#*/
                            ROUND(ISNULL(t3.lr0, 0),6) 'tq0' ,/*同比情况同期销量0#*/
                            ROUND(ISNULL(t3.lrrly, 0),6) 'tqrly' ,/*同比情况同期销量燃料油*/
                            ROUND(ISNULL(t1.dyMonthlySales - t3.dyMonthlySales, 0),6) 'tbzfzl' ,/*同比增幅总量*/
                            ROUND(ISNULL(t1.dyqy - t3.dyqy, 0),6) 'tbzfqy' ,/*同比增幅汽油*/
                            ROUND(ISNULL(t1.dycy - t3.dycy, 0),6) 'tbzfcy' ,/*同比增幅柴油*/
                            ROUND(ISNULL(t1.lrrly - t3.lrrly, 0),6) 'tbzfrly' , /*同比增幅燃料油*/
                            ROUND(ISNULL(t3.dyqy, 0),6) 'tqqy' ,/*同期汽油总销量*/
                            ROUND(ISNULL(t3.dycy, 0),6) 'tqcy' ,/*同期柴油总销量*/
                            ROUND(ISNULL(( t1.dyMonthlySales - t3.dyMonthlySales )
                                   / NULLIF(t3.dyMonthlySales, 0), 0),6) 'tbzl' ,/*同比总量*/
                            ROUND(ISNULL(( t1.dyqy - t3.dyqy ) / NULLIF(t3.dyqy, 0),
                                   0),6) 'tbqy' ,/*同比汽油*/
                            ROUND(ISNULL(( t1.dycy - t3.dycy ) / NULLIF(t3.dycy, 0),
                                   0),6) 'tbcy' ,/*同比柴油*/
                            ROUND(ISNULL(( t1.lrrly - t3.lrrly ) / NULLIF(t3.lrrly,
                                                              0), 0),6) 'tbrly' , /*同比燃料油*/
                            ROUND(ISNULL(t4.dyMonthlySales, 0),6) 'syMonthlySales' ,/*上月环比总销量*/
                            ROUND(ISNULL(t4.lr92, 0),6) 'sy92' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lr95, 0),6) 'sy95' ,/*上月环比95#销量*/
                            ROUND(ISNULL(t4.lr98, 0),6) 'sy98' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lr0, 0),6) 'sy0' ,/*上月环比92#销量*/
                            ROUND(ISNULL(t4.lrrly, 0),6) 'syrly' ,/*上月环比燃料油销量*/
                            ROUND(ISNULL(t1.dyMonthlySales - t4.dyMonthlySales, 0),6) 'hbzfzl' ,/*环比增幅总量*/
                            ROUND(ISNULL(t1.dyqy - t4.dyqy, 0),6) 'hbzfqy' ,/*环比增幅汽油*/
                            ROUND(ISNULL(t1.dycy - t4.dycy, 0),6) 'hbzfcy' ,/*环比增幅柴油*/
                            ROUND(ISNULL(t1.lrrly - t4.lrrly, 0),6) 'hbzfrly' ,/*环比增幅燃料油*/
                            ROUND(ISNULL(t4.dyqy, 0),6) 'syqy' ,/*上月汽油总销量*/
                            ROUND(ISNULL(t4.dycy, 0),6) 'sycy' ,/*上月柴油总销量*/
                            ROUND(ISNULL(( t1.dyMonthlySales - t4.dyMonthlySales )
                                   / NULLIF(t4.dyMonthlySales, 0), 0),6) 'hbzl' ,/*环比总量*/
                            ROUND(ISNULL(( t1.dyqy - t4.dyqy ) / NULLIF(t4.dyqy, 0),
                                   0),6) 'hbqy' ,/*环比汽油*/
                            ROUND(ISNULL(( t1.dycy - t4.dycy ) / NULLIF(t4.dycy, 0),
                                   0),6) 'hbcy' ,/*环比柴油*/
                            ROUND(ISNULL(( t1.lrrly - t4.lrrly ) / NULLIF(t4.lrrly,
                                                              0), 0),6) 'hbrly'/*环比燃料油*/
                  FROM      ( --当月销售情况
                              SELECT    BigAreaCode ,
                                        SUM(CASE WHEN Category IN ( '01', '02' )
                                                 THEN MonthlySales
                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                        SUM(CASE WHEN Category IN ( '01' )
                                                 THEN MonthlySales
                                            END) 'dyqy' , /*当月汽油*/
                                        SUM(CASE WHEN b.Extend1 IN ( '0#' )
                                                 THEN MonthlySales
                                            END) 'dycy' ,/*当月柴油*/
                                        SUM(CASE WHEN Category IN ( '01', '02' )
                                                 THEN MonthlySales
                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                        SUM(CASE WHEN b.Extend1 = '92#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr92' ,
                                        SUM(CASE WHEN b.Extend1 = '95#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr95' ,
                                        SUM(CASE WHEN b.Extend1 = '98#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr98' ,
                                        SUM(CASE WHEN b.Extend1 = '0#'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lr0' ,
                                        SUM(CASE WHEN b.Extend1 = 'RLY'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrrly' ,
                                        SUM(CASE WHEN b.Extend1 = 'CNG'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrCNG' ,
                                        SUM(CASE WHEN b.Extend1 = 'LNG'
                                                 THEN MonthlySales
                                                 ELSE 0
                                            END) 'lrLNG' ,
                                        SUM(CASE WHEN a.Category IN ( '01',
                                                              '02' )
                                                 THEN SalesVol
                                            END) 'drSalesVol' ,
                                        SUM(CASE WHEN b.Extend1 = '92#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr92' ,
                                        SUM(CASE WHEN b.Extend1 = '95#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr95' ,
                                        SUM(CASE WHEN b.Extend1 = '98#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr98' ,
                                        SUM(CASE WHEN b.Extend1 = '0#'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'dr0' ,
                                        SUM(CASE WHEN b.Extend1 = 'RLY'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drrly' ,
                                        SUM(CASE WHEN b.Extend1 = 'CNG'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drCNG' ,
                                        SUM(CASE WHEN b.Extend1 = 'LNG'
                                                 THEN SalesVol
                                                 ELSE 0
                                            END) 'drLNG'
                              FROM      dbo.OSMS_Daily_Sales_lg a ,
                                        dbo.ERP_MaterialClass b
                              WHERE     WorkDay = @v_jsrq
                                        AND a.GasCode = b.MaterialCode
                                        AND a.DataType = '01'   and a.UniversalCode not in ('33350266','33350432','33350040')
                                        AND a.SalesMode = '01'
                                        AND a.Category <> '03'
                                        AND a.OUCode LIKE @V_OUCode
                              GROUP BY  BigAreaCode
                            ) t1
                            LEFT JOIN ( ---当月计划
                                        --SELECT  UnitCode ,
                                        --        SUM(MonSalesPlan) 'dyMonSalesPlan' ,
                                        --        SUM(MonSalesPlan) / @v_dyzts 'dyyrj' , /*应日均*/
                                        --        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
                                        --        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
                                        --FROM    dbo.MonthlySalesPlan
                                        --WHERE   Monthly = @v_jsny
                                        --        AND DistributionChannel = '01'
                                        --        AND Category IN ( '01', '02' )
                                        --GROUP BY UnitCode
										SELECT  b.BigAreaCode ,
        SUM(a.MonSalesPlan) 'dyMonSalesPlan' ,
        SUM(a.MonSalesPlan) / @v_dyzts 'dyyrj' , /*应日均*/
        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
FROM    MonthlySalesPlan a ,
        (select distinct OUCode,BigAreaCode from OSMS_Daily_Sales_lg where  UniversalCode not in ('33350266','33350432','33350040'))b
WHERE   a.UnitCode = b.OUCode
        AND a.Monthly = @v_jsny
        AND a.DistributionChannel = '01'
        AND a.Category IN ( '01', '02' )
        AND LEN(a.UnitCode) = 15
GROUP BY b.BigAreaCode
                                      ) t2 ON t2.BigAreaCode = t1.BigAreaCode
                            LEFT JOIN ( --去年同期销售情况
                                        SELECT  BigAreaCode ,
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                SUM(CASE WHEN Category IN (
                                                              '01' )
                                                         THEN MonthlySales
                                                    END) 'dyqy' , /*当月汽油*/
                                                SUM(CASE WHEN b.Extend1 IN (
                                                              '0#' )
                                                         THEN MonthlySales
                                                    END) 'dycy' ,/*当月柴油*/
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) / @v_scts 'dysjrj' , /*实际日均*/
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrLNG' ,
                                                SUM(CASE WHEN a.Category IN (
                                                              '01', '02' )
                                                         THEN SalesVol
                                                    END) 'drSalesVol' ,
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drLNG'
                                        FROM    dbo.OSMS_Daily_Sales_lg a ,
                                                dbo.ERP_MaterialClass b
                                        WHERE   WorkDay = @v_tbrq
                                                AND a.GasCode = b.MaterialCode  and a.UniversalCode not in ('33350266','33350432','33350040')
                                                AND a.DataType = '01'
                                                AND a.SalesMode = '01'
                                                AND a.Category <> '03'
                                                AND a.OUCode LIKE @V_OUCode
                                        GROUP BY BigAreaCode
                                      ) t3 ON t1.BigAreaCode = t3.BigAreaCode
                            LEFT JOIN ( --同年上月期销售情况
                                        SELECT  BigAreaCode ,
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                SUM(CASE WHEN Category IN (
                                                              '01' )
                                                         THEN MonthlySales
                                                    END) 'dyqy' , /*当月汽油*/
                                                SUM(CASE WHEN b.Extend1 IN (
                                                              '0#' )
                                                         THEN MonthlySales
                                                    END) 'dycy' ,/*当月柴油*/
                                                SUM(CASE WHEN Category IN (
                                                              '01', '02' )
                                                         THEN MonthlySales
                                                    END) / @v_scts 'dysjrj' , /*实际日均*/
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN MonthlySales
                                                         ELSE 0
                                                    END) 'lrLNG' ,
                                                SUM(CASE WHEN a.Category IN (
                                                              '01', '02' )
                                                         THEN SalesVol
                                                    END) 'drSalesVol' ,
                                                SUM(CASE WHEN b.Extend1 = '92#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr92' ,
                                                SUM(CASE WHEN b.Extend1 = '95#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr95' ,
                                                SUM(CASE WHEN b.Extend1 = '98#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr98' ,
                                                SUM(CASE WHEN b.Extend1 = '0#'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'dr0' ,
                                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drrly' ,
                                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drCNG' ,
                                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                                         THEN SalesVol
                                                         ELSE 0
                                                    END) 'drLNG'
                                        FROM    dbo.OSMS_Daily_Sales_lg a ,
                                                dbo.ERP_MaterialClass b
                                        WHERE   WorkDay = @v_hbrq
                                                AND a.GasCode = b.MaterialCode  and a.UniversalCode not in ('33350266','33350432','33350040')
                                                AND a.DataType = '01'
                                                AND a.SalesMode = '01'
                                                AND a.Category <> '03'
                                                AND a.OUCode LIKE @V_OUCode
                                        GROUP BY BigAreaCode
                                      ) t4 ON t1.BigAreaCode = t4.BigAreaCode
                            LEFT JOIN OSMS_YPT_PQDYGX T5 ON t1.BigAreaCode = T5.BigAreaCode
                ) T  
                order by hhk ;
        
    END;
                ", workday_sqyxsrbb);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 营销决策-云平台-销售数据查询
        /// </summary>
        /// <param name="workday_yxjchypt"></param>
        /// <returns></returns>
        //*******77-待调整区域至分公司*****************
        [HttpGet]
        public string yingxiaojuece_yunpingtai_xscx(string workday_yxjchypt = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
         SELECT   
                                                '云平台日销售数据'as kkb,SUM(SalesVol)  SalesVol
                                        FROM    dbo.OSMS_Daily_Sales_lg a ,
                                                dbo.ERP_MaterialClass b
                                        WHERE   WorkDay =  '{0}'
                                                AND a.GasCode = b.MaterialCode
                                                AND a.DataType = '01'
                                                AND a.SalesMode = '01'
                                                AND a. Category IN (
                                                              '01', '02' )
                                                AND a.OUCode LIKE '100%'
                ", workday_yxjchypt);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 营销决策-销售数据查询
        /// </summary>
        /// <param name="workday_yxjcxscx"></param>
        /// <returns></returns>
        //*******76-待调整区域至分公司*****************
        [HttpGet]
        public string yingxiaojuece_xscx(string workday_yxjcxscx = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=SinopecDSS;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
            SELECT    '营销决策日销售数据'as kkb,  sum([N_CTON] )  as yxje_xl
             FROM [dbo].[Rpt_StationSales]	   where   [DateKey]=CONVERT(varchar(100), CONVERT(datetime, '{0}'), 112)
            and typecode in('02','01') and   [UniversalCode] not in
            (select [UniversalCode] from [dbo].[SYS_OrgUnit] where parentoucode='100045001001')

                ", workday_yxjcxscx);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "dzqb");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["dzqb"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 轻油夜间销售查询
        /// </summary>
        /// <param name="workday_sqyyjxs"></param>
        /// <param name="workday_dqyyjxs"></param>
        /// <returns></returns>
        //*******77*****************
        [HttpGet]
        public string lingshou_quyu_yejianxiaoshou(string workday_sqyyjxs = "", string workday_dqyyjxs = "", string oilno = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";

            DateTime ko_s = Convert.ToDateTime(workday_sqyyjxs);
            //string ks = ko_s.ToString("yyyy-MM-dd");
            //string year_nian = ko_s.ToString("yyyyMM");

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                Console.Write("数据库打开成功");
                string sql_dzqb;
                #region
                //                sql_dzqb = string.Format(@"   
                // 	                         declare @strat datetime
                //                            declare @end datetime
                //                            declare @oilno varchar(20)
                //                            set @strat=CONVERT(datetime,'{0}')
                //                            set @end =CONVERT(datetime,'{1}')
                //                            set @oilno='%{2}%'
                //                            select  hhhk.oilno,sum(hhhk.tqs) as 'tqs',sum(hhhk.xsje) as 'xsje',sum(hhhk.xsl) as 'xsl',hhhk.yzmc as 'yzmc',hhhk.quyu as 'quyu',hhhk.nodeno   from 
                //		                            (

                //                (select a.oilno, count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end)  as 'quyu'
                //				                             from his_oilvouch_bycash2022 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                //				                             where   a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
                //				                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end ) 
                //			                            )
                //			                            union all 
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end ) as 'quyu'
                //					                             from oildetail2022 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
                //		                            group by a.oilno,b.nodeName,a.nodeno, 
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end )
                //		                            )
                //                                     union all
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end)  as 'quyu'
                //					                             from unlocal_detail2022  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //		                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end )
                //		                            )
                //union all
                //(select  a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end)  as 'quyu'
                //				                             from his_oilvouch_bycash2021 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                //				                             where   a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //				                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end ) 
                //			                            )
                //			                            union all 
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end ) as 'quyu'
                //					                             from oildetail2021 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //		                            group by a.oilno,b.nodeName,a.nodeno, 
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end )
                //		                            )
                //                                     union all
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                           ( case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end)  as 'quyu'
                //					                             from unlocal_detail2021  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //		                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            (case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end )
                //		                            )


                //union all 
                //(select  a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end  as 'quyu'
                //				                             from his_oilvouch_bycash2020 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                //				                             where   a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //				                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end  
                //			                            )
                //			                            union all
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end  as 'quyu'
                //					                             from oildetail2020 a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //		                            group by a.oilno,b.nodeName,a.nodeno, 
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end 
                //		                            )
                //                                     union all
                //			                            (select a.oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',a.nodeno,
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end  as 'quyu'
                //					                             from unlocal_detail2020  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                //		                              where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno
                //		                            group by a.oilno,b.nodeName,a.nodeno,
                //				                            case when left(c.oucode,6) ='100047'then '海口'
                //				                                    when left(c.oucode,6) ='100048'then  '三亚'
                //				                                    when left(c.oucode,6) ='100049'then '儋州'
                //				                                    when left(c.oucode,6) ='100050'then '琼海'
                //                                                    when left(c.oucode,6) ='100051'then '万宁'
                //                                                    when left(c.oucode,6) ='100052'then '澄迈'
                //					                            else 'zhong' end 
                //		                            )
                //                             ) as hhhk
                //                            group by hhhk.yzmc,hhhk.quyu,hhhk.nodeno,hhhk.oilno
                //                            order by hhhk.yzmc,hhhk.quyu
                //                ", workday_sqyyjxs, workday_dqyyjxs, oilno);//  -- 轻油夜间销售 旧码查询
                #endregion

                sql_dzqb = string.Format(@"   
 	                         declare @strat datetime
declare @end datetime
declare @oilno varchar(20)

set @strat=CONVERT(datetime,'{0}')
set @end =CONVERT(datetime,'{1}')
set @oilno='%{2}%'
select  hhhk.oilno,sum(hhhk.tqs) as 'tqs',sum(hhhk.xsje) as 'xsje',sum(hhhk.xsl) as 'xsl',hhhk.yzmc as 'yzmc',hhhk.quyu as 'quyu',hhhk.nodeno   from 
		                            (

                (select (case 
                        when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                        when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                        when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                        when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                        when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                        end) as oilno, count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu' 
				           from his_oilvouch_bycash{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                                    inner join oiltype k on  k.sinopec_code=a.oilno
				                             where   a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' 
				                            group by (case 
                                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                                end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) 
			                            )
			      union all 
			                (select (case 
                                    when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                    when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                    when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                    when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                    when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                    end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end ) as 'quyu'
					              from oildetail{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                           inner join oiltype k on  k.sinopec_code=a.oilno                                 
		                    where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047' 
		                group by (case 
                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                end),b.nodeName,b.sinopec_nodeno, 
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end )
		                )
                  union all
			                (select (case 
                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                ( case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end)  as 'quyu'
					                    from unlocal_detail{3}  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                            inner join oiltype k on  k.sinopec_code=a.oilno
		                    where  a.opetime>=@strat and a.opetime<@end and DATEPART(HOUR,a.opetime) in (0,1,2,3,4,5,6) and  left(c.OUCode,6)  >='100047'
		                group by (case 
                                when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                end),b.nodeName,b.sinopec_nodeno,
				                (case when left(c.oucode,6) ='100047'then '海口'
				                        when left(c.oucode,6) ='100048'then  '三亚'
				                        when left(c.oucode,6) ='100049'then '儋州'
				                        when left(c.oucode,6) ='100050'then '琼海'
                                        when left(c.oucode,6) ='100051'then '万宁'
                                        when left(c.oucode,6) ='100052'then '澄迈'
					                else 'zhong' end )
		                            )

                             ) as hhhk
                            group by hhhk.yzmc,hhhk.quyu,hhhk.nodeno,hhhk.oilno
                            order by hhhk.yzmc,hhhk.quyu
                ", workday_sqyyjxs, workday_dqyyjxs, oilno, ko_s.ToString("yyyy"));//  -- 轻油夜间销售 当年查询
                // workday_sqyyjxs.Substring(0,3)   "2022"
                Console.Write(workday_sqyyjxs.Substring(0, 3) + "*************8");
                System.Console.Write(workday_sqyyjxs.Substring(0, 3));
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "lingshou_quyu_yejianxiaoshou");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_yejianxiaoshou"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 零售油站区域打桶销售查询
        /// </summary>
        /// <param name="workday_sqyyjxs"></param>
        /// <param name="workday_dqyyjxs"></param>
        /// <returns></returns>
        [HttpGet]
        public string lingshou_quyu_datongxiaoshou_chaxun(string workday_sqyyjxs = "", string workday_dqyyjxs = "", string oilno = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_sqyyjxs);
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
 	                         declare @strat datetime
                            declare @end datetime
							declare @oilno varchar(20)
                            set @strat=CONVERT(datetime,'{0}')
                            set @end =CONVERT(datetime,'{1}')
							set @oilno='%{2}%'
                            select  hhhk.oilno,sum(hhhk.tqs) as 'tqs',sum(hhhk.xsje) as 'xsje',sum(hhhk.xsl) as 'xsl',hhhk.yzmc as 'yzmc',hhhk.quyu as 'quyu',hhhk.nodeno   from 
		                            (

                (select (case 
                        when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                        when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                        when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                        when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                        when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                        end) as oilno, count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu' 
				                             from his_oilvouch_bycash{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode  
                                                inner join oiltype k on  k.sinopec_code=a.oilno
				                             where   a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
				                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) 
			                            )
			                            union all 
			                            (select (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end ) as 'quyu'
					                             from oildetail{3} a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                                          inner join oiltype k on  k.sinopec_code=a.oilno
		                              where  a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047' and a.oilno like @oilno 
		                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno, 
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end )
		                            )
                                     union all
			                            (select (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) as oilno,count(a.opetime) as 'tqs',sum(a.Amount) as 'xsje',sum(a.litter) as 'xsl',b.nodeName as 'yzmc',b.sinopec_nodeno as nodeno,
				                           ( case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end)  as 'quyu'
					                             from unlocal_detail{3}  a inner join nodeinfor b  on a.nodeno=b.nodeno inner join SYS_OrgUnit c on b.sinopec_nodeno=c.UniversalCode 
                                                        inner join oiltype k on  k.sinopec_code=a.oilno
		                              where  a.opetime>=@strat and a.opetime<@end and a.litter>=200 and  left(c.OUCode,6)  >='100047'and a.oilno like @oilno 
		                            group by (case 
                                            when PATINDEX('%柴油%', LOWER(k.content)) > 0  then '柴油'
                                            when PATINDEX('%95%', LOWER(k.content)) > 0  then '95汽油'
                                            when PATINDEX('%92%', LOWER(k.content)) > 0  then '92汽油'
                                            when PATINDEX('%98%', LOWER(k.content)) > 0  then '98汽油'
                                            when PATINDEX('%天然气%', LOWER(k.content)) > 0  then '天然气'
                                            end) ,b.nodeName,b.sinopec_nodeno,
				                            (case when left(c.oucode,6) ='100047'then '海口'
				                                    when left(c.oucode,6) ='100048'then  '三亚'
				                                    when left(c.oucode,6) ='100049'then '儋州'
				                                    when left(c.oucode,6) ='100050'then '琼海'
                                                    when left(c.oucode,6) ='100051'then '万宁'
                                                    when left(c.oucode,6) ='100052'then '澄迈'
					                            else 'zhong' end )
		                            )

                             ) as hhhk
                            group by hhhk.yzmc,hhhk.quyu,hhhk.nodeno,hhhk.oilno
                            order by hhhk.yzmc,hhhk.quyu


                ", workday_sqyyjxs, workday_dqyyjxs, oilno, ko_s.ToString("yyyy"));//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "lingshou_quyu_datongxiaoshou_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["lingshou_quyu_datongxiaoshou_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }


        /// <summary>
        /// 海信券消费查询-油站
        /// </summary>
        /// <param name="workday_shxqxfcx"></param>
        /// <param name="workday_dhxqxfcx"></param>
        /// <returns></returns>
        //*******99*****************
        [HttpGet]
        public string haixinquan_xiaofei_chaxun(string workday_shxqxfcx = "", string workday_dhxqxfcx = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_jk_xs_kk = new DataSet();
            try
            {
                //conn.Open();

                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxqxfcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxqxfcx);
                DataTable dt_jk_xs = hx_hk.quan_xiaoshou_mingxi(ko_s, ko_e);   //接口返回数据_实时流水
                dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_jk_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// APP自提订单查询
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="djzbillno"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdan_chaxun(string workday_sappddcx = "", string workday_dappddcx = "", string orgname = "", string djzbillno = "", string orgcode = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                DataTable dt_jk_xs = hx_hk.APP_ziti_xiaoshou_chaxun(ko_s, ko_e, orgname, djzbillno, orgcode);   //接口返回数据_实时流水
                                                                                                                //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }

        /// <summary>
        /// APP自提订单汇总查询
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="djzbillno"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdanhuizong_chaxun(string workday_sappddcx = "", string workday_dappddcx = "", string orgname = "", string djzbillno = "", string orgcode = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                DataTable dt_jk_xs = hx_hk.APP_ziti_xiaoshouhuizong_chaxun(ko_s, ko_e, orgname, djzbillno, orgcode);   //接口返回数据_实时流水
                                                                                                                       //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }

        /// <summary>
        /// APP自提订单汇总月查询
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="djzbillno"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdanhuizong_yuechaxun(string workday_sappddcx = "", string workday_dappddcx = "", string orgname = "", string djzbillno = "", string orgcode = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                DataTable dt_jk_xs = hx_hk.APP_ziti_xiaoshouhuizong_yuechaxun(ko_s, ko_e, orgname, djzbillno, orgcode);   //接口返回数据_实时流水
                                                                                                                          //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }


        /// <summary>
        /// APP自提订单明细查询（团购订单）
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdan_mingxi_chaxun(string billno = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                DataTable dt_jk_xs = hx_hk.APP_ziti_xiaoshoumingxi_chaxun(billno);   //接口返回数据_实时流水
                                                                                     //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }

        /// <summary>
        /// APP自提订单支付方式明细查询
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="orgcode"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdan_zhifumingxi_chaxun(string workday_sappddcx = "", string workday_dappddcx = "", string orgname = "", string orgcode = "", string billno = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                DataTable dt_jk_xs = hx_hk.APP_ziti_zhifufangshi_mingxi_chaxun(ko_s, ko_e, orgname, orgcode, billno);   //接口返回数据_实时流水
                                                                                                                        //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }

        /// <summary>
        /// APP自提订单支付方式汇总查询
        /// </summary>
        /// <param name="workday_sappddcx"></param>
        /// <param name="workday_dappddcx"></param>
        /// <param name="orgname"></param>
        /// <param name="orgcode"></param>
        /// <returns></returns>
        [HttpGet]
        public string APP_zitidingdan_zhifuhuizong_chaxun(string workday_sappddcx = "", string workday_dappddcx = "", string orgname = "", string orgcode = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds

            DataSet dt_zt_xs_kk = new DataSet();
            try
            {
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_sappddcx);
                DateTime ko_e = Convert.ToDateTime(workday_dappddcx);
                DataTable dt_jk_xs = hx_hk.APP_ziti_zhifufangshi_huizong_chaxun(ko_s, ko_e, orgname, orgcode);   //接口返回数据_实时流水
                                                                                                                 //dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                dt_zt_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_zt_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {

            }
            return result;

        }

        /// <summary>
        /// 海信验收单查询
        /// </summary>
        /// <param name="workday_shxqxfcx"></param>
        /// <param name="workday_dhxqxfcx"></param>
        /// <returns></returns>
        [HttpGet]
        public string haixin_yanshoudan_chaxun(string workday_shxysdcx = "", string workday_dhxysdcx = "", string SUPNAME = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_jk_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxysdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxysdcx);
                DataTable dt_jk_xs = hx_hk.HX_yanshoudan_chaxun(ko_s, ko_e, SUPNAME);   //接口返回数据_实时流水
                dt_jk_xs_kk.Tables.Add(dt_jk_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_jk_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 海信洗车商品支付方式汇总查询
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <returns></returns>
        [HttpGet]
        public string haixin_xiche_zhifufangshi_huizongchaxun(string workday_shxysdcx = "", string workday_dhxysdcx = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxysdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxysdcx);
                DataTable dt_xc_xs = hx_hk.HX_xiche_zhifufangshi_huizongchaxun(ko_s, ko_e);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 海信团购单同步执行方法---未启用
        /// </summary>
        /// <param name="billno"></param>
        [HttpGet]
        public void HX_tuangoudan_tongbu(string billno = "")
        {
            Huhk_tongbu huhk_Tongbu = new Huhk_tongbu();
            huhk_Tongbu.tuangoudingdan_tongbu(billno);
        }

        /// <summary>
        /// 云平台团购单查询
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_tuangoudan_chaxun(string billno = "")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                //sql_dzqb = string.Format(@"   
                //    declare @billno varchar(50)
                //    set @billno='{0}'
                //    select * FROM HX_tuangoudan where billno=@billno;

                //", billno);//  -- 加油站加油卡占比查询
                //sql_dzqb = string.Format(@"   

                //    declare @billno varchar(100)
                //    set @billno='{0}'   -- 1PFXS202207200066
                //    SELECT  A.ID,A.BILLNO,A.SERIALNO,A.PLUID,A.PLUCODE,B.LEIBIE_YAN 
                //          ,A.PLUNAME,A.BARCODE,B.xiaolei_name,B.zhonglei_name,B.dalei_name
                //          ,A.SPEC ,A.UNIT,A.PACKUNIT ,A.PACKQTY ,A.PFCOUNT ,A.HJPRICE
                //       ,B.HETONG_price ,A.PRICE ,A.PFPRICE,B.shiji_price   
                //       ,ROUND(convert(float,A.PFPRICE)-convert(float,B.shiji_price),2) SGJC   -- 申请价-规定价>0
                //       ,ROUND((convert(float,A.PFPRICE)-convert(float,A.HJPRICE))/(1+convert(float,A.XTAXRATE)/100),2)  U
                //       ,ROUND((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100),2)  V
                //       ,A.YSTOTAL      ,A.SSTOTAL
                //       ,ROUND((convert(float,A.PFCOUNT)*convert(float,B.shiji_price))/(1+convert(float,A.XTAXRATE)/100),2)   W
                //       ,ROUND(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100),2)  X
                //       ,ROUND((convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100) -(convert(float,A.PFCOUNT)*convert(float,B.shiji_price)/(1+convert(float,A.XTAXRATE)/100))),2)  Y  -- x-w
                //       ,ROUND(convert(float,A.PFCOUNT)*(convert(float,A.PFPRICE)-convert(float,A.HJPRICE)/(1+convert(float,A.XTAXRATE)/100)),2)  Z   -- o-u
                //       ,ROUND((convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100))),2)  AA   --o*v
                //       ,ROUND(convert(float,A.PFCOUNT)*(convert(float,A.PFPRICE)-convert(float,A.HJPRICE)/(1+convert(float,A.XTAXRATE)/100))/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100)),2) AB    -- Z/X
                //       ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100))*(1+convert(float,A.XTAXRATE)/100)/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)),2) AC   -- AA/X
                //          ,A.XTAXRATE      ,A.XTAXTOTAL      ,A.HJTOTAL      ,A.HMLTOTAL      ,A.HMLRATE      ,A.HCOST 
                //      FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan] A left JOIN HX_shangpin_xinxi B ON A.PLUCODE=B.PLUCODE
                //      WHERE A.BILLNO=@billno order by A.ID

                //", billno);//  -- 加油站加油卡占比查询
                sql_dzqb = string.Format(@"
                   				declare @billno varchar(100)
                    set @billno='{0}'   -- 1PFXS202207200066
                    (SELECT  '' orgcode,'' orgname,
					'' BILLNO,'' SERIALNO,'' PLUID,'' PLUCODE,'' LEIBIE_YAN 
                          ,'' PLUNAME,'' BARCODE,'' xiaolei_name,'' zhonglei_name,'' dalei_name
                          ,'' SPEC ,'' UNIT,'' PACKUNIT ,'' PACKQTY ,ISNULL(sum(convert(float,a.PFCOUNT)),0) PFCOUNT  ,'' HJPRICE
                       ,'' HETONG_price ,'' PRICE ,'' PFPRICE,'' guiding_TG_price,'合计' shiji_price   
                       , ISNULL(sum(a.tgjc),0) TGJC   -- 申请价-规定价>0
                       , ISNULL(sum(a.U),0) U
                       , ISNULL(sum(a.V),0) V
                       , ISNULL(sum(convert(float,a.YSTOTAL)),0) YSTOTAL      ,ISNULL(sum(convert(float,a.SSTOTAL)),0) SSTOTAL
                       , ISNULL(sum(a.W),0) W
                       , ISNULL(sum(a.X),0) X
                       , ISNULL(sum(a.x-a.w),0) Y  -- x-w
                       , ISNULL(sum(a.Z),0) Z   -- o-u
                       , ISNULL(sum(a.AA),0) AA   --o*v
                       , ISNULL(sum(a.Z),0)*100/NULLIF(sum(a.X),0) AB    -- Z/X
                       , ISNULL(sum(a.AA),0)*100/NULLIF(sum(a.X),0) AC   -- AA/X
                       ,'' XTAXRATE  ,'' XTAXTOTAL  ,'' HJTOTAL  ,'' HMLTOTAL  ,'' HMLRATE  ,'' HCOST 


                      FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan_ler] A left join  HX_tuangoudan_head b on a.billno=b.billno
                      WHERE A.BILLNO=@billno -- and CONVERT(float,a.tgjc)<0
					  )
                    union all
                    (SELECT  b.orgcode,b.orgname,
					A.BILLNO,A.SERIALNO,A.PLUID,A.PLUCODE,A.LEIBIE_YAN 
                          ,A.PLUNAME,A.BARCODE,A.xiaolei_name,A.zhonglei_name,A.dalei_name
                          ,A.SPEC ,A.UNIT,A.PACKUNIT ,A.PACKQTY ,A.PFCOUNT ,A.HJPRICE
                       ,A.HETONG_price ,A.PRICE ,A.PFPRICE,A.guiding_TG_price,A.shiji_price   
                       , A.TGJC   -- 申请价-规定价>0
                       , A.U
                       , A.V
                       , A.YSTOTAL      ,A.SSTOTAL
                       , A.W
                       , A.X
                       , (A.x-A.w) Y   -- A.Y  -- x-w
                       , A.Z   -- o-u
                       , A.AA   --o*v
                       , A.AB*100 AB   -- Z/X
                       , A.AC*100  AC  -- AA/X
                       ,A.XTAXRATE  ,A.XTAXTOTAL  ,A.HJTOTAL  ,A.HMLTOTAL  ,A.HMLRATE  ,A.HCOST 
                      FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan_ler] A left join  HX_tuangoudan_head b on a.billno=b.billno
                      WHERE A.BILLNO=@billno 
					  )
					  

                ", billno);
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "HX_tuangoudan_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["HX_tuangoudan_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 团购单-烟草类型库存占比展示
        /// </summary>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_tuangoudan_yancao(string billno = "")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   

   --biao e是加油站的全部香烟库存  16行有问题 --03161 无商品资料
       declare @sum_dingdan_count int
   declare @sum_shiji_count int
   declare @billno varchar(100)
   set @billno='{0}'
   -- 团购单数量汇总
   set @sum_dingdan_count=(select sum(convert(int,h.pfcount))   from HX_tuangoudan h  
						where h.billno=@billno)
	-- 团购单中对应实际香烟库存
   -- set @sum_shiji_count=( select sum(convert(int,he.SUM_KCCOUNT))  from 
			--			HX_tuangoudan ha   left join 
			--			(select hc.SUM_KCCOUNT,hc.plucode,hd.orgcode from HX_xiangyan_kucun hc inner join HX_tuangoudan_head hd on 
			--			 hd.rptdate=hc.workday where hd.billno=@billno and hd.orgcode = hc.orgcode) he
			--			on ha.PLUCODE=he.plucode
			--	 where  ha.billno=@billno)
begin
  select b.leibie_yan , a.billno,  
	ROUND(ISNULL(sum(convert(int,a.pfcount)), 0),2) pfcount,
    ROUND(ISNULL(sum(convert(float,a.pfcount)), 0)*100/@sum_dingdan_count,2)  as sum_pfcount_zhanbi,
    ROUND(ISNULL(@sum_dingdan_count, 0),2)  as sum_pfcount ,
	ROUND(ISNULL(sum(convert(int,e.SUM_KCCOUNT)), 0),2)  SUM_KCCOUNT
    -- ,ROUND(ISNULL(@sum_shiji_count, 0),2) sum_shiji_count
  from 
  HX_tuangoudan a left join HX_shangpin_xinxi b on a.PLUCODE=b.PLUCODE
  left join 
  (select c.SUM_KCCOUNT,c.plucode,d.orgcode from HX_xiangyan_kucun c inner join HX_tuangoudan_head d on 
  d.rptdate=c.workday where d.billno=@billno and d.orgcode = c.orgcode) e
  on a.PLUCODE=e.plucode
  where  a.billno=@billno
  group by b.leibie_yan, a.BILLNO
  end
                             
                ", billno);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "HX_yancao_kucun_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["HX_yancao_kucun_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 海信销售明细云平台查询
        /// </summary>
        /// <param name="fengongsi"></param>
        /// <param name="shixian"></param>
        /// <param name="orgcode"></param>
        /// <param name="orgname"></param>
        /// <param name="weizhi"></param>
        /// <param name="leixing"></param>
        /// <param name="xxlx"></param>
        /// <param name="rptdate_sta"></param>
        /// <param name="rptdate_end"></param>
        /// <param name="dalei"></param>
        /// <param name="zhonglei"></param>
        /// <param name="xiaolei"></param>
        /// <param name="plucode"></param>
        /// <param name="pluname"></param>
        /// <param name="jhjymode"></param>
        /// <param name="splx1"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_xiaoshou_mingxi_read(string fengongsi = "", string shixian = "", string orgcode = "", string orgname = "", string weizhi = "", string leixing = "", string xxlx = "", string rptdate_sta = "", string rptdate_end = "", string dalei = "", string zhonglei = "", string xiaolei = "", string plucode = "", string pluname = "", string splx3 = "", string splx1 = "")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                  --jymode 0-经销；1-代销；2-联销   IsSelfBrand   统采   IsImpPlu   非重点商品
                  select khkn.fengongsi,khkn.shixian,khkn.orgcode,khkn.orgname,khkn.weizhi,khkn.leixing,
                       khkn.xxlx,khkn.rptdate,khkn.dalei,khkn.zhonglei,khkn.xiaolei,khkn.plucode,khkn.pluname
                       ,khkn.splx1,khkn.splx2,khkn.splx3,khkn.unit,khkn.spec,khkn.xscount,khkn.price,khkn.hxtotal
                       ,khkn.xtaxrate,khkn.wxtotal,khkn.wjcost,khkn.wmtotal,khkn.wlmll 
                  from 
                  (
                       select b.fengongsi,b.shixian,b.orgcode,b.orgname, b.weizhi,b.leixing
					      ,case  when  a.datatype != 'D'  then '零售'
							    when a.datatype='D' and a.cxdatatype='5' then '团购'
							    when a.datatype='D' and a.cxdatatype='6' then ' '
							    when a.datatype='D' and a.cxdatatype='7' then '名特优销售'
							    when a.datatype='D' and a.cxdatatype='4' then '内部销售'
							    when a.datatype='D' and a.cxdatatype='13' then '一体化提货'
							    when a.datatype='D' and a.cxdatatype in ('8','H') then '提货'
						     end as xxlx 
					      ,a.rptdate   --
                          ,(select hnk.clsname  from HX_shangpin_pinlei as hnk 
							    where hnk.clscode= left(a.clscode,2) ) as dalei  
					      ,(select hnk_z.clsname  from HX_shangpin_pinlei as hnk_z 
							    where hnk_z.clscode= left(a.clscode,4) ) as zhonglei
					      ,cb.clsname  as xiaolei ,a.plucode,a.pluname
					      ,'统采' as splx1
					      ,'非重点商品' as splx2    --
					      ,case  when a.jymode='0' then '经销' 
							     when a.jymode= '1' then '联销' 
							     end as splx3
                          ,a.unit,a.spec,a.xscount,a.price,a.hxtotal,a.xtaxrate,a.wxtotal,a.wjcost,a.wmtotal
                          , ROUND((convert(float,a.wmtotal)/convert(float,a.wxtotal))*100,2)  as wlmll

                      from HX_tSalPluDetail a left join HX_shop_leixing b on a.orgcode= b.orgcode    
                          left join HX_shangpin_pinlei cb on a.clscode=  cb.clscode

                      where  a.wxtotal!='0' and  b.fengongsi like '%{0}%' and shixian like '%{1}%' and b.orgcode like '%{2}%' 
                             and b.orgname like '%{3}%' and b.weizhi like '%{4}%' and b.leixing like '%{5}%' 
                             and a.rptdate >='{6}' and a.rptdate <='{7}'and a.plucode like '%{8}%' and a.pluname like '%{9}%' 

                    ) khkn where khkn.xxlx like '%{10}%'
                         and khkn.dalei like '%{11}%'  and khkn.zhonglei like '%{12}%'  and khkn.xiaolei like '%{13}%'
                          and khkn.splx3 like '%{14}%'  and khkn.splx1 like '%{15}%'
                             
                ", fengongsi, shixian, orgcode, orgname, weizhi, leixing, rptdate_sta, rptdate_end, plucode, pluname, xxlx, dalei,
                    zhonglei, xiaolei, splx3, splx1);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "HX_yancao_kucun_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["HX_yancao_kucun_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 业务报表
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        public string HX_yewubaobiao_read(string fengongsi="") 
        {
            return fengongsi ;
        }

        /// <summary>
        /// rpt_cloud_daily_hn_StationOnWaterDailySales    海上日报表  不剔除那三个海上站
        /// lingshou_fengongsi_HS_baobiao
        /// </summary>
        /// <param name="workday"></param>
        /// <returns></returns>
        [HttpGet]
        public string lingshou_fengongsi_HS_baobiao(string workday = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            string workday_A = (workday.Replace("-",string.Empty));  //把2022-10-22  调整为20221022 

            try
            {
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
              -----海上加油站销售日报表
--ALTER PROC [dbo].[rpt_cloud_daily_hn_StationOnWaterDailySales]
DECLARE @v_jsrq DATE ,
            @v_scts DECIMAL ,/*生产天数*/
            @v_dyzts DECIMAL ,/*总天数*/
            @v_ycrq DATE ,/*开始日期月份的月初日期*/
            @v_ymrq DATE ,/*结束日期月份的月末日期*/
            @v_where_jyfs VARCHAR(50) = '-1' ,/*经营方式where条件变量*/
            @v_where_hzfs VARCHAR(50)= '-1' ,/*汇总方式where条件变量*/
            @v_ksny VARCHAR(6) , /*开始年月*/
            @v_jsny VARCHAR(6) ,/*结束年月*/
            @v_ncrq DATE ,/*年初日期*/
            @v_tbrq DATE ,
            @v_hbrq DATE, 
			@V_RQ VARCHAR(8);

		set @V_RQ = '{0}' ; /*日期*/ 

        SET @v_jsrq = CONVERT(DATE, @V_RQ); 
        SET @v_ncrq = DATEADD(yy, DATEDIFF(yy, 0, @v_jsrq), 0);  /*年初日期*/
        SET @v_ycrq = DATEADD(DD, -DAY(@v_jsrq) + 1, @v_jsrq); /*月初日期*/
        SET @v_ymrq = DATEADD(DD, -DAY(DATEADD(M, 1, @v_jsrq)),
                              DATEADD(M, 1, @v_jsrq));/*月末日期*/
        SET @v_scts = DAY(@v_jsrq);--DATEDIFF(DAY, @V_KSRQ, @V_JSRQ) + 1; /*生产天数*/
        SET @v_dyzts = DAY(@v_ymrq); --DATEDIFF(DAY, @v_ksrq_ycrq, @v_jsrq_ymrq) + 1;/*总天数*/

        SET @v_ksny = CONVERT(VARCHAR(6), @v_ncrq, 112); /*开始年月*/
        SET @v_jsny = CONVERT(VARCHAR(6), @v_jsrq, 112);/*结束年月*/
        SET @v_tbrq = DATEADD(YEAR, -1, @v_jsrq);
        SET @v_hbrq = DATEADD(MONTH, -1, @v_jsrq);


SELECT  -- ROW_NUMBER() OVER ( ORDER BY T.dysjwcjd DESC ) 'rank' ,/*排名*/

						case T.BigAreaname 
                            when  '海口分公司' then '1.海口'
						    when '三亚分公司' then '2.三亚'
						    when '儋州分公司' then '3.儋州'
						    when '琼海分公司' then '4.琼海'
						    when '万宁分公司' then '5.万宁'
						    when '澄迈分公司' then '6.澄迈'
						    end  BigAreaname, --分公司
						sum(T.dyMonSalesPlan) dyMonSalesPlan,--当月计划量
						sum(t.dyyrj) dyyrj,       -- 当月应日均
						sum(t.dysjrj) dysjrj,       -- 当月实际日均
						t.dyywcjd*100  dyywcjd,  --sum(T.dyMonSalesPlan)/@v_dyzts dyywcjd ,--当月应完成进度
						-- t.dysjwcjd, --当月实际完成进度
						-- t.dyqjd ,    --当月欠进度
						ISNULL(ISNULL(sum(t.MonthlySales), 0)
                                           / NULLIF(sum(T.dyMonSalesPlan), 0), 0)*100 'dysjwcjd' , --当月实际完成进度
                                    ISNULL(ISNULL(sum(t.MonthlySales), 0)
                                           / NULLIF(sum(T.dyMonSalesPlan), 0)
                                           - ISNULL(t.dyywcjd, 0), 0)*100 'dyqjd' , --当月欠进度
						sum(t.MonthlySales) MonthlySales,   /*当月累日销量合计*/
						sum(t.lr92) lr92,         /*当月累日销量92#*/
						sum(t.lr95) lr95,          /*当月累日销量95#*/
						sum(t.lr98) lr98,          /*当月累日销量98#*/
						sum(t.lr0) lr0,           /*当月累日销量0#*/
						sum(t.lrrly) lrrly,          /*当月累日销量燃料油*/
						sum(t.lrCNG) lrCNG,        /*当月累日销量CNG*/
						sum(t.lrLNG) lrLNG,        /*当月累日销量LNG*/
						-- sum(t.dyqy) dyqy,         /*当月汽油总销量*/   --有问题
						-- sum(t.dycy) dycy,		/*当月柴油总销量*/
						sum(t.SalesVol) SalesVol,		/*当日销量合计*/
						sum(t.dr92) dr92,	/*当日销量92#*/
                        sum(t.dr95) dr95,	/*当日销量95#*/
                        sum(t.dr98) dr98,	/*当日销量98#*/
                        sum(t.dr0 ) dr0,		/*当日销量dr0*/
                        sum(t.drrly) drrly,	/*当日销量燃料油*/
                        sum(t.drCNG) drCNG,	/*当日销量CNG*/
                        sum(t.drLNG) drLNG,	/*当日销量LNG*/
                        sum(t.tqMonthlySales) tqMonthlySales,/*同比情况同期销量总量*/
                        sum(t.tq92) tq92,	/*同比情况同期销量92#*/
                        sum(t.tq95) tq95,	/*同比情况同期销量95#*/
                        sum(t.tq98) tq98,	/*同比情况同期销量98#*/
                        sum(t.tq0) tq0,		/*同比情况同期销量0#*/
                        sum(t.tqrly) tqrly,	/*同比情况同期销量燃料油*/
                        sum(t.tbzfzl) tbzfzl,	/*同比增幅总量*/
                        sum(t.tbzfqy) tbzfqy,	/*同比增幅汽油*/
                        sum(t.tbzfcy) tbzfcy,	/*同比增幅柴油*/
                        sum(t.tbzfrly) tbzfrly, /*同比增幅燃料油*/
                        -- sum(t.tqqy) tqqy,	/*同期汽油总销量*/
                        -- sum(t.tqcy) tqcy,	/*同期汽油总销量*/
                        -- t.tbzl ,	/*同比总量*/
                        -- t.tbqy ,	/*同比汽油*/
                        -- t.tbcy ,	/*同比柴油*/
                        -- t.tbrly ,	/*同比燃料油*/
						ISNULL(( ISNULL(sum(t.MonthlySales), 0)
                                             - ISNULL(sum(t.tqMonthlySales), 0) )
                                           / NULLIF(sum(t.tqMonthlySales), 0), 0)*100  'tbzl' ,/*同比总量*/
                                    ISNULL(( ISNULL(sum(t.dyqy), 0)
                                             - ISNULL(sum(t.tqqy ), 0) )
                                           / NULLIF(sum(t.tqqy ), 0), 0)*100 'tbqy' ,/*同比汽油*/
                                    ISNULL(( ISNULL(sum(t.dycy), 0)
                                             - ISNULL(sum(t.tqcy), 0) )
                                           / NULLIF(sum(t.tqcy), 0), 0)*100  'tbcy' ,/*同比柴油*/
                                    ISNULL(( ISNULL(sum(t.lrrly), 0)
                                             - ISNULL(sum(t.tqrly), 0) )
                                           / NULLIF(sum(t.tqrly), 0), 0)*100  'tbrly' , /*同比燃料油*/
                        sum(t.syMonthlySales) syMonthlySales,/*上月环比总销量*/
                        sum(t.sy92) sy92,	/*上月环比92#销量*/
                        sum(t.sy95) sy95,	/*上月环比95#销量*/
                        sum(t.sy98) sy98,	/*上月环比92#销量*/
                        sum(t.sy0) sy0,		/*上月环比92#销量*/
                        sum(t.syrly) syrly,	/*上月环比燃料油销量*/
                        sum(t.hbzfzl) hbzfzl,	/*环比增幅总量*/
                        sum(t.hbzfqy) hbzfqy,	/*环比增幅汽油*/
                        sum(t.hbzfcy) hbzfcy,	/*环比增幅柴油*/
                        sum(t.hbzfrly) hbzfrly,	/*环比增幅燃料油*/
                        -- sum(t.syqy) syqy,	/*上月汽油总销量*/
                        -- sum(t.sycy) sycy,	/*上月柴油总销量*/
                        -- t.hbzl ,	/*环比总量*/
                        -- t.hbqy ,	/*环比汽油*/
                        -- t.hbcy ,	/*环比柴油*/
                        -- t.hbrly		/*环比燃料油*/
						ISNULL(( ISNULL(sum(t.MonthlySales), 0)
                                             - ISNULL(sum(t.syMonthlySales), 0) )
                                           / NULLIF(sum(t.syMonthlySales), 0), 0)*100 'hbzl' ,/*环比总量*/
                                    ISNULL(( ISNULL(sum(t.dyqy), 0)
                                             - ISNULL(sum(t.syqy), 0) )
                                           / NULLIF(sum(t.syqy), 0), 0)*100  'hbqy' ,/*环比汽油*/
                                    ISNULL(( ISNULL(sum(t.dycy), 0)
                                             - ISNULL(sum(t.sycy), 0) )
                                           / NULLIF(sum(t.sycy), 0), 0)*100  'hbcy' ,/*环比柴油*/
                                    ISNULL(( ISNULL(sum(t.lrrly), 0)
                                             - ISNULL(sum(t.syrly), 0) )
                                           / NULLIF(sum(t.syrly), 0), 0)*100 'hbrly'/*环比燃料油*/



                FROM    ( SELECT    -- t6.AreaName ,/*片区名称*/
									t6.BigAreaname,
                                     -- t6.ShortName ,/*加油站名称*/
                                    ISNULL(t5.dyMonSalesPlan, 0) 'dyMonSalesPlan' ,--当月计划量
                                    ISNULL(t5.dyyrj, 0) 'dyyrj' ,--当月应日均
                                    ISNULL(t1.dyMonthlySales / @v_scts, 0) 'dysjrj' , --当月实际日均
                                    ISNULL(t5.dyywcjd, 0) 'dyywcjd' , --当月应完成进度
                                    ISNULL(ISNULL(t1.dyMonthlySales, 0)
                                           / NULLIF(t5.dyMonSalesPlan, 0), 0) 'dysjwcjd' , --当月实际完成进度
                                    ISNULL(ISNULL(t1.dyMonthlySales, 0)
                                           / NULLIF(t5.dyMonSalesPlan, 0)
                                           - ISNULL(t5.dyywcjd, 0), 0) 'dyqjd' , --当月欠进度
                                    ISNULL(t1.dyMonthlySales, 0) MonthlySales ,/*当月累日销量合计*/
                                    ISNULL(t1.lr92, 0) 'lr92' ,/*当月累日销量92#*/
                                    ISNULL(t1.lr95, 0) 'lr95' ,/*当月累日销量95#*/
                                    ISNULL(t1.lr98, 0) 'lr98' ,/*当月累日销量98#*/
                                    ISNULL(t1.lr0, 0) 'lr0' ,/*当月累日销量0#*/
                                    ISNULL(t1.lrrly, 0) 'lrrly' ,/*当月累日销量燃料油*/
                                    ISNULL(t1.lrCNG, 0) 'lrCNG' ,/*当月累日销量CNG*/
                                    ISNULL(t1.lrLNG, 0) 'lrLNG' ,/*当月累日销量LNG*/
                                    ISNULL(t1.dyqy, 0) 'dyqy' ,/*当月汽油总销量*/
                                    ISNULL(t1.dycy, 0) 'dycy' ,/*当月柴油总销量*/
                                    ISNULL(t1.drSalesVol, 0) 'SalesVol' ,/*当日销量合计*/
                                    ISNULL(t1.dr92, 0) 'dr92' ,/*当日销量92#*/
                                    ISNULL(t1.dr95, 0) 'dr95' ,/*当日销量95#*/
                                    ISNULL(t1.dr98, 0) 'dr98' ,/*当日销量98#*/
                                    ISNULL(t1.dr0, 0) 'dr0' ,/*当日销量dr0*/
                                    ISNULL(t1.drrly, 0) 'drrly' ,/*当日销量燃料油*/
                                    ISNULL(t1.drCNG, 0) 'drCNG' ,/*当日销量CNG*/
                                    ISNULL(t1.drLNG, 0) 'drLNG' ,/*当日销量LNG*/
                                    ISNULL(t3.dyMonthlySales, 0) 'tqMonthlySales' ,/*同比情况同期销量总量*/
                                    ISNULL(t3.lr92, 0) 'tq92' ,/*同比情况同期销量92#*/
                                    ISNULL(t3.lr95, 0) 'tq95' ,/*同比情况同期销量95#*/
                                    ISNULL(t3.lr98, 0) 'tq98' ,/*同比情况同期销量98#*/
                                    ISNULL(t3.lr0, 0) 'tq0' ,/*同比情况同期销量0#*/
                                    ISNULL(t3.lrrly, 0) 'tqrly' ,/*同比情况同期销量燃料油*/
                                    ISNULL(t1.dyMonthlySales, 0)
                                    - ISNULL(t3.dyMonthlySales, 0) 'tbzfzl' ,/*同比增幅总量*/
                                    ISNULL(t1.dyqy, 0) - ISNULL(t3.dyqy, 0) 'tbzfqy' ,/*同比增幅汽油*/
                                    ISNULL(t1.dycy, 0) - ISNULL(t3.dycy, 0) 'tbzfcy' ,/*同比增幅柴油*/
                                    ISNULL(t1.lrrly, 0) - ISNULL(t3.lrrly, 0) 'tbzfrly' , /*同比增幅燃料油*/
                                    ISNULL(t3.dyqy, 0) 'tqqy' ,/*同期汽油总销量*/
                                    ISNULL(t3.dycy, 0) 'tqcy' ,/*同期柴油总销量*/
                                    ISNULL(( ISNULL(t1.dyMonthlySales, 0)
                                             - ISNULL(t3.dyMonthlySales, 0) )
                                           / NULLIF(t3.dyMonthlySales, 0), 0) 'tbzl' ,/*同比总量*/
                                    ISNULL(( ISNULL(t1.dyqy, 0)
                                             - ISNULL(t3.dyqy, 0) )
                                           / NULLIF(t3.dyqy, 0), 0) 'tbqy' ,/*同比汽油*/
                                    ISNULL(( ISNULL(t1.dycy, 0)
                                             - ISNULL(t3.dycy, 0) )
                                           / NULLIF(t3.dycy, 0), 0) 'tbcy' ,/*同比柴油*/
                                    ISNULL(( ISNULL(t1.lrrly, 0)
                                             - ISNULL(t3.lrrly, 0) )
                                           / NULLIF(t3.lrrly, 0), 0) 'tbrly' , /*同比燃料油*/
                                    ISNULL(t4.dyMonthlySales, 0) 'syMonthlySales' ,/*上月环比总销量*/
                                    ISNULL(t4.lr92, 0) 'sy92' ,/*上月环比92#销量*/
                                    ISNULL(t4.lr95, 0) 'sy95' ,/*上月环比95#销量*/
                                    ISNULL(t4.lr98, 0) 'sy98' ,/*上月环比92#销量*/
                                    ISNULL(t4.lr0, 0) 'sy0' ,/*上月环比92#销量*/
                                    ISNULL(t4.lrrly, 0) 'syrly' ,/*上月环比燃料油销量*/
                                    ISNULL(t1.dyMonthlySales, 0)
                                    - ISNULL(t4.dyMonthlySales, 0) 'hbzfzl' ,/*环比增幅总量*/
                                    ISNULL(t1.dyqy, 0) - ISNULL(t4.dyqy, 0) 'hbzfqy' ,/*环比增幅汽油*/
                                    ISNULL(t1.dycy, 0) - ISNULL(t4.dycy, 0) 'hbzfcy' ,/*环比增幅柴油*/
                                    ISNULL(t1.lrrly, 0) - ISNULL(t4.lrrly, 0) 'hbzfrly' ,/*环比增幅燃料油*/
                                    ISNULL(t4.dyqy, 0) 'syqy' ,/*上月汽油总销量*/
                                    ISNULL(t4.dycy, 0) 'sycy' ,/*上月柴油总销量*/
                                    ISNULL(( ISNULL(t1.dyMonthlySales, 0)
                                             - ISNULL(t4.dyMonthlySales, 0) )
                                           / NULLIF(t4.dyMonthlySales, 0), 0) 'hbzl' ,/*环比总量*/
                                    ISNULL(( ISNULL(t1.dyqy, 0)
                                             - ISNULL(t4.dyqy, 0) )
                                           / NULLIF(t4.dyqy, 0), 0) 'hbqy' ,/*环比汽油*/
                                    ISNULL(( ISNULL(t1.dycy, 0)
                                             - ISNULL(t4.dycy, 0) )
                                           / NULLIF(t4.dycy, 0), 0) 'hbcy' ,/*环比柴油*/
                                    ISNULL(( ISNULL(t1.lrrly, 0)
                                             - ISNULL(t4.lrrly, 0) )
                                           / NULLIF(t4.lrrly, 0), 0) 'hbrly'/*环比燃料油*/
                          FROM      ( SELECT    tb1.OUCode ,
                                                tb1.UniversalCode ,
                                                tb1.ShortName ,
                                                tb2.AreaCode ,
                                                tb2.AreaName,
												tb2.BigAreaCode,
												tb2.BigAreaName 
                                      FROM      ( SELECT    t1.OUCode ,
                                                            t1.ParentOUCode ,
                                                            t1.UniversalCode ,
                                                            t2.ShortName
                                                  FROM      ( SELECT
																  OUCode ,
																  UniversalCode ,
																  OUName ,
																  ParentOUCode
                                                              FROM
																dbo.SYS_OrgUnit
                                                              WHERE
																ParentOUCode IN (
																		SELECT DISTINCT 
																		YPT_AREACODE
																		FROM
																		dbo.OSMS_YPT_PQDYGX
																		WHERE
																		YPT_AREACODE <> '' )
                                                              AND OULevel = 5
                                                              AND Status = 1
                                                            ) t1
                                                            JOIN ( SELECT
                                                              OUCode ,
                                                              ShortName
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Remark IN (
                                                              SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006' )
                                                              ) t2 ON t2.OUCode = t1.OUCode
                                                ) tb1   
												-------- tb1***
                                                JOIN ( SELECT DISTINCT
                                                              LG_AREANAME AS 'AreaName' ,
                                                              YPT_AREACODE AS 'AreaCode',
															  BigAreaCode,
															  BigAreaName 
                                                       FROM   dbo.OSMS_YPT_PQDYGX
                                                       WHERE  YPT_AREACODE <> ''
                                                     ) tb2 ON tb2.AreaCode = tb1.ParentOUCode
												-------- tb2***
												
                                    ) t6    --**********- t6 -*********
                                    LEFT JOIN ( ---当月计划
                                                SELECT  UnitCode ,
                                                        SUM(MonSalesPlan) 'dyMonSalesPlan' ,
                                                        SUM(MonSalesPlan)
                                                        / @v_dyzts 'dyyrj' , /*应日均*/
                                                        @v_scts / @v_dyzts 'ywc' , /*当月截止目前应完成*/
                                                        @v_scts / @v_dyzts 'dyywcjd' /*应完成进度*/
                                                FROM    dbo.MonthlySalesPlan
                                                WHERE   Monthly = @v_jsny
                                                        AND DistributionChannel = '01'
                                                        AND Category IN ( '01',
                                                              '02' )
                                                      --  AND LEN(UnitCode) = 8
                                                GROUP BY UnitCode
                                              ) t5 ON t5.UnitCode = t6.OUCode
										--**********- t5 -*********
                                    LEFT JOIN ( --当月销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												 SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   WorkDay = @v_jsrq
                                                        AND DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND SalesMode = '01'
                                                        AND Category <> '03'
                                                GROUP BY AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName
                                              ) t1 ON t1.UniversalCode = t6.UniversalCode
										 --**********- t1 -*********
                                    LEFT JOIN ( --去年同期销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   a.WorkDay = @v_tbrq
                                                        AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND a.SalesMode = '01'
                                                        AND a.Category <> '03'
                                                GROUP BY a.AreaCode ,
                                                        a.AreaName ,
                                                        a.UniversalCode ,
                                                        a.ShortName
                                              ) t3 ON t6.UniversalCode = t3.UniversalCode
											  --**********- t3 -*********
                                    LEFT JOIN ( --同年上月期销售情况
                                                SELECT  AreaCode ,
                                                        AreaName ,
                                                        UniversalCode ,
                                                        ShortName ,
                                                         SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) 'dyMonthlySales' ,/*当月累计销售量*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01' )
                                                              THEN MonthlySales
                                                            END) 'dyqy' , /*当月汽油*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 

                                                              THEN MonthlySales
                                                            END) 'dycy' ,/*当月柴油*/
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN MonthlySales
                                                            END) / @v_scts 'dysjrj' , /*实际日均*/
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrrly' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='CNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrCNG' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='LNG' 
                                                              THEN MonthlySales
                                                              ELSE 0
                                                            END) 'lrLNG' ,
                                                        SUM(CASE
                                                              WHEN Category IN (
                                                              '01', '02' )
                                                              THEN SalesVol
                                                            END) 'drSalesVol' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='92#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr92' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='95#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr95' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='98#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr98' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='0#' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'dr0' ,
                                                        SUM(CASE
                                                              WHEN c.Extend1 ='RLY' 
															    THEN SalesVol
                                                              ELSE 0
                                                            END) 'drrly' ,
                                                        SUM(CASE
                                                               WHEN c.Extend1 ='CNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drCNG' ,
                                                        SUM(CASE
                                                             WHEN c.Extend1 ='LNG' 
                                                              THEN SalesVol
                                                              ELSE 0
                                                            END) 'drLNG'
                                                FROM    dbo.OSMS_Daily_Sales_lg a
												left join  (
												 SELECT  a.TypeCode ,
														a.TypeName ,
														a.MaterialCode ,
														a.MaterialName ,
														a.Extend1 ,
														a.extend2 NAME
												FROM    Cloud.dbo.ERP_MaterialClass a
												WHERE   Extend1 <> ''
												) c ON a.GasCode = c.MaterialCode
                                                        JOIN ( SELECT   DISTINCT
                                                              Remark
                                                              FROM
                                                              dbo.SYS_OrgProperty
                                                              WHERE
                                                              Geography = '004006'
                                                             ) b ON a.UniversalCode = b.Remark
                                                WHERE   a.WorkDay = @v_hbrq
                                                        AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                                        AND a.SalesMode = '01'
                                                        AND a.Category <> '03'
                                                GROUP BY a.AreaCode ,
                                                        a.AreaName ,
                                                        a.UniversalCode ,
                                                        a.ShortName
                                              ) t4 ON t6.UniversalCode = t4.UniversalCode
											  --**********- t4 -*********
                        ) T
						group by  T.BigAreaname,t.dyywcjd
						order by
						case T.BigAreaname when  '海口分公司' then '1.海口'
						when '三亚分公司' then '2.三亚'
						when '儋州分公司' then '3.儋州'
						when '琼海分公司' then '4.琼海'
						when '万宁分公司' then '5.万宁'
						when '澄迈分公司' then '6.澄迈'
						end;


                ", workday_A);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "rpt_cloud_daily_hn_StationOnWaterDailySales");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["rpt_cloud_daily_hn_StationOnWaterDailySales"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 调拨单查询-hx_hk.HX_diaobodan(ko_s, ko_e); 
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_diaobodan(string workday_shxdbdcx = "", string workday_dhxdbdcx = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxdbdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxdbdcx);
                DataTable dt_xc_xs = hx_hk.HX_diaobodan(ko_s, ko_e);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 便利店交易笔数查询，hx_hk.HX_bianlidian_bishu(ko_s, ko_e,orgcode,orgname); 
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <param name="orgcode"></param>
        /// <param name="orgname"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_bianlidian_bishu(string workday_shxbscx = "", string workday_dhxbscx = "", string orgcode = "", string orgname = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
                DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);
                DataTable dt_xc_xs = hx_hk.HX_bianlidian_bishu(ko_s, ko_e,orgcode,orgname);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 销售排名-前20名   xiaoshou_pm 当月
        /// </summary>
        /// <param name="workday_shxysdcx"></param>
        /// <param name="workday_dhxysdcx"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_xiaoshou_pm(string workday_shxxspm = "", string workday_dhxxspm = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_shxxspm);
                DateTime ko_e = Convert.ToDateTime(workday_dhxxspm);
                DataTable dt_xc_xs = hx_hk.xiaoshou_pm(ko_s, ko_e);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 海信商品信息查询
        /// </summary>
        /// <param name="plucode"></param>
        /// <param name="pluname"></param>
        /// <param name="orgcode"></param>
        /// <param name="barcode"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_shangpin_xinxi(string plucode = "", string pluname = "", string orgcode = "", string barcode = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DataTable dt_xc_xs = hx_hk.shangpin_shuxing(plucode, orgcode, pluname, barcode);   //接口返回数据_商品信息
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 分公司日报表-汇总查询
        /// 已经调整完毕，去除了三个海上站点，已经去掉了（去年没有去掉），增加了同比，小数位数保留了2位
        /// </summary>
        /// <param name="workday_starfgsrbb"></param>
        /// <param name="workday_endfgsrbb"></param>
        /// <returns></returns>
        [HttpGet]
        public string fengongsi_ribaobiao_huizong(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                           declare @workday_s datetime
                            declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                            declare @workday_sl datetime
                            declare @workday_dl datetime
                            set @workday_sl= DATEADD(year,-1,@workday_s) 
                            set @workday_dl= DATEADD(year,-1,@workday_d) 

                            begin
                            select a.BigAreaCode,a.BigAreaName
							,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
							
							from 
                            (SELECT  a.BigAreaCode , a.BigAreaName,
                                            SUM(CASE WHEN b.Extend1 = '92#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'SalesVola' ,
                                            SUM(CASE WHEN b.Extend1 = '95#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVolb' ,
                                            SUM(CASE WHEN b.Extend1 = '98#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVolc' ,
                                            SUM(CASE WHEN b.Extend1 = '0#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'SalesVold' ,
                                            SUM(CASE WHEN b.Extend1 = 'RLY'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'rly' ,
                                            SUM(CASE WHEN b.Extend1 = 'CNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'CNG' ,
                                            SUM(CASE WHEN b.Extend1 = 'LNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'LNG'
                                FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
	                           --  join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                                WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                        AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                        AND a.SalesMode = '01'  and a.BigAreaCode != '100045'
                                        AND a.Category <> '03'
                                        and a.BigAreaCode <> '*'
			                    --         and c.Geography = '004006'
                                GROUP BY a.BigAreaCode ,  a.BigAreaName)  a 
	                            full join 
	                            (SELECT  aa.BigAreaCode as qn_BigAreaCode , aa.BigAreaName as qn_BigAreaName,
                                            SUM(CASE WHEN bb.Extend1 = '92#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_SalesVola' ,
                                            SUM(CASE WHEN bb.Extend1 = '95#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVolb' ,
                                            SUM(CASE WHEN bb.Extend1 = '98#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVolc' ,
                                            SUM(CASE WHEN bb.Extend1 = '0#'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_SalesVold' ,
                                            SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as 'qn_rly' ,
                                            SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_CNG' ,
                                            SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                        THEN SalesVol
                                                        ELSE 0
                                                END) as  'qn_LNG'
                                FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
	                           --  join SYS_OrgProperty c ON aa.UniversalCode = c.Remark
                                WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl
                                        AND aa.DataType = '01'  and aa.UniversalCode not in ('33350266','33350432','33350040')
                                        AND aa.SalesMode = '01'  and aa.BigAreaCode != '100045'
                                        AND aa.Category <> '03'
                                        and aa.BigAreaCode <> '*'
			                         --    and c.Geography = '004006'
                                GROUP BY aa.BigAreaCode ,  aa.BigAreaName) b on a.BigAreaCode=b.qn_BigAreaCode
                                -- order by a.BigAreaCode
							union all
							select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj 
                    order by a.BigAreaCode
	                            end;


                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "fengongsi_ribaobiao_huizong");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["fengongsi_ribaobiao_huizong"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 分公司海上日报表-汇总查询
        /// 已调整，海上站点去除3个海上站，增加去年的数据同时增加了同比，小数位数保留了2位
        /// </summary>
        /// <param name="workday_sfgsrbb"></param>
        /// <param name="workday_efgsrbb"></param>
        /// <returns></returns>
        [HttpGet]
        public string fengongsi_HS_ribaobiao_huizong(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                          declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
                begin
                select a.BigAreaCode , a.BigAreaName 
				,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'

				from 
				(SELECT  a.BigAreaCode , a.BigAreaName,
                                SUM(CASE WHEN b.Extend1 = '92#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'SalesVola' ,
                                SUM(CASE WHEN b.Extend1 = '95#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVolb' ,
                                SUM(CASE WHEN b.Extend1 = '98#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVolc' ,
                                SUM(CASE WHEN b.Extend1 = '0#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'SalesVold' ,
                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'rly' ,
                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'CNG' ,
                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'LNG'
                    FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
					join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                    WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                            AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
                    GROUP BY a.BigAreaCode ,  a.BigAreaName) a
					inner join 
					(
						SELECT  a.BigAreaCode , a.BigAreaName,
                                SUM(CASE WHEN b.Extend1 = '92#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_SalesVola' ,
                                SUM(CASE WHEN b.Extend1 = '95#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVolb' ,
                                SUM(CASE WHEN b.Extend1 = '98#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVolc' ,
                                SUM(CASE WHEN b.Extend1 = '0#'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_SalesVold' ,
                                SUM(CASE WHEN b.Extend1 = 'RLY'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as 'qn_rly' ,
                                SUM(CASE WHEN b.Extend1 = 'CNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_CNG' ,
                                SUM(CASE WHEN b.Extend1 = 'LNG'
                                            THEN SalesVol
                                            ELSE 0
                                    END) as  'qn_LNG'
                    FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
					join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                    WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                            AND a.DataType = '01'   and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
                    GROUP BY a.BigAreaCode ,  a.BigAreaName
					) b on a.BigAreaCode=b.BigAreaCode

	union all
					select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
						join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
						join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                            AND a.SalesMode = '01'
                            AND a.Category <> '03'
                            and a.BigAreaCode <> '*'
							and c.Geography = '004006'
							) b on a.lj = b.lj    
                    order by a.BigAreaCode 
	                end;

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "fengongsi_HS_ribaobiao_huizong");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["fengongsi_HS_ribaobiao_huizong"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 海信团购单head查询-于天骄用   
        /// 这里的HX_tuangoudan_chaxun和上面的团购系统的方法一样，造成冲突，所以这里的名字调整为HX_tuangoudan_TJ
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="workday_dtgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_tuangoudan_TJ(string workday_stgdcx = "", string workday_dtgdcx = "", string billno = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_tg_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);
                DateTime ko_e = Convert.ToDateTime(workday_dtgdcx);
                DataTable dt_xc_xs = hx_hk.HX_tuangoudan_TJ(billno,ko_s, ko_e);   //接口返回数据_实时流水
                dt_tg_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_tg_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 市县公司日报表汇总查询
        /// 调整完毕，去掉三个海上站点，并且增加了合计，小数点保留了2位
        /// </summary>
        /// <param name="workday_sfgsrbb"></param>
        /// <param name="workday_efgsrbb"></param>
        /// <returns></returns>
        [HttpGet]
        public string ls_shixiangongsi_ribaobiao_huizong(string workday_ssxgsrbb = "", string workday_esxgsrbb = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_ssxgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_esxgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d= '{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 

                    begin
                    select a.BranchCode,a.BranchName
					,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
					from 
                    (SELECT  a.BranchCode , a.BranchName,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
	                   --  join SYS_OrgProperty c ON a.UniversalCode = c.Remark
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*'
			                 --    and c.Geography = '004006'
                        GROUP BY a.BranchCode ,  a.BranchName)  a 
	                    left join 
	                    (SELECT  aa.BranchCode as qn_BranchCode , aa.BranchName as qn_BranchName,
                                    SUM(CASE WHEN bb.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN bb.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN bb.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN bb.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
	                   --  join SYS_OrgProperty c ON aa.UniversalCode = c.Remark
                        WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl
                                AND aa.DataType = '01'  and aa.UniversalCode not in ('33350266','33350432','33350040')
                                AND aa.SalesMode = '01'  and aa.BigAreaCode != '100045001'
                                AND aa.Category <> '03'
                                and aa.BigAreaCode <> '*'
			                   --  and c.Geography = '004006'
                        GROUP BY aa.BranchCode ,  aa.BranchName) b on a.BranchCode=b.qn_BranchCode
				union all
				select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'合计'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'  and a.BigAreaCode != '100045001'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj   
                        order by a.BranchCode
	                    end;

						

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "shixiangongsi_ribaobiao_huizong");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["shixiangongsi_ribaobiao_huizong"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 加油站日报表汇总查询
        /// 已经调整完毕，去掉三个海上站点，增加合计，同时小数点留2位
        /// </summary>
        /// <param name="workday_syzrbb"></param>
        /// <param name="workday_eyzrbb"></param>
        /// <returns></returns>
        [HttpGet]
        public string ls_youzhan_ribaobiao_huizong(string workday_syzrbb = "", string workday_eyzrbb = "",string shortname="")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_syzrbb);
                DateTime ko_e = Convert.ToDateTime(workday_eyzrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                        -- 含有同比环比数据 去掉3个海上站点，并增加了合计
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
                    begin
                    select a.ShortName,a.UniversalCode
					,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
					from 
                    (SELECT  a.ShortName , a.UniversalCode,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d and a.shortname like '%{2}%'
                                AND a.DataType = '01' and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*'
                        GROUP BY a.ShortName , a.UniversalCode) a
	                    left  join
	                    (SELECT  aa.ShortName as qn_ShortName , aa.UniversalCode as qn_UniversalCode,
                                    SUM(CASE WHEN bb.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN bb.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN bb.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN bb.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN bb.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN bb.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN bb.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg aa inner join ERP_MaterialClass bb on aa.GasCode=bb.Materialcode
                        WHERE   aa.WorkDay >= @workday_sl  AND aa.WorkDay <=@workday_dl and aa.shortname like '%{2}%' 
                                AND aa.DataType = '01'  and aa.UniversalCode not in ('33350266','33350432','33350040')
                                AND aa.SalesMode = '01'
                                AND aa.Category <> '03'
                                and aa.BigAreaCode <> '*'
                        GROUP BY aa.ShortName , aa.UniversalCode) b  on a.UniversalCode=b.qn_UniversalCode
	                    
						union all
						
                    select '',a.quansheng
					,ROUND(cast(a.SalesVola as float),2),ROUND(cast(a.SalesVolb as float),2)
					,ROUND(cast(a.SalesVolc as float),2),ROUND(cast(a.SalesVold as float),2),ROUND(cast(a.rly as float),2)
					,ROUND(cast(a.CNG as float),2),ROUND(cast(a.LNG as float),2)
					,ROUND(cast(b.qn_SalesVola as float),2)
					,ROUND(cast(b.qn_SalesVolb as float),2),ROUND(cast(b.qn_SalesVolc as float),2)
					,ROUND(cast(b.qn_SalesVold as float),2),ROUND(cast(b.qn_rly as float),2)
					,ROUND(cast(b.qn_CNG as float),2),ROUND(cast(b.qn_LNG as float),2) 
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END
					
	from  (SELECT 1 lj,'合计'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d and a.shortname like '%{2}%'
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl and a.shortname like '%{2}%'
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj     
                            end;


                ", ko_s, ko_e, shortname);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "ls_youzhan_ribaobiao_huizong");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["ls_youzhan_ribaobiao_huizong"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 已经调整完毕
        /// 全省日报表汇总  不是单独界面，是在嵌入在fengongsi_ribaobiao_huizong.chtml 中
        /// workday_sqsrbb   workday_eqsrbb   除开了三个海上站 33350266  儋州白马井   33350432  三亚南边海   33350040  海口三木  
        /// </summary>
        /// <param name="workday_sfgsrbb"></param>
        /// <param name="workday_efgsrbb"></param>
        /// <returns></returns>
        [HttpGet]
        public string ls_quansheng_ribaobiao_huizong(string workday_sfgsrbb = "", string workday_efgsrbb = "")
        {
            string result = "";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime ko_e = Convert.ToDateTime(workday_efgsrbb);
                conn.Open();
                string sql_dzqb;
                sql_dzqb = string.Format(@"   
                    declare @workday_s datetime
                    declare @workday_d datetime
                    set @workday_s='{0}'
                    set @workday_d='{1}'
                    declare @workday_sl datetime
                    declare @workday_dl datetime
                    set @workday_sl= DATEADD(year,-1,@workday_s) 
                    set @workday_dl= DATEADD(year,-1,@workday_d) 
					-- cast((qy_10.MonSalesPlan) as Float)
                    begin
                    select a.quansheng
                    ,ROUND(cast(a.SalesVola as float),2) 'SalesVola',ROUND(cast(a.SalesVolb as float),2) 'SalesVolb'
					,ROUND(cast(a.SalesVolc as float),2) 'SalesVolc',ROUND(cast(a.SalesVold as float),2) 'SalesVold',ROUND(cast(a.rly as float),2) 'rly'
					,ROUND(cast(a.CNG as float),2) 'CNG',ROUND(cast(a.LNG as float),2) 'LNG'
					,ROUND(cast(b.qn_SalesVola as float),2) 'qn_SalesVola'
					,ROUND(cast(b.qn_SalesVolb as float),2) 'qn_SalesVolb',ROUND(cast(b.qn_SalesVolc as float),2) 'qn_SalesVolc'
					,ROUND(cast(b.qn_SalesVold as float),2) 'qn_SalesVold',ROUND(cast(b.qn_rly as float),2) 'qn_rly'
					,ROUND(cast(b.qn_CNG as float),2) 'qn_CNG',ROUND(cast(b.qn_LNG as float),2)  'qn_LNG'
					,CASE WHEN cast(a.SalesVola as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVola as float)-cast(b.qn_SalesVola as float))/cast(a.SalesVola as float),2)
						END 'tongbia'
					,CASE WHEN cast(a.SalesVolb as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolb as float)-cast(b.qn_SalesVolb as float))/cast(a.SalesVolb as float),2)
						END 'tongbib'
					,CASE WHEN cast(a.SalesVolc as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVolc as float)-cast(b.qn_SalesVolc as float))/cast(a.SalesVolc as float),2)
						END 'tongbic'
					,CASE WHEN cast(a.SalesVold as float) = 0 THEN 0
						ELSE ROUND((cast(a.SalesVold as float)-cast(b.qn_SalesVold as float))/cast(a.SalesVold as float),2)
						END 'tongbid'
					,CASE WHEN cast(a.rly as float) = 0 THEN 0
						ELSE ROUND((cast(a.rly as float)-cast(b.qn_rly as float))/cast(a.rly as float),2)
						END 'tongbirly'
					,CASE WHEN cast(a.CNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.CNG as float)-cast(b.qn_CNG as float))/cast(a.CNG as float),2)
						END 'tongbicng'
					,CASE WHEN cast(a.LNG as float) = 0 THEN 0
						ELSE ROUND((cast(a.LNG as float)-cast(b.qn_LNG as float))/cast(a.LNG as float),2)
						END 'tongbilng'
					
	from  (SELECT 1 lj,'全省'as quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_s  AND a.WorkDay <=@workday_d
                                AND a.DataType = '01'  and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') a
                       join        
                      (SELECT 1 lj, '全省去年'as qn_quansheng,
                                    SUM(CASE WHEN b.Extend1 = '92#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_SalesVola' ,
                                    SUM(CASE WHEN b.Extend1 = '95#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolb' ,
                                    SUM(CASE WHEN b.Extend1 = '98#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVolc' ,
                                    SUM(CASE WHEN b.Extend1 = '0#'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_SalesVold' ,
                                    SUM(CASE WHEN b.Extend1 = 'RLY'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as 'qn_rly' ,
                                    SUM(CASE WHEN b.Extend1 = 'CNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_CNG' ,
                                    SUM(CASE WHEN b.Extend1 = 'LNG'
                                                THEN SalesVol
                                                ELSE 0
                                        END) as  'qn_LNG'
                        FROM    OSMS_Daily_Sales_lg a inner join ERP_MaterialClass b on a.GasCode=b.Materialcode
                        WHERE   a.WorkDay >= @workday_sl  AND a.WorkDay <=@workday_dl
                                AND a.DataType = '01'   and a.UniversalCode not in ('33350266','33350432','33350040')
                                AND a.SalesMode = '01'
                                AND a.Category <> '03'
                                and a.BigAreaCode <> '*') b on a.lj = b.lj     
                            end;

                ", ko_s, ko_e);//  -- 加油卡比例附表区域
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "ls_quansheng_ribaobiao_huizong");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["ls_quansheng_ribaobiao_huizong"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        //待调试
        [HttpGet]
        public string ls_chaiyou_bishu(string workday_shxbscx = "", string workday_dhxbscx = "")
        {
            string connetStr = "server=************;user=sbxquery;password=*********;database=oilstation;sslMode=none;Allow User Variables=True"; //localhost不支持ssl连接时，最后一句一定要加！！！
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();

            DateTime ko_s = Convert.ToDateTime(workday_shxbscx);
            DateTime ko_e = Convert.ToDateTime(workday_dhxbscx);

            string result = "";
            try
            {
                SQLCon.Open();
                string searchStr;
                //searchStr = string.Format(@"select ID from sm_manoilvouch where sinopecnodeno='33350265' LIMIT 10");//  -- 加油卡比例附表区域
                //searchStr = string.Format(@"
                //    select count(*) id,sinopecnodeno from sm_manoilvouch a left join sm_oilcode b 
                //    on a.oilno=b.code 
                //    where a.liter>100 and a.takedate>='{0}' and a.takedate<'{1}'
                //    and b.name like '%柴%'
                //    group by sinopecnodeno
                //    ;");//  -- 加油卡比例附表区域
                searchStr = string.Format(@"
                select count(a.id) id,a.sinopecnodeno from sm_manoilvouch a 
                where a.liter>100 and a.takedate>='{0}' and a.takedate<'{1}'
                and 
                a.oilno in ('60000359','60000358','60033428','60173198','60189274','60189819','60205886','60514943','60000357','60000373')
                group by a.sinopecnodeno
                ;",ko_s,ko_e);//  -- 加油卡比例附表区域
                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "ls_chaiyou_bishu");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["ls_chaiyou_bishu"]);
            }
            catch (MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示");     //显示错误信息
            }
            finally
            {
                //conn.Close();
                SQLCon.Close();
            }
            return result;
        }

        /// <summary>
        /// APP连接--石化钱包-休眠用户
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string APP_shqb_xiumian( string end_time = "")
        {
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;"; //localhost不支持ssl连接时，最后一句一定要加！！！
            //stat.database.ip = rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com
            //stat.database.name = stat
            //stat.database.port = 3306
            //stat.database.username = sunbox_report
            //stat.database.password = ****************
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");
                //DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime stat_time = Convert.ToDateTime(end_time);
                DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
                DateTime stat_d_time = stat_time.AddMonths(-2); //. AddDays(-1);

                //string searchStr_1 = " select id from user_base where user_type = '1'  and create_time >= '{0}' and create_time<'{1}'";   //student表中数据
                string searchStr = string.Format(@"
                select kk.user_code from 
                    -- 一年内有交易
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{1}' and ope_time<='{2}' and card_no is null
                    ) kk
                    where 
                    -- 3个月内无交易
                    kk.user_code not in (
                    select user_code from 
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{2}' and card_no is null
                    ) hh group by hh.user_code
                    )
                    group by kk.user_code;
                   

                ", stat_x_time, stat_d_time, end_time);//  -- 邮储导入

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_xiumian");
                result = JsonConvert.SerializeObject(ds.Tables["APP_shqb_xiumian"]);
            }
            catch //(MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                //MessageBox.Show("关闭数据库");
            }

            return result;

        }

        /// <summary>
        /// 石化钱包低频用户查询
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string APP_shqb_dipin(string end_time = "")
        {

            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");
                //DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime stat_time = Convert.ToDateTime(end_time);
                //DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
                DateTime stat_d_time = stat_time.AddMonths(-1); //. AddDays(-1);

                //string searchStr_1 = " select id from user_base where user_type = '1'  and create_time >= '{0}' and create_time<'{1}'";   //student表中数据
                string searchStr = string.Format(@"
                
                select kk.user_code  
                from 
                -- 一年内有交易
                (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null
                ) kk
                group by kk.user_code
                having count(kk.user_code)>=1 and  count(kk.user_code)<3;
                   

                ", stat_d_time, end_time);//  -- 邮储导入  3和5 是单位时间内的次数

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_dipin");
                result = JsonConvert.SerializeObject(ds.Tables["APP_shqb_dipin"]);
            }
            catch //(MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                MessageBox.Show("关闭数据库");
            }

            return result;

        }

        /// <summary>
        /// 石化钱包摇摆用户导出
        /// </summary>
        /// <param name="end_time"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string APP_shqb_yaobai(string end_time = "")
        {
            //string connetStr = "server=**************;user=xuexin;password=****************;database=sunbox;sslMode=none;"; //localhost不支持ssl连接时，最后一句一定要加！！！
            //stat.database.ip = rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com
            //stat.database.name = stat
            //stat.database.port = 3306
            //stat.database.username = sunbox_report
            //stat.database.password = ****************
            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=api_order;sslMode=none;";//STAT库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");
                //DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime stat_time = Convert.ToDateTime(end_time);
                //DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
                DateTime stat_d_time = stat_time.AddMonths(-1); //. AddDays(-1);

                //string searchStr_1 = " select id from user_base where user_type = '1'  and create_time >= '{0}' and create_time<'{1}'";   //student表中数据
                string searchStr = string.Format(@"
                
                    select kk.user_code 
                    from 
                    -- 一年内有交易
                    (select user_code  from api_order.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_1 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_2 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_3 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_4 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_5 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_6 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_7 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_8 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_9 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_10 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_11 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_12 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_13 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_14 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_15 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_16 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_17 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_18 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_19 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_20 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_21 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_22 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_23 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_24 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_25 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_26 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_27 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_28 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_29 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_30 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_31 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_32 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_33 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_34 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_35 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_36 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_37 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_38 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_39 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_40 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_41 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_42 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_43 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_44 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_45 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_46 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_47 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_48 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_49 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_50 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_51 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_52 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_53 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_54 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_55 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_56 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_57 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_58 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_59 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_60 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_61 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_62 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null union all

                    select user_code  from api_order1.snpc_ord_refuel_63 where 1 = 1 and ope_time >='{0}' and ope_time<='{1}' and card_no is null
                    ) kk
                    group by kk.user_code
                    having count(kk.user_code)>=3 and  count(kk.user_code)<5;
                   

                ", stat_d_time, end_time);//  -- 邮储导入  3和5 是单位时间内的次数 

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_shqb_yaobai");
                result = JsonConvert.SerializeObject(ds.Tables["APP_shqb_yaobai"]);
            }
            catch //(MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                //MessageBox.Show("关闭数据库");
            }

            return result;

        }



        //加油卡系统--query01 ---数据源调整为云平台
        //休眠用户
        /// <summary>
        /// 加油卡休眠用户  4个月内有交易，近两个月没有交易
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        [HttpGet]
        public string lingshou_xiumian_chaxun(string workday_slsxm = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);

            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   
                        declare @workday_sx datetime
                        declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sx = Dateadd(MONTH, -2, '{0}')
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                            and b.cardno not in (select a.cardno from oildetail a where a.opetime>=@workday_sx and a.opetime <='{0}')
                            and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "lingshou_xiumian_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["lingshou_xiumian_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        //低频用户
        /// <summary>
        /// 低频客户 加油卡低频（不活跃）用户 3个月内交易一到2次   43777
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        [HttpGet]
        public string lingshou_dipin_chaxun(string workday_slsxm = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);

            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   

                        declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                             and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno
                        having count(b.cardno)>=1 and  count(b.cardno)<3;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "lingshou_dipin_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["lingshou_dipin_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 加油卡摇摆客户导出
        /// </summary>
        /// <param name="workday_slsxm"></param>
        /// <returns></returns>
        [HttpGet]
        public string lingshou_yaobai_chaxun(string workday_slsxm = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=ICCard;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_slsxm);

            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   

                        declare @workday_sd datetime
                        -- set @workday =  DateDiff(DAY, '2022-01-01', GETDATE())
                        set @workday_sd = Dateadd(MONTH, -4, '{0}')

 	                    select  b.cardno,count(b.cardno) as num,e.telphno2,e.telphno1,e.compno 
                        from oildetail b inner join CARDINFOR d on b.cardno=d.cardno 
                            inner join cardpsninfor e on d.compno=e.compno  
                        where 
                            b.opetime>=@workday_sd and b.opetime <='{0}'
                             and e.compno is not NULL
                        group by  b.cardno,e.telphno2,e.telphno1,e.compno
                        having count(b.cardno)>=3 and  count(b.cardno)<5;

                ", workday_slsxm);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "lingshou_yaobai_chaxun");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["lingshou_yaobai_chaxun"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }


        /// <summary>
        /// 员工提出-app
        /// </summary>
        /// <param name="bill_date_month"></param>
        /// <param name="emp_name"></param>
        /// <param name="emp_phone"></param>
        /// <param name="store_ou_name"></param>
        /// <param name="standard_code"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string APP_yg_ticheng(string bill_date_month = "", string emp_name ="", string emp_phone ="", string store_ou_name ="", string standard_code ="")
        {

            string connetStr = "server=rm-wz917k974y6324bd7go.mysql.rds.aliyuncs.com;user=sunbox_report;password=****************;database=stat;sslMode=none;";//STAT库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");

                MessageBox.Show(bill_date_month);//年月日
                //MessageBox.Show(stat_time.ToString());//正常的时间
                //MessageBox.Show(stat_d_time);
                string searchStr = string.Format(@"
                    select a.id,a.emp_name,a.emp_id,a.emp_phone,a.store_ou_code,a.store_ou_name,
                    a.standard_code,a.bill_date_month,a.emp_self_lifting_rewards_num,a.emp_delivery_rewards_num
                    from  shopping_noil_emp_distribution_month_sum  a
                    where a.emp_self_lifting_rewards_num IS NOT NULL
                    AND a.emp_delivery_rewards_num IS NOT null
                                        limit 10
                ");//  -- 邮储导入  3和5 是单位时间内的次数

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "APP_yg_ticheng");
                result = JsonConvert.SerializeObject(ds.Tables["APP_yg_ticheng"]);
            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                MessageBox.Show("关闭数据库");

            }

            return result;

        }

        /// <summary>
        /// 加油站加满率
        /// </summary>
        /// <param name="UniversalCode"></param>
        /// <param name="workday_jml"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string youzhan_jiamanlv(string UniversalCode="",string workday_jml="")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_jml);

            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   

                          -- 加满率
  select CAST((select count(*) from CheckDetail_2023 a inner join sys_OrgUnit b 
  on a.oucode=b.oucode where b.UniversalCode='{0}' and a.WorkDay='{1}'
  and Amount not in (100,200,300,400,500,600,700,800,900,1000,1100,1200,1300,1400,1500)) as float)/CAST((select count(*) from CheckDetail_2023 a inner join sys_OrgUnit b 
  on a.oucode=b.oucode where b.UniversalCode='{0}' and a.WorkDay='{1}') as float) as jml

                ", UniversalCode, workday_jml);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "youzhan_jiamanlv");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["youzhan_jiamanlv"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 加满率
        /// </summary>
        /// <param name="UniversalCode"></param>
        /// <param name="workday_jml"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string youzhan_jiamanlv_baobiao(string UniversalCode = "", string workday_jml = "", string workday_d_jml = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_jml);
            string ks = ko_s.ToString("yyyy-MM-dd");    //workday1是datatime
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   

                    -- 加满率
                    with 
                    hk as (select count(id) id,b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end) as fengongsi, left(b.ShortName,2) as shixian
	                    from CheckDetail_{1} a inner join sys_OrgUnit b 
                        on a.oucode=b.oucode where b.UniversalCode like '%{0}%' and a.WorkDay>='{2}' and a.WorkDay<='{3}' 
                        and a.Amount not in ('100','200','300','400','500','600','700','800','900','1000','1100','1200','1300','1400','1500')
	                    and a.Amount>=100 
                        group by b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end),left(b.ShortName,2)),
                    hk_1 as (select count(id) id,b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end) as fengongsi,left(b.ShortName,2) as shixian,b.OUCode 
	                    from CheckDetail_{1} a inner join sys_OrgUnit b 
                        on a.oucode=b.oucode where b.UniversalCode like '%{0}%' and a.WorkDay='{2}' and a.WorkDay<='{3}'  
                        group by b.UniversalCode,b.ShortName,(case when left(b.oucode,6) ='100047'then '海口'
				                            when left(b.oucode,6) ='100048'then  '三亚'
				                            when left(b.oucode,6) ='100049'then '儋州'
				                            when left(b.oucode,6) ='100050'then '琼海'
                                            when left(b.oucode,6) ='100051'then '万宁'
                                            when left(b.oucode,6) ='100052'then '澄迈'
					                    else 'zhong' end),left(b.ShortName,2),b.OUCode)
                    select hk.fengongsi,hk.shixian,hk.ShortName,hk.UniversalCode,hk.id,round(cast(hk.id as float)/cast(hk_1.id as float)*100,2) as jml 
                    from hk inner join hk_1 on hk.UniversalCode=hk_1.UniversalCode


                ", UniversalCode,  ko_s.ToString("yyyy"),workday_jml, workday_d_jml);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "youzhan_jiamanlv_baobiao");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["youzhan_jiamanlv_baobiao"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 回头率 TIDB数据库
        /// </summary>
        /// <param name="UniversalCode"></param>
        /// <param name="workday_jml"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string youzhan_huitoulv_baobiao(string end_time = "")
        {

            string connetStr = "server=10.193.63.125:4000;user=root;password=******************;database=dmpdata;sslMode=none;";//sunbox库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");
                //DateTime ko_s = Convert.ToDateTime(workday_sfgsrbb);
                DateTime stat_time = Convert.ToDateTime(end_time);
                //DateTime stat_x_time = stat_time.AddMonths(-1); //. AddDays(-1);
                DateTime stat_d_time = stat_time.AddMonths(-1); //. AddDays(-1);

                //string searchStr_1 = " select id from user_base where user_type = '1'  and create_time >= '{0}' and create_time<'{1}'";   //student表中数据
                string searchStr = string.Format(@"
                
                        -- ******核心*****
                        -- 加油卡的汽油回头率 少了组织机构
                        select hk_4.area_name,hk_4.city_name,hk_4.station_name,
                                hk.nodeno,hk.type_name,hk.hk_num,hk_1.hk_1_num,hk_2.qy_shqb_bs,
		                        round((CAST(hk_1.hk_1_num as double)+cast(hk_2.qy_shqb_bs as double))/cast(hk.hk_num as double),2)
		                         
                         from 
                        (
	                        select a.nodeno,b.type_name,count(a.ID) hk_num
	                        from dmpdata.jyk_his_oilvouch_bycash a 
	                        inner join dmpdata.zt_oil_code b on a.oilno=b.material_code
	                        where  opetime>='2023-12-13' and  opetime<='2023-12-14'
	                        and b.type_name='汽油'
	                        group by b.type_name,a.nodeno	

                        ) hk inner join (
	                        select a.nodeno,b.type_name,count(a.ID) hk_1_num
	                        from dmpdata.jyk_oildetail a 
	                        inner join dmpdata.zt_oil_code b on a.oilno=b.material_code
	                        where  opetime>='2023-12-13' and  opetime<='2023-12-14'
	                        and b.type_name='汽油'
	                        group by b.type_name,a.nodeno
                        ) hk_1 on  hk.nodeno=hk_1.nodeno 
                        inner join jyk_nodeinfor hk_3 
	                        on hk.nodeno=hk_3.nodeno
                        inner join (
	                        select a.station_code,b.type_name,count(a.mobile_no) qy_shqb_bs from  dmpdata.app_e_wallet_sale_order a 
	                        inner join dmpdata.zt_oil_code b on a.oil_code=b.material_code
	                        where  b.type_name='汽油'  and 
	                        a.com_up_time>='2023-12-13' and a.com_up_time<='2023-12-14'
	                        group by a.station_code,b.type_name
                        ) hk_2 on hk_2.station_code=hk_3.sinopec_nodeno 
                        inner join app_base_sys_station hk_4
	                        on hk_4.standard_code= hk_3.sinopec_nodeno 

                ", stat_d_time, end_time);//  -- 邮储导入  3和5 是单位时间内的次数 

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "youzhan_huitoulv_baobiao");
                result = JsonConvert.SerializeObject(ds.Tables["youzhan_huitoulv_baobiao"]);
            }
            catch //(MySqlException ex)
            {
                //MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                //MessageBox.Show("关闭数据库");
            }

            return result;
        }

        
        
        /// <summary>
        /// 优惠平衡点分公司-查询
        /// </summary>
        /// <param name="youpin_code"></param>
        /// <returns></returns>
        [HttpGet]
        public string qy_fgs_youhuipinghengdian_select(string youpin_name="")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";

            DateTime currentTime = DateTime.Now;
            string WorkDay1 = currentTime.ToString("yyyy-MM-dd");//当前日期

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"
                SELECT id ,youpin_code ,youpin_name,ROUND(qty,2) qty,ROUND(qty_kg,2)  qty_kg
                      ,ROUND(guaipai_price,2) guaipai_price ,ROUND(xs_price,2) xs_price,ROUND(xs_amount,2) xs_amount ,ROUND(guiding_maoli,2) guiding_maoli,ROUND(maoli,2) maoli
                      ,ROUND(yh_fudu,2) yh_fudu,ROUND(yh_fudu_kg,2) yh_fudu_kg,ROUND(guiding_midu,2) guiding_midu,ROUND(xs_midu,2) xs_midu,ROUND(yh_maoli,2) yh_maoli 
                      ,ROUND(yh_maoli_kg,2) yh_maoli_kg,ROUND(yh_qty_kg,2) yh_qty_kg,ROUND(yh_qty,2) yh_qty,ROUND(yh_xs_amount,2) yh_xs_amount
                      ,ROUND(zengfu_bili*100,2) zengfu_bili,workday,remark,remark_1

                  FROM [HA_BankDataCompare].[dbo].[qy_yh_pinghengdian]
				where  WorkDay='{0}' and youpin_name like '%{1}%'
                ", WorkDay1, youpin_name);
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "qy_fgs_youhuipinghengdian_select");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["qy_fgs_youhuipinghengdian_select"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="youpin_name"></param>
        /// <returns></returns>
        [HttpGet]
        public string qy_jyz_youhuipinghengdian_select(string youpin_name = "")
        {
            string result = "";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";

            DateTime currentTime = DateTime.Now;
            string WorkDay1 = currentTime.ToString("yyyy-MM-dd");//当前日期

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"
                SELECT id ,youpin_code ,youpin_name,ROUND(qty,2) qty,ROUND(qty_kg,2)  qty_kg
                      ,ROUND(guaipai_price,2) guaipai_price ,ROUND(xs_price,2) xs_price,ROUND(xs_amount,2) xs_amount 
                      ,ROUND(yh_fudu,2) yh_fudu,ROUND(yh_fudu_kg,2) yh_fudu_kg,ROUND(guiding_midu,2) guiding_midu,ROUND(xs_midu,2) xs_midu
                      ,ROUND(yh_qty_kg,2) yh_qty_kg,ROUND(yh_qty,2) yh_qty,ROUND(yh_xs_amount,2) yh_xs_amount
                      ,ROUND(zengfu_bili*100,2) zengfu_bili,workday,remark,remark_1

                  FROM [HA_BankDataCompare].[dbo].[qy_yh_pinghengdian]
				where  WorkDay='{0}' and youpin_name like '%{1}%'
                ", WorkDay1, youpin_name);
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "qy_jyz_youhuipinghengdian_select");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["qy_jyz_youhuipinghengdian_select"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 促销单查询--于天骄
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_cuxiaodan_TJ(string workday_stgdcx = "", string billno = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_tg_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);
                
                DataTable dt_xc_xs = hx_hk.HX_cuxiaodan_TJ(billno, ko_s);   //接口返回数据_实时流水
                dt_tg_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_tg_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }
        
        /// <summary>
        /// 促销单查询，时间随机
        /// </summary>
        /// <param name="workday_stgdcx"></param>
        /// <param name="billno"></param>
        /// <returns></returns>
        [HttpGet]
        public string HX_cuxiaodan_time_TJ(string workday_stgdcx = "", string workday_edgdcx = "", string billno = "")
        {
            string result = "";
            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_tg_xs_kk = new DataSet();
            try
            {
                //conn.Open();
                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下

                DateTime ko_s = Convert.ToDateTime(workday_stgdcx);
                DateTime ko_d = Convert.ToDateTime(workday_edgdcx);
                DataTable dt_xc_xs = hx_hk.HX_cuxiaodan_yue_TJ(billno, ko_s, ko_d);   //接口返回数据_实时流水
                dt_tg_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_jk_xs
                result = JsonConvert.SerializeObject(dt_tg_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错

            }
            catch
            {
            }
            finally
            {
                //conn.Close();
            }
            return result;

        }

        /// <summary>
        /// 交控轻油销售--生产
        /// </summary>
        /// <param name="workday_jml"></param>
        /// <param name="workday_d_jml"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string jiaokong_qyxs_baobiao( string workday_jml = "", string workday_d_jml = "")
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=10.193.63.22\\sql02;Initial Catalog=Cloud;uid=sa;pwd=Hacloud2015:)";

            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            DateTime ko_s = Convert.ToDateTime(workday_jml);
            //string ks = ko_s.ToString("yyyy-MM-dd");    //workday1是datatime
            try
            {
                conn.Open();
                string sql_dzqb;

                sql_dzqb = string.Format(@"   


                    -- 交控轻油报表导出  CAST(qy_11.xiaoshoushuliang_kg AS FLOAT)
                    select '海南省交控石化有限公司' as xszz,'成品油销售' as ddlx,'' as wybsh,
                    hk.WorkDay,hk.kh,'' as ywy ,hk.bm,hk.kpkh,'' as hangh,hk.jsfs,
                    hk.fhck,hk.fhwl,hk.xssl,   -- 体积  M5
                    round(cast(hk.m_kg as float)/cast(hk.xssl as float),2)*1000 as MD,  -- 密度
                    hk.xsje,cast(hk.m_kg as float)*1000 as xskg,'13' as shuilv,    -- 销售数量、税率
                    round(cast(hk.xssl as float)*cast(hk.fjfdj as float),2) as qyfjtxf,   -- 汽油附加通行费
                    hk.fjfdj,
                    round(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)),2) as yysrhs,   -- '营业收入（含税）'
                    round(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float))-(cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/1.13,2) as shuie,
                    round((cast(hk.xsje as float) - (cast(hk.xssl as float) * cast(hk.fjfdj as float)))/1.13,2) as bhsje,   --  '不含税金额'
                    round(((cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/cast(hk.xssl as float)/1.13),2) as wsdj,
                    round((cast(hk.xsje as float)-(cast(hk.xssl as float)*cast(hk.fjfdj as float)))/cast(hk.xssl as float),2) as hsdj,
                    '' as dpzk,'' as zke  
                    from (
	                    select a.WorkDay ,
			                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end as kh,
			                    a.ShortName as bm,
			                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end  as kpkh,
			                    b.cloudCheckName as jsfs,
			                    a.ShortName as fhck,a.GasCode as fhwl,
			                    a.SalesVol as m_kg,a.amount as xsje,a.qty as xssl,
			                    case when Category = '02' then 0  -- 柴油
				                     when Category = '01' then 1.05  -- 汽油  'L*附加费单价',
				                     end as fjfdj
	                    from  [dbo].[OSMS_Daily_Sales_BW] a inner join OSMS_PAYTYPE b on a.CheckMode=b.cloudCheckMode
	                    where a.WorkDay>='{0}' and a.WorkDay>='{1}' 
                            and a.BigAreaName='交控' and a.SalesVol>0
	                    group by a.WorkDay,b.cloudCheckName,a.ShortName,a.GasCode,a.SalesVol,a.amount,a.qty,
	                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end,
	                    case when a.CheckMode = '012001' then a.ShortName  --现金
				                     when a.CheckMode = '012006' then a.ShortName  -- 银行卡
				                     when a.CheckMode = '012007' then 'IC卡消费' 
				                     end,
	                    case when Category = '02' then 0  -- 柴油
				                     when Category = '01' then 1.05  -- 汽油  'L*附加费单价',
				                     end) hk ;



                ",  workday_jml, workday_d_jml);//  -- 加油站加油卡占比查询
                SqlDataAdapter sda = new SqlDataAdapter(sql_dzqb, conn); //为存储过程名
                sda.Fill(ds, "jiaokong_qyxs_baobiao");//邮储到账表名，ycdz
                result = JsonConvert.SerializeObject(ds.Tables["jiaokong_qyxs_baobiao"]);
            }
            catch
            {
            }
            finally
            {
                conn.Close();

            }
            return result;
        }

        /// <summary>
        /// 液化气网络订单送营销，这里只有网络订单   --------这里的字段 需要去掉 _   这个，好像不识别，导出是正常的  
        /// </summary>
        /// <param name="start_time"></param>
        /// <param name="end_time"></param>
        /// <returns></returns>
        [HttpGet]  //和调用方法有关，pos方式的话，则是[post],另外页面js调用和这里需要同步，post方法
        public string yhq_lpg_jydd(string start_time= "",string end_time = "")
        {
            //以下为测试环境
            //string connetStr = "server=10.193.63.145;port=5581;user=root;password=****************;database=lpg;sslMode=none;";//lpg库
            string connetStr = "server=************;port=3306;user=admin;password=**********;database=lpg;sslMode=none;";//lpg库

            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                MessageBox.Show("打开数据库");

                //string searchStr_1 = " select id from user_base where user_type = '1'  and create_time >= '{0}' and create_time<'{1}'";   //student表中数据
                string searchStr = string.Format(@"
                     select id_,AREA_,dept_,no_,time_,cust_type_,cust_id_,AMOUNT_,PAY_TYPE_,PAY_NO_,TRADE_NO_,FLAG_ ,BUY_NAME_,PHONE_  
                     from lpg.lpg_bill where TIME_ >='{0}'  and TIME_ <='{1}'


                ", start_time, end_time);//  -- 邮储导入

                MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                adapter.Fill(ds, "lpg_jydd");
                result = JsonConvert.SerializeObject(ds.Tables["lpg_jydd"]);
            }
            catch (MySqlException ex)
            {
                MessageBox.Show(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                MessageBox.Show("关闭数据库");
            }

            return result;

        }


    }





    /// <summary>
    /// 团购单同步类和同步方法
    /// </summary>
    public class Huhk_tongbu
    {
        /// <summary>
        /// 团购订单明细海信同步到云平台
        /// </summary>
        /// <param name="billno"></param>
        public void tuangoudingdan_mingxi_tongbu(string billno)
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();

            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {

                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                //string billno = "1PFXS202204190224";
                DataTable dt_xc_xs = hx_hk.APP_ziti_xiaoshoumingxi_chaxun(billno);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_xc_xs

                conn.Open();//连接数据库 
                SqlCommand ni;//初始化数据库，并储存sql代码

                //查询数据列表处理
                List<HX_Tuangoudan_mingxi> huhkk = new List<HX_Tuangoudan_mingxi>();     //列表
                HX_Tuangoudan_mingxi huhukk = new HX_Tuangoudan_mingxi();              //数据结构
                foreach (DataRow row in dt_xc_xs.Rows)    //遍历表的每一行row   循环遍历
                {
                    huhukk.BILLNO = (Convert.ToString(row["BILLNO"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.SERIALNO = (Convert.ToString(row["SERIALNO"]));
                    huhukk.PLUID = (Convert.ToString(row["PLUID"]));
                    huhukk.PLUCODE = (Convert.ToString(row["PLUCODE"]));
                    huhukk.PLUNAME = (Convert.ToString(row["PLUNAME"]));
                    huhukk.BARCODE = (Convert.ToString(row["BARCODE"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.SPEC = (Convert.ToString(row["SPEC"]));
                    huhukk.UNIT = (Convert.ToString(row["UNIT"]));
                    huhukk.PACKUNIT = (Convert.ToString(row["PACKUNIT"]));
                    huhukk.PACKQTY = (Convert.ToString(row["PACKQTY"]));
                    huhukk.PFCOUNT = (Convert.ToString(row["PFCOUNT"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.HJPRICE = (Convert.ToString(row["HJPRICE"]));
                    huhukk.PRICE = (Convert.ToString(row["PRICE"]));
                    huhukk.PFPRICE = (Convert.ToString(row["PFPRICE"]));
                    huhukk.TGJ = (Convert.ToString(row["TGJ"]));
                    huhukk.SGJC = (Convert.ToString(row["SGJC"]));
                    huhukk.YSTOTAL = (Convert.ToString(row["YSTOTAL"]));
                    huhukk.SSTOTAL = (Convert.ToString(row["SSTOTAL"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.XTAXRATE = (Convert.ToString(row["XTAXRATE"]));
                    huhukk.XTAXTOTAL = (Convert.ToString(row["XTAXTOTAL"]));
                    huhukk.HJTOTAL = (Convert.ToString(row["HJTOTAL"]));
                    huhukk.HMLTOTAL = (Convert.ToString(row["HMLTOTAL"]));
                    huhukk.HMLRATE = (Convert.ToString(row["HMLRATE"]));
                    huhukk.HCOST = (Convert.ToString(row["HCOST"]));

                    huhkk.Add(huhukk);               //一起把huhukk数据结构添加到huhkk列表

                    string sql = "";
                    sql = string.Format(@" 
                    declare @billno varchar(50)
                    declare @SERIALNO varchar(50)
                    set @SERIALNO='{1}'
                    set @billno='{0}'
                    begin
                        if  exists(select 1 from HX_tuangoudan where BILLNO = @billno and SERIALNO=@SERIALNO)
                        begin
                            begin
                                delete from HX_tuangoudan where  BILLNO = @billno and SERIALNO=@SERIALNO
                            end
                            begin
                                insert into HX_tuangoudan(BILLNO,SERIALNO,PLUID,PLUCODE,PLUNAME,BARCODE,SPEC,UNIT,PACKUNIT,PACKQTY,PFCOUNT,HJPRICE,PRICE,PFPRICE,TGJ,SGJC,YSTOTAL,SSTOTAL,XTAXRATE,XTAXTOTAL,HJTOTAL,HMLTOTAL,HMLRATE,HCOST) 
                                values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}','{15}','{16}','{17}','{18}','{19}','{20}','{21}','{22}','{23}')
                            end
                        end
                        else
                            begin
                                insert into HX_tuangoudan(BILLNO,SERIALNO,PLUID,PLUCODE,PLUNAME,BARCODE,SPEC,UNIT,PACKUNIT,PACKQTY,PFCOUNT,HJPRICE,PRICE,PFPRICE,TGJ,SGJC,YSTOTAL,SSTOTAL,XTAXRATE,XTAXTOTAL,HJTOTAL,HMLTOTAL,HMLRATE,HCOST) 
                                values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}','{15}','{16}','{17}','{18}','{19}','{20}','{21}','{22}','{23}')
                            end
                    end",
                        huhukk.BILLNO, huhukk.SERIALNO, huhukk.PLUID, huhukk.PLUCODE, huhukk.PLUNAME, huhukk.BARCODE, huhukk.SPEC, huhukk.UNIT, huhukk.PACKUNIT, huhukk.PACKQTY, huhukk.PFCOUNT, huhukk.HJPRICE, huhukk.PRICE,
                        huhukk.PFPRICE, huhukk.TGJ, huhukk.SGJC, huhukk.YSTOTAL, huhukk.SSTOTAL, huhukk.XTAXRATE, huhukk.XTAXTOTAL, huhukk.HJTOTAL, huhukk.HMLTOTAL, huhukk.HMLRATE, huhukk.HCOST);
                    int n = 0;
                    ni = new SqlCommand(sql, conn);     //插入语句执行
                    n = ni.ExecuteNonQuery();
                    ni.Dispose();
                    if (n != 0) { Console.Write("海信团购单mingxi同步成功！"); } else { Console.Write("海信团购单mingxi同步失败！失败！"); }
                }


                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                //System.Console.WriteLine("1234545678345672345678");
                //System.Console.WriteLine(result);

                conn.Close();
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
                Console.Write(ex.Message);
            }
            finally
            {
                //conn.Close();
                conn.Close();
                Console.Write("Cloud 关闭数据库成功！");
            }
        }

        /// <summary>
        /// 团购订单信息海信同步到云平台
        /// </summary>
        /// <param name="billno"></param>
        public void tuangoudingdan_tongbu(string billno) 
        {
            string result = "";
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();

            //1 根据查询条件查询数据库数据,存入ds
            DataSet dt_xc_xs_kk = new DataSet();
            try
            {

                WebReference.hx_xiaoshou_jiekou hx_hk = new WebReference.hx_xiaoshou_jiekou();  //new  接口一下
                //string billno = "1PFXS202204190224";
                DataTable dt_xc_xs = hx_hk.APP_ziti_xiaoshoudan_chaxun(billno);   //接口返回数据_实时流水
                dt_xc_xs_kk.Tables.Add(dt_xc_xs);        //数据集中增加表dt_xc_xs

                conn.Open();//连接数据库 
                SqlCommand ni;//初始化数据库，并储存sql代码

                //查询数据列表处理
                List<HX_Tuangoudan> huhkk_list = new List<HX_Tuangoudan>();     //列表
                HX_Tuangoudan huhukk = new HX_Tuangoudan();              //数据结构
                foreach (DataRow row in dt_xc_xs.Rows)    //遍历表的每一行row   循环遍历
                {
                    huhukk.BILLNO = (Convert.ToString(row["BILLNO"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.LRDATE = (Convert.ToString(row["LRDATE"]));
                    huhukk.USERCODE = (Convert.ToString(row["USERCODE"]));
                    huhukk.USERNAME = (Convert.ToString(row["USERNAME"]));
                    huhukk.TJDATE = (Convert.ToString(row["TJDATE"]));
                    huhukk.JZDATE = (Convert.ToString(row["JZDATE"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.JZRCODE = (Convert.ToString(row["JZRCODE"]));
                    huhukk.JZRNAME = (Convert.ToString(row["JZRNAME"]));
                    huhukk.RPTDATE = (Convert.ToString(row["RPTDATE"]));
                    huhukk.ORGCODE = (Convert.ToString(row["ORGCODE"]));
                    huhukk.ORGNAME = (Convert.ToString(row["ORGNAME"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.ETPCODE = (Convert.ToString(row["ETPCODE"]));
                    huhukk.ETPNAME = (Convert.ToString(row["ETPNAME"]));
                    huhukk.YWYID = (Convert.ToString(row["YWYID"]));
                    huhukk.YWYCODE = (Convert.ToString(row["YWYCODE"]));
                    huhukk.YWYNAME = (Convert.ToString(row["YWYNAME"]));
                    huhukk.YXDATE = (Convert.ToString(row["YXDATE"]));
                    huhukk.ADDRESS = (Convert.ToString(row["ADDRESS"]));   //把行ORGCODE 字段的值存在set中
                    huhukk.DHDATE = (Convert.ToString(row["DHDATE"]));
                    huhukk.PFCOUNT = (Convert.ToString(row["PFCOUNT"]));
                    huhukk.PFTOTAL = (Convert.ToString(row["PFTOTAL"]));
                    huhukk.SSTOTAL = (Convert.ToString(row["SSTOTAL"]));
                    huhukk.XTAXTOTAL = (Convert.ToString(row["XTAXTOTAL"]));
                    huhukk.QKTOTAL = (Convert.ToString(row["QKTOTAL"]));
                    huhukk.REMARK = (Convert.ToString(row["REMARK"]));
                    huhukk.INORGCODE = (Convert.ToString(row["INORGCODE"]));
                    huhukk.TJRID = (Convert.ToString(row["TJRID"]));
                    huhukk.TJRCODE = (Convert.ToString(row["TJRCODE"]));
                    huhukk.TJRNAME = (Convert.ToString(row["TJRNAME"]));
                    huhukk.AUDITUSERID1 = (Convert.ToString(row["AUDITUSERID1"]));
                    huhukk.AUDITUSERCODE1 = (Convert.ToString(row["AUDITUSERCODE1"]));
                    huhukk.AUDITUSERNAME1 = (Convert.ToString(row["AUDITUSERNAME1"]));
                    huhukk.AUDITDATE1 = (Convert.ToString(row["AUDITDATE1"]));
                    huhukk.ZFDATE = (Convert.ToString(row["ZFDATE"]));
                    huhukk.ZFRID = (Convert.ToString(row["ZFRID"]));
                    huhukk.ZFRNAME = (Convert.ToString(row["ZFRNAME"]));
                    huhukk.HJTOTAL = (Convert.ToString(row["HJTOTAL"]));
                    huhukk.HMLTOTAL = (Convert.ToString(row["HMLTOTAL"]));
                    huhukk.JZDATEPOS = (Convert.ToString(row["JZDATEPOS"]));
                    huhukk.HCOST = (Convert.ToString(row["HCOST"]));
                    huhukk.PROFIT = (Convert.ToString(row["PROFIT"]));
                    huhukk.ZPCOMNAME = (Convert.ToString(row["ZPCOMNAME"]));
                    huhukk.ZPCOMVATNO = (Convert.ToString(row["ZPCOMVATNO"]));

                    huhkk_list.Add(huhukk);               //一起把huhukk数据结构添加到huhkk列表

                    string sql = "";
                    sql = string.Format(@" 
                    declare @billno varchar(150)
                    declare @RPTDATE varchar(150)
                    set @RPTDATE='{8}'
                    set @billno='{0}'
                    begin
                        if  exists(select 1 from HX_tuangoudan_head where BILLNO = @billno and RPTDATE=@RPTDATE)
                        begin
                            begin
                                delete from HX_tuangoudan_head where  BILLNO = @billno and RPTDATE=@RPTDATE
                            end
                            begin
                                insert into HX_tuangoudan_head(BILLNO,LRDATE,USERCODE ,USERNAME,TJDATE,JZDATE
                                  ,JZRCODE,JZRNAME,RPTDATE,ORGCODE ,ORGNAME ,ETPCODE ,ETPNAME,YWYID ,YWYCODE ,YWYNAME ,YXDATE ,ADDRESS
                                  ,DHDATE ,PFCOUNT,PFTOTAL ,SSTOTAL ,XTAXTOTAL  ,QKTOTAL ,REMARK ,INORGCODE ,TJRID  ,TJRCODE ,TJRNAME
                                  ,AUDITUSERID1 ,AUDITUSERCODE1,AUDITUSERNAME1,AUDITDATE1 ,ZFDATE ,ZFRID  ,ZFRNAME ,HJTOTAL ,HMLTOTAL
                                  ,JZDATEPOS ,HCOST ,PROFIT ,ZPCOMNAME  ,ZPCOMVATNO) 
                                values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}'
                                    ,'{15}','{16}','{17}','{18}','{19}','{20}','{21}','{22}','{23}','{24}','{25}','{26}','{27}','{28}'
                                    ,'{29}','{30}','{31}','{32}','{33}','{34}','{35}','{36}','{37}','{38}','{39}','{40}','{41}','{42}')
                            end
                        end
                        else
                            begin
                                insert into HX_tuangoudan_head(BILLNO,LRDATE,USERCODE ,USERNAME,TJDATE,JZDATE
                                  ,JZRCODE,JZRNAME,RPTDATE,ORGCODE ,ORGNAME ,ETPCODE ,ETPNAME,YWYID ,YWYCODE ,YWYNAME ,YXDATE ,ADDRESS
                                  ,DHDATE ,PFCOUNT,PFTOTAL ,SSTOTAL ,XTAXTOTAL  ,QKTOTAL ,REMARK ,INORGCODE ,TJRID  ,TJRCODE ,TJRNAME
                                  ,AUDITUSERID1 ,AUDITUSERCODE1,AUDITUSERNAME1,AUDITDATE1 ,ZFDATE ,ZFRID  ,ZFRNAME ,HJTOTAL ,HMLTOTAL
                                  ,JZDATEPOS ,HCOST ,PROFIT ,ZPCOMNAME  ,ZPCOMVATNO) 
                                values('{0}','{1}','{2}','{3}','{4}','{5}','{6}','{7}','{8}','{9}','{10}','{11}','{12}','{13}','{14}'
                                    ,'{15}','{16}','{17}','{18}','{19}','{20}','{21}','{22}','{23}','{24}','{25}','{26}','{27}','{28}'
                                    ,'{29}','{30}','{31}','{32}','{33}','{34}','{35}','{36}','{37}','{38}','{39}','{40}','{41}','{42}')
                            end
                    end",
                        huhukk.BILLNO, huhukk.LRDATE, huhukk.USERCODE, huhukk.USERNAME, huhukk.TJDATE, huhukk.JZDATE, huhukk.JZRCODE
                        , huhukk.JZRNAME, huhukk.RPTDATE, huhukk.ORGCODE, huhukk.ORGNAME, huhukk.ETPCODE, huhukk.ETPNAME,huhukk.YWYID
                        , huhukk.YWYCODE, huhukk.YWYNAME, huhukk.YXDATE, huhukk.ADDRESS, huhukk.DHDATE, huhukk.PFCOUNT, huhukk.PFTOTAL
                        , huhukk.SSTOTAL, huhukk.XTAXTOTAL, huhukk.QKTOTAL, huhukk.REMARK, huhukk.INORGCODE, huhukk.TJRID, huhukk.TJRCODE
                        , huhukk.TJRNAME, huhukk.AUDITUSERID1, huhukk.AUDITUSERCODE1, huhukk.AUDITUSERNAME1, huhukk.AUDITDATE1, huhukk.ZFDATE
                        , huhukk.ZFRID, huhukk.ZFRNAME, huhukk.HJTOTAL, huhukk.HMLTOTAL, huhukk.JZDATEPOS, huhukk.HCOST, huhukk.PROFIT, 
                        huhukk.ZPCOMNAME, huhukk.ZPCOMVATNO
                        );
                    int n = 0;
                    ni = new SqlCommand(sql, conn);     //插入语句执行
                    n = ni.ExecuteNonQuery();
                    ni.Dispose();
                    if (n != 0) { Console.Write("海信团购单同步成功！"); } else { Console.Write("海信团购单同步失败！失败！"); }
                }


                result = JsonConvert.SerializeObject(dt_xc_xs_kk);
                //前端chtml调用WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                //System.Console.WriteLine("1234545678345672345678");
                //System.Console.WriteLine(result);

                conn.Close();
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
                Console.Write(ex.Message);
            }
            finally
            {
                //conn.Close();
                conn.Close();
                Console.Write("Cloud 关闭数据库成功！");
            }
        }

        /// <summary>
        /// Cloud库团购订单入库tuangoudingdan_ler表，目的是固定团购单的价格
        /// </summary>
        /// <param name="billno"></param>
        public void tuangoudingdan_ler_tongbu(string billno) 
        {
            //string shujuku = "Data Source=************\\sql01;Initial Catalog=GDStationData;uid=sa;pwd=Hacloud2015:)";
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            SqlConnection conn = new SqlConnection(shujuku);

            //1 根据查询条件查询数据库数据,存入ds
            try
            {
                conn.Open();//连接数据库 
                Console.Write("Cloud 打开数据库成功！");
                SqlCommand ni;//初始化数据库，并储存sql代码
                string sql = "";
                sql = string.Format(@" 

                    -- 这里需要同步到新表
                    declare @billno varchar(100)
                    set @billno='{0}'   -- 1PFXS202207200066
                    begin
	                    if not exists(select 1 from HX_tuangoudan_ler where billno=@billno)
	                    begin
		                    insert into HX_tuangoudan_ler(id,billno,SERIALNO,PLUID,PLUCODE,LEIBIE_YAN 
		                        ,PLUNAME,BARCODE,xiaolei_name,zhonglei_name,dalei_name
		                        ,SPEC ,UNIT,PACKUNIT ,PACKQTY ,PFCOUNT ,HJPRICE
		                        ,HETONG_price ,PRICE ,PFPRICE,guiding_TG_price,shiji_price 
		                        , TGJC,u,v,YSTOTAL,SSTOTAL,w,x,y,z,aa,ab,ac,XTAXRATE,XTAXTOTAL,HJTOTAL,HMLTOTAL,HMLRATE,HCOST)
		                    SELECT  A.ID,
		                    A.BILLNO,A.SERIALNO,A.PLUID,A.PLUCODE,B.LEIBIE_YAN 
		                        ,A.PLUNAME,A.BARCODE,B.xiaolei_name,B.zhonglei_name,B.dalei_name
		                        ,A.SPEC ,A.UNIT,A.PACKUNIT ,A.PACKQTY ,A.PFCOUNT ,A.HJPRICE
		                        ,B.HETONG_price ,A.PRICE ,A.PFPRICE,B.guiding_TG_price ,B.shiji_price   
		                        ,ROUND(convert(float,A.PFPRICE)-convert(float,B.guiding_TG_price),2) TGJC   -- 申请价-规定价>0
		                        ,ROUND((convert(float,A.PFPRICE)-convert(float,A.HJPRICE))/(1+convert(float,A.XTAXRATE)/100),2)  U      -- 库存成本团购单位毛利（不含税）  PFPRICE-HJPRICE
		                        ,ROUND((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100),2)  V   -- 最新成本团购单位毛利（不含税）  PFPRICE-HETONG_price
		                        ,A.YSTOTAL      ,A.SSTOTAL
		                        ,ROUND((convert(float,A.PFCOUNT)*convert(float,B.guiding_TG_price))/(1+convert(float,A.XTAXRATE)/100),2)   W
		                        ,ROUND(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100),2)  X
		                        ,ROUND((convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100) -(convert(float,A.PFCOUNT)*convert(float,B.shiji_price)/(1+convert(float,A.XTAXRATE)/100))),2)  Y  -- x-w
		                         ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,A.HJPRICE))/(1+convert(float,A.XTAXRATE)/100)),2)  Z   -- o*u
		                        ,ROUND((convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100))),2)  AA   --o*v
		                         ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,A.HJPRICE))/(1+convert(float,A.XTAXRATE)/100))/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)/(1+convert(float,A.XTAXRATE)/100)),2) AB    -- Z/X
		                        ,ROUND(convert(float,A.PFCOUNT)*((convert(float,A.PFPRICE)-convert(float,B.HETONG_price))/(1+convert(float,A.XTAXRATE)/100))*(1+convert(float,A.XTAXRATE)/100)/(convert(float,A.PFCOUNT)*convert(float,A.PFPRICE)),2) AC   -- AA/X
		                        ,A.XTAXRATE      ,A.XTAXTOTAL      ,A.HJTOTAL      ,A.HMLTOTAL      ,A.HMLRATE      ,A.HCOST 
		                        -- into HX_tuangoudan_ler
		 
	                        FROM [HA_BankDataCompare].[dbo].[HX_tuangoudan] A left JOIN HX_shangpin_xinxi B ON A.PLUCODE=B.PLUCODE
	                        WHERE A.BILLNO=@billno order by A.ID

	                    end

                    end
                    ", billno);
                int n = 0;
                ni = new SqlCommand(sql, conn);     //插入语句执行
                n = ni.ExecuteNonQuery();
                ni.Dispose();
                if (n != 0) { Console.Write("海信团购单入库同步成功！"); } else { Console.Write("海信团购单入库同步失败！失败！"); }
                conn.Close();
            }
            catch (Exception ex)
            {
                //MessageBox.Show(ex.Message);
                Console.Write(ex.Message);
            }
            finally
            {
                conn.Close();
                Console.Write("Cloud 关闭数据库成功！");
            }
        }

        /// <summary>
        /// 优惠平衡点插入-执行前必须手工录入数据
        /// </summary>
        /// <param name="qty"></param>
        /// <param name="guaipai_price"></param>
        /// <param name="xs_midu"></param>
        /// <param name="guiding_midu"></param>
        /// <param name="guiding_maoli"></param>
        /// <param name="yh_fudu"></param>
        /// <returns></returns>
        public void qy_youhui_pinghengdian_insert(string qty = "", string guaipai_price = "", string guiding_midu = "", string guiding_maoli = "", string yh_fudu = "",string youpin_name = "")
        {
            string shujuku = "Data Source=************\\sql01;Initial Catalog=HA_BankDataCompare;uid=sa;pwd=Hacloud2015:)";
            int n = 0;
            DateTime currentTime = DateTime.Now;
            string WorkDay1 = currentTime.ToString("yyyy-MM-dd");//当前日期

            //
            string xs_midu = " ";
            SqlConnection conn = new SqlConnection(shujuku);
            DataSet ds = new DataSet();
            try
            {
                conn.Open();
                string sql_dzqb;
                SqlCommand ni;//初始化数据库，并储存sql代码

                sql_dzqb = string.Format(@"
                   	  	declare @qty float               -- 销售量
                        declare @guaipai_price float     -- 挂牌价 
                        declare @workday varchar(50)     -- 录入日期
                        declare @xs_midu float           --销售密度
                        declare @guiding_midu float      -- 规定密度
                        declare @guiding_maoli float     -- 规定毛利
                        declare @yh_fudu float           -- 优惠幅度升
                        declare @youpin_name varchar(50) -- 油品名称
                      
                        set @qty = convert(float,'{0}')   
                        set @guaipai_price = convert(float,'{1}')  
                        set @workday = '{2}'  
                        set @xs_midu = convert(float,'{3}')  
                        set @guiding_midu = convert(float,'{4}')   
                        set @guiding_maoli = convert(float,'{5}')  
                        set @yh_fudu = convert(float,'{6}')   
                        set @youpin_name = '{7}'  
                            
                        -- 根据 qty guaipai_price  guiding_maoli  yh_fudu  guiding_midu xs_midu 
                        -- 存入数据库并计算插入  待后期进行下查询 
                        begin 
                        insert into qy_yh_pinghengdian (youpin_name,qty,qty_kg,guaipai_price,
                                    xs_price,xs_amount,guiding_maoli,
                                    maoli,yh_fudu,yh_fudu_kg,
                                    guiding_midu,xs_midu,yh_maoli,
                                    yh_maoli_kg,
                                    yh_qty_kg,
                                    yh_qty,
                                    yh_xs_amount,
                                    zengfu_bili,
                                    workday
                                    )
                         values( @youpin_name,  @qty,  @qty*@guiding_midu*0.001,  @guaipai_price,
	                            (@guaipai_price/@guiding_midu)*1000,   @qty*(@guaipai_price),  @guiding_maoli,
	                            ((@qty*@guiding_midu*0.001)*@guiding_maoli)/1.13,  @yh_fudu,  @yh_fudu*@guiding_midu*1000,
	                            @guiding_midu,  @guiding_midu,   (@qty*@guiding_midu*0.001)*@guiding_maoli,   --M
	                            @guiding_maoli-(@yh_fudu*@guiding_midu*1000),  
                                (@qty*@guiding_midu*0.001)*@guiding_maoli/(@guiding_maoli-(@yh_fudu*@guiding_midu*1000)),   -- O
	                            (@qty*@guiding_midu*0.001)*@guiding_maoli*1000/((@guiding_maoli-(@yh_fudu*@guiding_midu*1000))*@guiding_midu),    -- P
	                            (@qty*@guiding_midu*0.001)*@guiding_maoli*1000*(@guaipai_price-@yh_fudu)/((@guiding_maoli-(@yh_fudu*@guiding_midu*1000))*@guiding_midu),
	                           ((@qty*@guiding_midu*0.001)*@guiding_maoli*1000/((@guiding_maoli-(@yh_fudu*@guiding_midu*1000))*@guiding_midu)-@qty)/@qty,
	                            @workday 
                                )
                        end		
					  

                ", qty, guaipai_price, WorkDay1, xs_midu, guiding_midu, guiding_maoli, yh_fudu,youpin_name);
                ni = new SqlCommand(sql_dzqb, conn); //插入语句执行
                n = ni.ExecuteNonQuery();
                ni.Dispose();
                if (n != 0) { Console.Write("优惠平衡点数据入库成功！"); } else { Console.Write("优惠平衡点数据入库失败！失败！"); }
                conn.Close();
            }
            catch (Exception ex)
            {
                Console.Write(ex.Message);
            }
            finally
            {
                conn.Close();
            }

        }

        /// <summary>
        /// 液化气订单同步地址和电话
        /// </summary>
        /// <param name="billno"></param>
        public void APP_yyhq_address(string billno)
        {

            string connetStr = "server=************;user=admin;password=**********;database=lpg;sslMode=none;";//STAT库
            MySqlConnection SQLCon = new MySqlConnection(connetStr);
            DataSet ds = new DataSet();
            string result = "";
            try
            {
                SQLCon.Open(); //连接数据库
                //MessageBox.Show("打开数据库");

                //MessageBox.Show(bill_no);//单号
                //MessageBox.Show(stat_time.ToString());//正常的时间
                //MessageBox.Show(stat_d_time);
                string searchStr = string.Format(@"
                     update  lpg.LPG_ORDER a inner join  
 		            (select * from sinopec_product.sr_receiving_address b WHERE b.buy_name = '王洲' and b.identity_card = '46020019790513001X' order by update_time desc limit 1  ) b 
                    on a.BUY_NAME_ =b.buy_name  and   a.IDENTITY_CARD_ = b.identity_card 
                set a.DISTRIB_ADDR_ = b.address , a.RECV_PHONE1_= b.phone ,a.CONTACT_PHONE_ =b.phone,a.ORDER_PHONE_=b.phone
                where a.STATUS_!='9'
                and a.NO_   = '{0}'
                ", billno);//  -- 邮储导入  3和5 是单位时间内的次数

                //MySqlDataAdapter adapter = new MySqlDataAdapter(searchStr, SQLCon);
                MySqlCommand adapter = new MySqlCommand(searchStr, SQLCon);
                int n = adapter.ExecuteNonQuery();
                adapter.Dispose();
                if (n != 0) { Console.WriteLine("修改信息成功", "提示", MessageBoxButton.OK); } else { Console.WriteLine("修改信息失败", "提示", MessageBoxButton.OK); }
            }
            catch (MySqlException ex)
            {
                Console.WriteLine(ex.Message, "提示", MessageBoxButton.OK);     //显示错误信息
            }
            finally
            {
                SQLCon.Close();
                //MessageBox.Show("关闭数据库");

            }


        }

    }

    /// <summary>
    /// 海信团购单明细--数据结构
    /// </summary>
    public class HX_Tuangoudan_mingxi
    {

        public string BILLNO { get; set; }

        public string SERIALNO { get; set; }

        public string PLUID { get; set; }
        //public void setZFNAME(string zfname) { ZFNAME = zfname; }
        public string PLUCODE { get; set; }
        //public void setPAYSSTOTAL(string paysstotal) { PAYSSTOTAL = paysstotal; }
        public string PLUNAME { get; set; }
        public string BARCODE { get; set; }
        public string SPEC { get; set; }
        public string UNIT { get; set; }
        public string PACKUNIT { get; set; }
        public string PACKQTY { get; set; }
        public string PFCOUNT { get; set; }
        public string HJPRICE { get; set; }
        public string PRICE { get; set; }
        public string PFPRICE { get; set; }
        public string TGJ { get; set; }
        public string SGJC { get; set; }
        public string YSTOTAL { get; set; }
        public string SSTOTAL { get; set; }
        public string XTAXRATE { get; set; }
        public string XTAXTOTAL { get; set; }
        public string HJTOTAL { get; set; }
        public string HMLTOTAL { get; set; }
        public string HMLRATE { get; set; }
        public string HCOST { get; set; }

    }

    /// <summary>
    /// 海信团购单--数据结构
    /// </summary>
    public class HX_Tuangoudan 
    {
        public string BILLNO { get; set; }
        public string LRDATE { get; set; }
        public string USERCODE { get; set; }
        public string USERNAME { get; set; }
        public string TJDATE { get; set; }
        public string JZDATE { get; set; }
        public string JZRCODE { get; set; }
        public string JZRNAME { get; set; }
        public string RPTDATE { get; set; }
        public string ORGCODE { get; set; }
        public string ORGNAME { get; set; }
        public string ETPCODE { get; set; }
        public string ETPNAME { get; set; }
        public string YWYID { get; set; }
        public string YWYCODE { get; set; }
        public string YWYNAME { get; set; }
        public string YXDATE { get; set; }
        public string ADDRESS { get; set; }
        public string DHDATE { get; set; }
        public string PFCOUNT { get; set; }
        public string PFTOTAL { get; set; }
        public string SSTOTAL { get; set; }
        public string XTAXTOTAL { get; set; }
        public string QKTOTAL { get; set; }
        public string REMARK { get; set; }
        public string INORGCODE { get; set; }
        public string TJRID { get; set; }
        public string TJRCODE { get; set; }
        public string TJRNAME { get; set; }
        public string AUDITUSERID1 { get; set; }
        public string AUDITUSERCODE1 { get; set; }
        public string AUDITUSERNAME1 { get; set; }
        public string AUDITDATE1 { get; set; }
        public string ZFDATE { get; set; }
        public string ZFRID { get; set; }
        public string ZFRNAME { get; set; }
        public string HJTOTAL { get; set; }
        public string HMLTOTAL { get; set; }
        public string JZDATEPOS { get; set; }
        public string HCOST { get; set; }
        public string PROFIT { get; set; }
        public string ZPCOMNAME { get; set; }
        public string ZPCOMVATNO { get; set; }



    }

}


