﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_sjyz" id="workday_sjyz" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_djyz" id="workday_djyz" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/lingshou_jiayouzhan_zffsblfb?workday_sjyz=" + $('#workday_sjyz').val() + "&workday_djyz=" + $('#workday_djyz').val();
            $.getJSON("/api/values/lingshou_jiayouzhan_zffsblfb?workday_sjyz=" + $('#workday_sjyz').val() + "&workday_djyz=" + $('#workday_djyz').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                      if (data.length > 0)
                      {
                          var jsonobj = $.parseJSON(data);//转化data的格式
                          var htmltable = "";
                          htmltable += '<tr><td>加油站</td><td>微信支付销量</td><td>总销量</td><td>微信支付比例</td><td>支付宝销量</td><td>总销量</td><td>支付宝比例</td><td>银行卡销量</td><td>总销量</td><td>银行卡比例</td><td>优惠券销量</td><td>总销量</td><td>优惠券比例</td> <td>加油卡销量</td><td>总销量</td><td>加油卡比例</td> <td>现金销量</td><td>总销量</td><td>现金比例</td><td>电子钱包销量</td><td>总销量</td><td>电子钱包比例</td><td>总销量</tr>';
                          $.each(jsonobj, function (i, item) {
                              htmltable += '<tr><td>' + item.youzhan + '</td><td>' + item.wxzfxl + '</td><td>' + item.zxl + '</td><td>' + item.wxzfbl + '%</td><td>' + item.zfbxl + '</td><td>' + item.zxl + '</td><td>' + item.zfbbl + '%</td><td>' + item.yhkxl + '</td><td>' + item.zxl + '</td><td>' + item.yhkbl + '%</td><td>' + item.yhqxl + '</td><td>' + item.zxl + '</td><td>' + item.yhqbl + '%</td> <td>' + item.jykxl + '</td><td>' + item.zxl + '</td><td>' + item.jykbl + '%</td> <td>' + item.xjxl + '</td><td>' + item.zxl + '</td><td>' + item.xjbl + '%</td><td>' + item.dzqbxl + '</td><td>' + item.zxl + '</td><td>' + item.dzqbbl + '%</td><td>' + item.zxl + '</tr>';
                          });  //循环each   json数组     <tr>行  <td>列
                          $('#shuju_1').html(htmltable);
                      } else
                      { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/lingshou_jiayouzhan_zffsblfb_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
