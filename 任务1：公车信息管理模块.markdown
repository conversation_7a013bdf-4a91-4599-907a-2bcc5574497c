# 任务1：公车信息管理模块

## 子任务1.1：多数据库实体配置
**位置**：/App_Start/FreeSqlConfig.cs

**要求**：
1. 配置FreeSql支持SQLServer/Oracle/MySQL
2. 实现动态连接字符串切换
3. 代码示例：

```csharp
public static IFreeSql CreateFreeSql() {
    var dbType = ConfigurationManager.AppSettings["MainDB"];
    var connStr = ConfigurationManager.ConnectionStrings[dbType].ConnectionString;
    
    return new FreeSql.FreeSqlBuilder()
        .UseConnectionString(GetDbType(dbType), connStr)
        .UseAutoSyncStructure(false) // 关闭自动迁移
        .Build();
}

private static DataType GetDbType(string type) => type switch {
    "SQLServer" => DataType.SqlServer,
    "Oracle" => DataType.Oracle,
    "MySQL" => DataType.MySql,
    _ => throw new Exception("Unsupported database")
};
```

## 子任务1.2：实体模型设计
**位置**：/Models/Bus.cs

**要求**：
1. 创建Bus实体类
2. 包含基本字段：车牌号、车型、状态、购买日期等
3. 使用FreeSql特性标注

```csharp
[Table(Name = "T_Bus")]
public class Bus
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }
    
    [Column(StringLength = 20, IsNullable = false)]
    public string PlateNumber { get; set; }
    
    [Column(StringLength = 50)]
    public string Model { get; set; }
    
    [Column]
    public BusStatus Status { get; set; }
    
    [Column]
    public DateTime PurchaseDate { get; set; }
    
    [Column]
    public DateTime CreateTime { get; set; } = DateTime.Now;
}

public enum BusStatus
{
    Available = 1,
    InUse = 2,
    Maintenance = 3,
    Retired = 4
}
```

## 子任务1.3：申请记录实体
**位置**：/Models/BusApplication.cs

**要求**：
1. 创建公车申请记录实体
2. 关联Bus实体
3. 包含申请人、时间、状态等信息

```csharp
[Table(Name = "T_BusApplication")]
public class BusApplication
{
    [Column(IsPrimary = true, IsIdentity = true)]
    public int Id { get; set; }
    
    [Column]
    public int BusId { get; set; }
    
    [Column(StringLength = 50, IsNullable = false)]
    public string ApplicantName { get; set; }
    
    [Column(StringLength = 200)]
    public string Purpose { get; set; }
    
    [Column]
    public DateTime StartTime { get; set; }
    
    [Column]
    public DateTime EndTime { get; set; }
    
    [Column]
    public ApplicationStatus Status { get; set; }
    
    [Column]
    public DateTime CreateTime { get; set; } = DateTime.Now;
    
    // 导航属性
    [Navigate(nameof(BusId))]
    public Bus Bus { get; set; }
}

public enum ApplicationStatus
{
    Pending = 1,
    Approved = 2,
    Rejected = 3,
    Completed = 4
}
```

## 子任务1.4：视图模型设计
**位置**：/ViewModels/BusApplicationVM.cs

**要求**：
1. 创建用于前端展示的视图模型
2. 包含查询条件和分页信息

```csharp
public class BusApplicationVM
{
    public int Id { get; set; }
    public string PlateNumber { get; set; }
    public string Model { get; set; }
    public string ApplicantName { get; set; }
    public string Purpose { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public string StatusText { get; set; }
    public DateTime CreateTime { get; set; }
}

public class BusApplicationQueryVM
{
    public string PlateNumber { get; set; }
    public string ApplicantName { get; set; }
    public ApplicationStatus? Status { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int PageIndex { get; set; } = 1;
    public int PageSize { get; set; } = 20;
}
```
