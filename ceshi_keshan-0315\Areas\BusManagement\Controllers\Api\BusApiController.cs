using System;
using System.Linq;
using System.Web.Http;
using ceshi_keshan_0315.Areas.BusManagement.App_Start;
using ceshi_keshan_0315.Areas.BusManagement.Models;

namespace ceshi_keshan_0315.Areas.BusManagement.Controllers.Api
{
    /// <summary>
    /// 公车管理 API 控制器
    /// 新增模块：提供公车信息的 RESTful API 接口
    /// </summary>
    [RoutePrefix("BusManagement/api/Bus")]
    public class BusApiController : ApiController
    {
        private readonly FreeSql.IFreeSql _freeSql = BusFreeSqlConfig.Instance;

        /// <summary>
        /// 获取所有可用公车
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAvailableBuses")]
        public IHttpActionResult GetAvailableBuses()
        {
            try
            {
                var buses = _freeSql.Select<Bus>()
                    .Where(b => b.Status == BusStatus.Available && !b.IsDeleted)
                    .OrderBy(b => b.PlateNumber)
                    .ToList(b => new
                    {
                        b.Id,
                        b.PlateNumber,
                        b.Model,
                        b.<PERSON>,
                        b.<PERSON>tCount,
                        DisplayText = $"{b.PlateNumber} ({b.Model}, {b.SeatCount}座)"
                    });

                return Ok(new { success = true, data = buses });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = "获取可用公车失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 检查指定时间段内公车是否可用
        /// </summary>
        /// <param name="busId">公车ID</param>
        /// <param name="startTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="excludeApplicationId">排除的申请ID（用于编辑时检查）</param>
        /// <returns></returns>
        [HttpGet]
        [Route("CheckAvailability")]
        public IHttpActionResult CheckAvailability(int busId, DateTime startTime, DateTime endTime, int excludeApplicationId = 0)
        {
            try
            {
                // 检查公车是否存在且可用
                var bus = _freeSql.Select<Bus>()
                    .Where(b => b.Id == busId && !b.IsDeleted)
                    .First();

                if (bus == null)
                {
                    return BadRequest(new { success = false, message = "指定的公车不存在" });
                }

                if (bus.Status != BusStatus.Available)
                {
                    return Ok(new { success = false, message = $"公车当前状态为：{GetStatusText(bus.Status)}，不可申请" });
                }

                // 检查时间冲突
                var conflictQuery = _freeSql.Select<BusApplication>()
                    .Where(a => a.BusId == busId && !a.IsDeleted)
                    .Where(a => a.Status == ApplicationStatus.Approved || a.Status == ApplicationStatus.Pending)
                    .Where(a => a.StartTime < endTime && a.EndTime > startTime);

                if (excludeApplicationId > 0)
                {
                    conflictQuery = conflictQuery.Where(a => a.Id != excludeApplicationId);
                }

                var hasConflict = conflictQuery.Any();

                if (hasConflict)
                {
                    var conflictApplication = conflictQuery.First();
                    return Ok(new 
                    { 
                        success = false, 
                        message = $"该时段车辆已被预约（申请人：{conflictApplication.ApplicantName}，时间：{conflictApplication.StartTime:MM-dd HH:mm} - {conflictApplication.EndTime:MM-dd HH:mm}）" 
                    });
                }

                return Ok(new { success = true, message = "该时段车辆可用" });
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("检查车辆可用性失败：" + ex.Message));
            }
        }

        /// <summary>
        /// 获取公车详细信息
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBusDetails/{id:int}")]
        public IHttpActionResult GetBusDetails(int id)
        {
            try
            {
                var bus = _freeSql.Select<Bus>()
                    .Where(b => b.Id == id && !b.IsDeleted)
                    .First();

                if (bus == null)
                {
                    return NotFound();
                }

                var result = new
                {
                    bus.Id,
                    bus.PlateNumber,
                    bus.Model,
                    bus.Brand,
                    bus.SeatCount,
                    Status = bus.Status,
                    StatusText = GetStatusText(bus.Status),
                    bus.PurchaseDate,
                    bus.Remarks,
                    IsAvailable = bus.Status == BusStatus.Available
                };

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = "获取公车详情失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取公车使用统计
        /// </summary>
        /// <param name="busId">公车ID</param>
        /// <param name="days">统计天数（默认30天）</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetUsageStatistics")]
        public IHttpActionResult GetUsageStatistics(int busId, int days = 30)
        {
            try
            {
                var startDate = DateTime.Now.AddDays(-days);
                
                var statistics = _freeSql.Select<BusApplication>()
                    .Where(a => a.BusId == busId && !a.IsDeleted)
                    .Where(a => a.Status == ApplicationStatus.Completed)
                    .Where(a => a.StartTime >= startDate)
                    .ToList();

                var result = new
                {
                    TotalApplications = statistics.Count,
                    TotalHours = statistics.Sum(a => (a.EndTime - a.StartTime).TotalHours),
                    AverageHoursPerUse = statistics.Count > 0 ? statistics.Average(a => (a.EndTime - a.StartTime).TotalHours) : 0,
                    MostFrequentUser = statistics.GroupBy(a => a.ApplicantName)
                        .OrderByDescending(g => g.Count())
                        .FirstOrDefault()?.Key ?? "无"
                };

                return Ok(new { success = true, data = result });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = "获取使用统计失败：" + ex.Message });
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(BusStatus status)
        {
            return status switch
            {
                BusStatus.Available => "可用",
                BusStatus.InUse => "使用中",
                BusStatus.Maintenance => "维修中",
                BusStatus.Retired => "已报废",
                _ => "未知"
            };
        }
    }
}
