# 全局技术栈配置

## 前端技术栈
- **框架**：ASP.NET MVC 5.2.3
- **UI库**：Bootstrap 3.0
- **JS库**：jQuery 1.10.2
- **日期控件**：My97DatePicker
- **数据格式**：JSON (Newtonsoft.Json 6.0.4)

## 后端技术栈
- **框架**：.NET Framework 4.7.2
- **Web服务**：ASP.NET Web API 5.2.3
- **ORM主选**：FreeSql 3.2.807 (多数据库支持)
- **ORM备选**：Entity Framework 6.1.3 (特定模块)
- **文件处理**：NPOI 2.5.1

## 数据库支持
- **主数据库**：SQL Server
- **可选数据库**：Oracle (OracleClient), MySQL (MySql.Data 8.0.33)

## 项目结构
```
Controllers/
  - BusMvcController.cs   // MVC控制器
  - BusApiController.cs    // API控制器
Models/
  - Bus.cs
  - BusApplication.cs
ViewModels/
  - BusApplicationVM.cs
Views/
  - Bus/
    - Index.cshtml
    - _BusGrid.cshtml // 局部视图
App_Start/
  - FreeSqlConfig.cs   // 多数据库配置
```

## 开发规范
- **命名约定**：使用 PascalCase 命名类和方法，camelCase 命名变量
- **数据库连接**：统一使用 FreeSql 进行数据库操作
- **异常处理**：所有 API 方法必须包含 try-catch 异常处理
- **日志记录**：重要操作需要记录日志
- **代码注释**：公共方法必须添加 XML 文档注释
