using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
using ceshi_keshan_0315.App_Start; // 新增：引用 Area 配置类

namespace ceshi_keshan_0315
{
    public class WebApiApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            // 方式1：使用框架默认的 Area 注册（推荐）
            AreaRegistration.RegisterAllAreas();

            // 方式2：使用自定义的 Area 注册配置类（可选）
            // AreaRegistrationConfig.RegisterAllAreas();

            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
        }
    }
}
