using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Http;
using System.Web.Mvc;
using System.Web.Optimization;
using System.Web.Routing;
// using ceshi_keshan_0315.App_Start; // 已删除：不再需要 Area 配置类

namespace ceshi_keshan_0315
{
    public class WebApiApplication : System.Web.HttpApplication
    {
        protected void Application_Start()
        {
            // 注册 Areas（仅保留 HelpPage Area）
            AreaRegistration.RegisterAllAreas();

            // 配置 Web API、过滤器、路由和资源包
            GlobalConfiguration.Configure(WebApiConfig.Register);
            FilterConfig.RegisterGlobalFilters(GlobalFilters.Filters);
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);
        }
    }
}
