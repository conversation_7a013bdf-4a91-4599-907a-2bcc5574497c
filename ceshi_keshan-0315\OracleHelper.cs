﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.OracleClient;
using System.Linq;
using System.Web;

namespace IcCardWebSevice
{
    public class OracleHelper
    {
        //数据库连接字符串(web.config来配置)，可以动态更改SQLString支持多数据库.
        private List<OracleParameter> parmeterList = new List<OracleParameter>();
        public string ConnectionString = System.Configuration.ConfigurationManager.ConnectionStrings["GDCVSDBConString"].ConnectionString;

        public OracleHelper() { }

        public OracleHelper(string constr)
        {
            ConnectionString = constr;
        }
                

        #region  执行简单SQL语句

        /// <summary>
        /// 执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数</returns>
        public int ExecuteSql(string sConn, string SQLString)
        {
            string connstr = System.Configuration.ConfigurationManager.ConnectionStrings[sConn].ConnectionString;
            return ExecuteSqlBase(SQLString, connstr);
        }

        private int ExecuteSqlBase(string SQLString, string connstr, params OracleParameter[] cmdParms)
        {
            using (OracleConnection connection = new OracleConnection(connstr))
            {
                using (OracleCommand cmd = new OracleCommand())
                {

                    try
                    {
                        PrepareCommand(cmd, connection, null, SQLString, cmdParms);
                        int rows = cmd.ExecuteNonQuery();
                        
                        cmd.Parameters.Clear();
                        return rows;
                    }
                    catch (System.Data.OracleClient.OracleException E)
                    {
                        throw new Exception(E.Message);
                    }
                }
            }

        }
        public int ExecuteSql(string SQLString)
        {
            return ExecuteSqlBase(SQLString, ConnectionString);
        }
        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">多条SQL语句</param>		
        public void ExecuteSqlTran(ArrayList SQLStringList)
        {
            using (OracleConnection conn = new OracleConnection(ConnectionString))
            {
                conn.Open();
                OracleCommand cmd = new OracleCommand();
                cmd.Connection = conn;
                OracleTransaction tx = conn.BeginTransaction();
                cmd.Transaction = tx;
                try
                {
                    for (int n = 0; n < SQLStringList.Count; n++)
                    {
                        string strsql = SQLStringList[n].ToString();
                        if (strsql.Trim().Length > 1)
                        {
                            cmd.CommandText = strsql;
                            cmd.ExecuteNonQuery();
                        }
                    }
                    tx.Commit();
                }
                catch (System.Data.OracleClient.OracleException E)
                {
                    tx.Rollback();
                    throw new Exception(E.Message);
                }
            }
        }
        /// <summary>
        /// 向数据库里插入图像格式的字段(和上面情况类似的另一种实例)
        /// </summary>
        /// <param name="strSQL">SQL语句</param>
        /// <param name="fs">图像字节,数据库的字段类型为image的情况</param>
        /// <returns>影响的记录数</returns>
        public int ExecuteSqlInsertImg(string strSQL, byte[] fs)
        {
            using (OracleConnection connection = new OracleConnection(ConnectionString))
            {
                OracleCommand cmd = new OracleCommand(strSQL, connection);
                System.Data.OracleClient.OracleParameter myParameter = new System.Data.OracleClient.OracleParameter("@fs", OracleType.LongRaw);
                myParameter.Value = fs;
                cmd.Parameters.Add(myParameter);
                try
                {
                    connection.Open();
                    int rows = cmd.ExecuteNonQuery();
                    return rows;
                }
                catch (System.Data.OracleClient.OracleException E)
                {
                    throw new Exception(E.Message);
                }
                finally
                {
                    cmd.Dispose();
                    connection.Close();
                }
            }
        }

        /// <summary>
        /// 执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public object GetSingle(string SQLString)
        {
            return GetSingleBase(SQLString);
        }

        private object GetSingleBase(string SQLString, params OracleParameter[] cmdParms)
        {

            using (OracleConnection connection = new OracleConnection(ConnectionString))
            {
                using (OracleCommand cmd = new OracleCommand())
                {
                    try
                    {
                        PrepareCommand(cmd, connection, null, SQLString, cmdParms);
                        object obj = cmd.ExecuteScalar();
                        cmd.Parameters.Clear();
                        if ((Object.Equals(obj, null)) || (Object.Equals(obj, System.DBNull.Value)))
                        {
                            return null;
                        }
                        else
                        {
                            return obj;
                        }
                    }
                    catch (System.Data.OracleClient.OracleException e)
                    {
                        throw new Exception(e.Message);
                    }
                }
            }

        }

        /// <summary>
        /// 执行查询语句，返回OracleDataReader ( 注意：调用该方法后，一定要对SqlDataReader进行Close )
        /// </summary>
        /// <param name="strSQL">查询语句</param>
        /// <returns>OracleDataReader</returns>
        public OracleDataReader ExecuteReader(string strSQL)
        {
            return ExecuteReaderBase(strSQL, ConnectionString);

        }
        /// <summary>
        /// 执行查询语句，返回OracleDataReader ( 注意：调用该方法后，一定要对SqlDataReader进行Close )
        /// </summary>
        /// <param name="strSQL">查询语句</param>
        /// <returns>OracleDataReader</returns>
        public OracleDataReader ExecuteReader(string sConn, string strSQL)
        {
            string connstr = System.Configuration.ConfigurationManager.ConnectionStrings[sConn].ConnectionString;
            return ExecuteReaderBase(strSQL, connstr);

        }
        private OracleDataReader ExecuteReaderBase(string strSQL, string connstr, params OracleParameter[] cmdParms)
        {

            OracleConnection connection = new OracleConnection(connstr);
            OracleCommand cmd = new OracleCommand();
            try
            {
                PrepareCommand(cmd, connection, null, strSQL, cmdParms);
                OracleDataReader myReader = cmd.ExecuteReader(CommandBehavior.CloseConnection);
                cmd.Parameters.Clear();
                return myReader;
            }
            catch (System.Data.OracleClient.OracleException e)
            {
                throw new Exception(e.Message);
            }

        }

        /// <summary>
        /// 执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString)
        {
            return QueryBase(SQLString, ConnectionString, "", null);
        }
        public DataSet QueryUseDbLink(string SQLString, string InsertTempTableSql = "")
        {
            return QueryBase(SQLString, ConnectionString, InsertTempTableSql, null);
        }
        /// <summary>
        /// 执行查询语句，返回DataSet
        /// </summary>
        /// <param name="sConn">【System.Configuration.ConfigurationManager.ConnectionStrings[sConn].ConnectionString】 web.config AppSettings keyname</param>
        /// <param name="sSQLString">查询语句</param>
        /// <returns></returns>
        public DataSet Query(string sConn, string sSQLString)
        {
            string connstr = System.Configuration.ConfigurationManager.ConnectionStrings[sConn].ConnectionString;
            return QueryBase(sSQLString, connstr, "", null);
        }

        private DataSet QueryBase(string sSQLString, string connstr, string InsertTempTableSql, params OracleParameter[] cmdParms)
        {
            String SelectSqlStr = sSQLString;
            using (OracleConnection connection = new OracleConnection(connstr))
            {
                DataSet ds = new DataSet();
                OracleTransaction trans = null;
                try
                {
                    connection.Open();
                    OracleCommand cmdInsert = new OracleCommand();
                    if (!string.IsNullOrWhiteSpace(InsertTempTableSql))
                    {
                        string FixSql = InsertTempTableSql; //string.Format("insert into {0} {1}", TempTableName, sSQLString);
                        SelectSqlStr = sSQLString;//string.Format("select * from {0}", TempTableName);
                        trans = connection.BeginTransaction();
                        PrepareCommand(cmdInsert, connection, trans, FixSql, cmdParms);
                        int rows = cmdInsert.ExecuteNonQuery();
                        cmdInsert.Parameters.Clear();

                    }
                    OracleCommand cmd = new OracleCommand();
                    PrepareCommand(cmd, connection, trans, SelectSqlStr, cmdParms);
                    using (OracleDataAdapter da = new OracleDataAdapter(cmd))
                    {
                        da.Fill(ds, "ds");
                        cmd.Parameters.Clear();
                        if (trans != null)
                        {
                            trans.Commit();
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (trans != null)
                    {
                        trans.Rollback();
                    }

                }

                return ds;
            }
        }

        /// <summary>
        /// 执行查询语句，返回DataTable
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataTable</returns>
        public DataTable GetDataTable(string SQLString)
        {
            return Query(SQLString).Tables[0];
            // DataTable dt = QueryUseDbLink(SQLString, TempTableName).Tables[0];

        }
        public DataTable GetDataTableUseDbLink(string SQLString, string InsertTempTableSql)
        {
            DataTable dt = QueryUseDbLink(SQLString, InsertTempTableSql).Tables[0];
            return dt;
        }
        /// <summary>
        /// 执行查询语句，返回DataTable
        /// </summary>
        /// <param name="sConn">链接字符串</param>
        /// <param name="sSQLString">查询语句</param>
        /// <returns></returns>
        public DataTable GetDataTable(string sConn, string sSQLString)
        {
            DataTable dt = Query(sConn, sSQLString).Tables[0];
            return dt;
        }
        /// <summary>
        /// 执行带一个存储过程参数的的SQL语句。
        /// </summary>
        /// <param name="execSql">要执行的SQL语句</param>
        /// /// <param name="sConn">数据库连接</param>
        /// <returns>影响的行数</returns>
        public int ExecuteNonQuery(string sConn, string sSql)
        {
            int count = 0;
            using (OracleConnection conn = new OracleConnection(ConnectionString))
            {
                conn.Open();
                using (OracleTransaction tran = conn.BeginTransaction())
                {
                    try
                    {
                        using (OracleCommand cmd = CreateCommand(conn, sSql))
                        {
                            cmd.Transaction = tran;
                            count = cmd.ExecuteNonQuery();
                        }
                        tran.Commit();
                    }
                    catch (Exception e)
                    {
                        tran.Rollback();
                        parmeterList.Clear();
                        throw e;
                    }
                }
                conn.Close();
            }
            return count;
        }
        /// <summary>
        /// 为生成的OracleCommand对象添加类型化参数
        /// </summary>
        /// <param name="parmenterName">对应的参数名</param>
        /// <param name="type">参数的类型</param>
        /// <param name="value">参数的值</param>
        public void AddParmeter(string parmenterName, DbType type, object value)
        {
            AddParmeter(parmenterName, type, value);
            //OracleParameter parmenter = new OracleParameter();
            //if (parmenterName.IndexOf(':') < 0)
            //{
            //    parmenterName = ":" + parmenterName;
            //}
            //parmenter.ParameterName = parmenterName;
            //parmenter.Value = value;
            //parmenter.DbType = type;
            //parmeterList.Add(parmenter);
        }
        /// <summary>
        /// 为生成的OracleCommand对象添加类型化参数
        /// </summary>
        /// <param name="parmenterName">对应的参数名</param>
        /// <param name="type">参数的类型</param>
        /// <param name="value">参数的值</param>
        public void AddParmeter(string parmenterName, OracleType type, object value)
        {
            OracleParameter parmenter = new OracleParameter();
            if (parmenterName.IndexOf(':') < 0)
            {
                parmenterName = ":" + parmenterName;
            }
            parmenter.ParameterName = parmenterName;
            parmenter.Value = value;
            parmenter.OracleType = type;

            parmeterList.Add(parmenter);
        }
        /// <summary>
        /// 表示对数据库执行SQL或存储过程
        /// </summary>
        /// <param name="conn">打开数据库连接</param>
        /// <param name="commandText">SQL语句</param>
        /// <returns></returns>
        public OracleCommand CreateCommand(OracleConnection conn, string commandText)
        {
            OracleCommand cmd = new OracleCommand();
            cmd.Connection = conn;
            cmd.CommandType = CommandType.Text;
            cmd.CommandText = commandText;
            for (int i = 0; i < parmeterList.Count; i++)
            {
                cmd.Parameters.Add(parmeterList[i]);
            }
            parmeterList.Clear();
            return cmd;
        }
        #endregion

        #region 执行带参数的SQL语句

        /// <summary>
        /// 执行SQL语句，返回影响的记录数
        /// </summary>
        /// <param name="SQLString">SQL语句</param>
        /// <returns>影响的记录数</returns>
        public int ExecuteSql(string SQLString, params OracleParameter[] cmdParms)
        {
            return ExecuteSqlBase(SQLString, ConnectionString, cmdParms);

        }

        /// <summary>
        /// 执行多条SQL语句，实现数据库事务。
        /// </summary>
        /// <param name="SQLStringList">SQL语句的哈希表（key为sql语句，value是该语句的OracleParameter[]）</param>
        public void ExecuteSqlTran(Hashtable SQLStringList)
        {
            using (OracleConnection conn = new OracleConnection(ConnectionString))
            {

                conn.Open();
                using (OracleTransaction trans = conn.BeginTransaction())
                {

                    OracleCommand cmd = new OracleCommand();
                    try
                    {
                        //循环
                        foreach (DictionaryEntry myDE in SQLStringList)
                        {
                            string cmdText = myDE.Key.ToString();
                            OracleParameter[] cmdParms = (OracleParameter[])myDE.Value;
                            PrepareCommand(cmd, conn, trans, cmdText, cmdParms);
                            int val = cmd.ExecuteNonQuery();
                            cmd.Parameters.Clear();

                            trans.Commit();
                        }
                    }
                    catch
                    {
                        trans.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// 执行一条计算查询结果语句，返回查询结果（object）。
        /// </summary>
        /// <param name="SQLString">计算查询结果语句</param>
        /// <returns>查询结果（object）</returns>
        public object GetSingle(string SQLString, params OracleParameter[] cmdParms)
        {
            return GetSingleBase(SQLString, cmdParms);

        }

        /// <summary>
        /// 执行查询语句，返回OracleDataReader ( 注意：调用该方法后，一定要对SqlDataReader进行Close )
        /// </summary>
        /// <param name="strSQL">查询语句</param>
        /// <returns>OracleDataReader</returns>
        public OracleDataReader ExecuteReader(string SQLString, params OracleParameter[] cmdParms)
        {
            return ExecuteReaderBase(SQLString, ConnectionString, cmdParms);


        }

        /// <summary>
        /// 执行查询语句，返回DataSet
        /// </summary>
        /// <param name="SQLString">查询语句</param>
        /// <returns>DataSet</returns>
        public DataSet Query(string SQLString, params OracleParameter[] cmdParms)
        {
            return QueryBase(SQLString, ConnectionString, "", cmdParms);
        }

        private void PrepareCommand(OracleCommand cmd, OracleConnection conn, OracleTransaction trans, string cmdText, OracleParameter[] cmdParms)
        {
            if (conn.State != ConnectionState.Open)
                conn.Open();
            if (cmd != null)
            {
                cmd.Connection = conn;
                cmd.CommandText = cmdText;
            }
            if (trans != null)
                cmd.Transaction = trans;
            cmd.CommandType = CommandType.Text;//cmdType;
            if (cmdParms != null)
            {
                foreach (OracleParameter parm in cmdParms)
                    cmd.Parameters.Add(parm);
            }
        }

        #endregion

        #region 存储过程操作

        /// <summary>
        /// 执行存储过程 返回SqlDataReader ( 注意：调用该方法后，一定要对SqlDataReader进行Close )
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>OracleDataReader</returns>
        public OracleDataReader RunProcedure(string storedProcName, IDataParameter[] parameters)
        {
            OracleConnection connection = new OracleConnection(ConnectionString);
            OracleDataReader returnReader;
            connection.Open();
            OracleCommand command = BuildQueryCommand(connection, storedProcName, parameters);
            command.CommandType = CommandType.StoredProcedure;
            returnReader = command.ExecuteReader(CommandBehavior.CloseConnection);
            return returnReader;
        }

        /// <summary>
        /// 执行存储过程
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="tableName">DataSet结果中的表名</param>
        /// <returns>DataSet</returns>
        public DataSet RunProcedure(string storedProcName, IDataParameter[] parameters, string tableName)
        {
            using (OracleConnection connection = new OracleConnection(ConnectionString))
            {
                DataSet dataSet = new DataSet();
                connection.Open();
                OracleDataAdapter sqlDA = new OracleDataAdapter();
                sqlDA.SelectCommand = BuildQueryCommand(connection, storedProcName, parameters);
                sqlDA.Fill(dataSet, tableName);
                connection.Close();
                return dataSet;
            }
        }

        /// <summary>
        /// 构建 OracleCommand 对象(用来返回一个结果集，而不是一个整数值)
        /// </summary>
        /// <param name="connection">数据库连接</param>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>OracleCommand</returns>
        private OracleCommand BuildQueryCommand(OracleConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            OracleCommand command = new OracleCommand(storedProcName, connection);
            command.CommandType = CommandType.StoredProcedure;
            foreach (OracleParameter parameter in parameters)
            {
                command.Parameters.Add(parameter);
            }
            return command;
        }

        /// <summary>
        /// 执行存储过程，返回影响的行数		
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <param name="rowsAffected">影响的行数</param>
        /// <returns></returns>
        public int RunProcedure(string storedProcName, IDataParameter[] parameters, out int rowsAffected)
        {
            using (OracleConnection connection = new OracleConnection(ConnectionString))
            {
                int result;
                connection.Open();
                OracleCommand command = BuildIntCommand(connection, storedProcName, parameters);
                rowsAffected = command.ExecuteNonQuery();
                result = (int)command.Parameters["ReturnValue"].Value;
                //Connection.Close();
                return result;
            }
        }

        /// <summary>
        /// 创建 OracleCommand 对象实例(用来返回一个整数值)	
        /// </summary>
        /// <param name="storedProcName">存储过程名</param>
        /// <param name="parameters">存储过程参数</param>
        /// <returns>OracleCommand 对象实例</returns>
        private OracleCommand BuildIntCommand(OracleConnection connection, string storedProcName, IDataParameter[] parameters)
        {
            OracleCommand command = BuildQueryCommand(connection, storedProcName, parameters);
            command.Parameters.Add(new OracleParameter("ReturnValue",
                OracleType.Int32, 4, ParameterDirection.ReturnValue,
                false, 0, 0, string.Empty, DataRowVersion.Default, null));
            return command;
        }

        #endregion


        public DataTable getPagingTable(string sqlOrTbname, int pageIndex, int pageSize, ref int totalRows, ref int totalPages)
        {
            //--------
            //存储过程获取dataset数据集  http://www.cnblogs.com/LuGang/archive/2009/10/31/1593655.html
            //根据存储过程的参数个数及类型生成参数对象
            OracleParameter p1 = new OracleParameter("rowCountPerPage", OracleType.Float, 10);
            OracleParameter p2 = new OracleParameter("indexNowPage", OracleType.Float, 10);
            OracleParameter p3 = new OracleParameter("tabName", OracleType.VarChar, 4000);
            OracleParameter p4 = new OracleParameter("totalRows", OracleType.Int32, 10);
            OracleParameter p5 = new OracleParameter("totalPages", OracleType.Int32, 10);
            OracleParameter p6 = new OracleParameter("p_cursor", OracleType.Cursor);

            //设置参数的输入输出类型,默认为输入
            p1.Direction = ParameterDirection.Input;
            p2.Direction = ParameterDirection.Input;
            p3.Direction = ParameterDirection.Input;
            p4.Direction = ParameterDirection.Output;
            p5.Direction = ParameterDirection.Output;
            p6.Direction = ParameterDirection.Output;

            //对输入参数定义初值,输出参数不必赋值.
            p1.Value = pageSize;
            p2.Value = pageIndex;
            p3.Value = sqlOrTbname;
            OracleParameter[] myParams = new OracleParameter[6] { p1, p2, p3, p4, p5, p6 };
            DataSet ds = RunProcedure("p_pak1.sp_cur2", myParams, "PO");
            totalRows = (int)p4.Value;
            totalPages = (int)(p5.Value);
            //-----------          
            return ds.Tables[0];
        }
        /// <summary>
        /// 过滤影响SQL或系统的字符
        /// </summary>
        /// <param name="strOfCheck"></param>
        /// <returns></returns>
        public string checkString(string strOfCheck)
        {
            strOfCheck = strOfCheck.Replace("\"", "“");
            strOfCheck = strOfCheck.Replace("\'", "“");
            return strOfCheck;
        }
    }
}