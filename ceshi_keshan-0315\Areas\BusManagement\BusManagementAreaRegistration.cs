using System.Web.Mvc;

namespace ceshi_keshan_0315.Areas.BusManagement
{
    /// <summary>
    /// 公车管理模块 Area 注册
    /// 新增模块：实现公车信息管理和申请审批功能
    /// </summary>
    public class BusManagementAreaRegistration : AreaRegistration 
    {
        public override string AreaName 
        {
            get 
            {
                return "BusManagement";
            }
        }

        public override void RegisterArea(AreaRegistrationContext context) 
        {
            // API 路由配置
            context.MapRoute(
                "BusManagement_api",
                "BusManagement/api/{controller}/{action}/{id}",
                new { action = "Index", id = UrlParameter.Optional },
                namespaces: new[] { "ceshi_keshan_0315.Areas.BusManagement.Controllers.Api" }
            );

            // MVC 路由配置
            context.MapRoute(
                "BusManagement_default",
                "BusManagement/{controller}/{action}/{id}",
                defaults: new { action = "Index", id = UrlParameter.Optional },
                namespaces: new[] { "ceshi_keshan_0315.Areas.BusManagement.Controllers" }
            );
        }
    }
}
