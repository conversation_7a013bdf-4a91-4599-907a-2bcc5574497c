﻿
@{
    ViewBag.Title = "APP石化钱包休眠用户查询";
}


<h2>APP石化钱包休眠用户查询</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="end_time" id="end_time" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Btn_dipin_cx" type="button" value="低频客户查询" />
                <input id="Btn_dipin" type="submit" value="低频客户导出" />

            </div>
            <div class="pull-right">
                <input id="Button1" type="button" value="休眠客户查询" />
                &nbsp;<input id="btn" type="submit" value="休眠客户导出" />
            </div>
            <div class="pull-right">
                <input id="Btn_yaobai_cx" type="button" value="摇摆客户查询" />
                <input id="Btn_yaobai" type="submit" value="摇摆客户导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/APP_shqb_xiumian?end_time=" + $('#end_time').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>手机号</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.user_code 
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/APP_shqb_xiumian_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

        $('#Btn_dipin_cx').click(function () {

            $.getJSON("/api/values/APP_shqb_dipin?end_time=" + $('#end_time').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>手机号</td><td>笔数</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.user_code + '</td><td>' + item.num
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });


        $('#Btn_dipin').click(function () {
            $("#form1").attr("action", "/Home/APP_shqb_dipin_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#Btn_dipin').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });


        $('#Btn_yaobai_cx').click(function () {

            $.getJSON("/api/values/APP_shqb_yaobai?end_time=" + $('#end_time').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>手机号</td><td>笔数</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.user_code + '</td><td>' + item.num
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });


        $('#Btn_yaobai').click(function () {
            $("#form1").attr("action", "/Home/APP_shqb_yaobai_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#Btn_yaobai').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

    });
</script>
