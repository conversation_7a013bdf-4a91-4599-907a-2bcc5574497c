﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace ceshi_keshan_0315.ServiceReference1 {
    using System.Data;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServiceReference1.hx_xiaoshou_jiekouSoap")]
    public interface hx_xiaoshou_jiekouSoap {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HelloWorld", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HelloWorld(string code, System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HelloWorld", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HelloWorldAsync(string code, System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/fenbo_dan_hk", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable fenbo_dan_hk(System.DateTime workday3, System.DateTime workday4);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/fenbo_dan_hk", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> fenbo_dan_hkAsync(System.DateTime workday3, System.DateTime workday4);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/fenbo_mingxi", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable fenbo_mingxi(string code2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/fenbo_mingxi", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> fenbo_mingxiAsync(string code2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/shangpin_shuxing", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable shangpin_shuxing(string plucode, string orgcode, string pluname, string barcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/shangpin_shuxing", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> shangpin_shuxingAsync(string plucode, string orgcode, string pluname, string barcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/kucun_xs", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable kucun_xs(string code, System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/kucun_xs", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> kucun_xsAsync(string code, System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/haixin_mendian_kucun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable haixin_mendian_kucun();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/haixin_mendian_kucun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> haixin_mendian_kucunAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/xiaoshou_pm", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable xiaoshou_pm(System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/xiaoshou_pm", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> xiaoshou_pmAsync(System.DateTime workday1, System.DateTime workday2);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/zhinengbuhuo_jinhuoliang", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable zhinengbuhuo_jinhuoliang(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/zhinengbuhuo_jinhuoliang", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> zhinengbuhuo_jinhuoliangAsync(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/quan_xiaoshou_mingxi", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable quan_xiaoshou_mingxi(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/quan_xiaoshou_mingxi", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> quan_xiaoshou_mingxiAsync(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshou_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_xiaoshou_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshou_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshou_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_zhifufangshi_chaxun(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_chaxunAsync(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_huizong_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_zhifufangshi_huizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_huizong_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_huizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_mingxi_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_zhifufangshi_mingxi_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_zhifufangshi_mingxi_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_mingxi_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshoumingxi_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_xiaoshoumingxi_chaxun(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshoumingxi_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshoumingxi_chaxunAsync(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshoudan_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_xiaoshoudan_chaxun(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshoudan_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshoudan_chaxunAsync(string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshouhuizong_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_xiaoshouhuizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshouhuizong_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshouhuizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshouhuizong_yuechaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable APP_ziti_xiaoshouhuizong_yuechaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/APP_ziti_xiaoshouhuizong_yuechaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshouhuizong_yuechaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_yanshoudan_chaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_yanshoudan_chaxun(System.DateTime workday33, System.DateTime workday44, string SUPNAME);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_yanshoudan_chaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_yanshoudan_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string SUPNAME);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiche_zhifufangshi_huizongchaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_xiche_zhifufangshi_huizongchaxun(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiche_zhifufangshi_huizongchaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_xiche_zhifufangshi_huizongchaxunAsync(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiangyan_kucunchaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_xiangyan_kucunchaxun(string orgcode, string orgname, string plucode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiangyan_kucunchaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_xiangyan_kucunchaxunAsync(string orgcode, string orgname, string plucode);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiaoshou_mingxi_tSalPluDetail", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_xiaoshou_mingxi_tSalPluDetail(int serialno_start, int serialno_end);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiaoshou_mingxi_tSalPluDetail", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_xiaoshou_mingxi_tSalPluDetailAsync(int serialno_start, int serialno_end);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiaoshou_maxserialno_tSalPluDetail", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_xiaoshou_maxserialno_tSalPluDetail();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_xiaoshou_maxserialno_tSalPluDetail", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_xiaoshou_maxserialno_tSalPluDetailAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_bianlidian_bishu", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_bianlidian_bishu(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_bianlidian_bishu", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_bianlidian_bishuAsync(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_diaobodan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_diaobodan(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_diaobodan", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_diaobodanAsync(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_yanshoudan", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_yanshoudan(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_yanshoudan", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_yanshoudanAsync(System.DateTime workday33, System.DateTime workday44);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_tuangoudan_TJ", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_tuangoudan_TJ(string billno, System.DateTime workday_k, System.DateTime workday_d);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_tuangoudan_TJ", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_tuangoudan_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_cuxiaoshangping_xiaoshouchaxun", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_cuxiaoshangping_xiaoshouchaxun(System.DateTime workday1, System.DateTime workday2, string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_cuxiaoshangping_xiaoshouchaxun", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_cuxiaoshangping_xiaoshouchaxunAsync(System.DateTime workday1, System.DateTime workday2, string billno);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_cuxiaodan_TJ", ReplyAction="*")]
        [System.ServiceModel.XmlSerializerFormatAttribute(SupportFaults=true)]
        System.Data.DataTable HX_cuxiaodan_TJ(string billno, System.DateTime workday_k);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/HX_cuxiaodan_TJ", ReplyAction="*")]
        System.Threading.Tasks.Task<System.Data.DataTable> HX_cuxiaodan_TJAsync(string billno, System.DateTime workday_k);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public interface hx_xiaoshou_jiekouSoapChannel : ceshi_keshan_0315.ServiceReference1.hx_xiaoshou_jiekouSoap, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "4.0.0.0")]
    public partial class hx_xiaoshou_jiekouSoapClient : System.ServiceModel.ClientBase<ceshi_keshan_0315.ServiceReference1.hx_xiaoshou_jiekouSoap>, ceshi_keshan_0315.ServiceReference1.hx_xiaoshou_jiekouSoap {
        
        public hx_xiaoshou_jiekouSoapClient() {
        }
        
        public hx_xiaoshou_jiekouSoapClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public hx_xiaoshou_jiekouSoapClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public hx_xiaoshou_jiekouSoapClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public hx_xiaoshou_jiekouSoapClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public System.Data.DataTable HelloWorld(string code, System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.HelloWorld(code, workday1, workday2);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HelloWorldAsync(string code, System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.HelloWorldAsync(code, workday1, workday2);
        }
        
        public System.Data.DataTable fenbo_dan_hk(System.DateTime workday3, System.DateTime workday4) {
            return base.Channel.fenbo_dan_hk(workday3, workday4);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> fenbo_dan_hkAsync(System.DateTime workday3, System.DateTime workday4) {
            return base.Channel.fenbo_dan_hkAsync(workday3, workday4);
        }
        
        public System.Data.DataTable fenbo_mingxi(string code2) {
            return base.Channel.fenbo_mingxi(code2);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> fenbo_mingxiAsync(string code2) {
            return base.Channel.fenbo_mingxiAsync(code2);
        }
        
        public System.Data.DataTable shangpin_shuxing(string plucode, string orgcode, string pluname, string barcode) {
            return base.Channel.shangpin_shuxing(plucode, orgcode, pluname, barcode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> shangpin_shuxingAsync(string plucode, string orgcode, string pluname, string barcode) {
            return base.Channel.shangpin_shuxingAsync(plucode, orgcode, pluname, barcode);
        }
        
        public System.Data.DataTable kucun_xs(string code, System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.kucun_xs(code, workday1, workday2);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> kucun_xsAsync(string code, System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.kucun_xsAsync(code, workday1, workday2);
        }
        
        public System.Data.DataTable haixin_mendian_kucun() {
            return base.Channel.haixin_mendian_kucun();
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> haixin_mendian_kucunAsync() {
            return base.Channel.haixin_mendian_kucunAsync();
        }
        
        public System.Data.DataTable xiaoshou_pm(System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.xiaoshou_pm(workday1, workday2);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> xiaoshou_pmAsync(System.DateTime workday1, System.DateTime workday2) {
            return base.Channel.xiaoshou_pmAsync(workday1, workday2);
        }
        
        public System.Data.DataTable zhinengbuhuo_jinhuoliang(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1) {
            return base.Channel.zhinengbuhuo_jinhuoliang(day_min, day_max, day_song, kucun_min, workday1);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> zhinengbuhuo_jinhuoliangAsync(string day_min, string day_max, string day_song, string kucun_min, System.DateTime workday1) {
            return base.Channel.zhinengbuhuo_jinhuoliangAsync(day_min, day_max, day_song, kucun_min, workday1);
        }
        
        public System.Data.DataTable quan_xiaoshou_mingxi(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.quan_xiaoshou_mingxi(workday33, workday44);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> quan_xiaoshou_mingxiAsync(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.quan_xiaoshou_mingxiAsync(workday33, workday44);
        }
        
        public System.Data.DataTable APP_ziti_xiaoshou_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshou_chaxun(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshou_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshou_chaxunAsync(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Data.DataTable APP_ziti_zhifufangshi_chaxun(string billno) {
            return base.Channel.APP_ziti_zhifufangshi_chaxun(billno);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_chaxunAsync(string billno) {
            return base.Channel.APP_ziti_zhifufangshi_chaxunAsync(billno);
        }
        
        public System.Data.DataTable APP_ziti_zhifufangshi_huizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode) {
            return base.Channel.APP_ziti_zhifufangshi_huizong_chaxun(workday33, workday44, orgname, orgcode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_huizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode) {
            return base.Channel.APP_ziti_zhifufangshi_huizong_chaxunAsync(workday33, workday44, orgname, orgcode);
        }
        
        public System.Data.DataTable APP_ziti_zhifufangshi_mingxi_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno) {
            return base.Channel.APP_ziti_zhifufangshi_mingxi_chaxun(workday33, workday44, orgname, orgcode, billno);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_zhifufangshi_mingxi_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string orgcode, string billno) {
            return base.Channel.APP_ziti_zhifufangshi_mingxi_chaxunAsync(workday33, workday44, orgname, orgcode, billno);
        }
        
        public System.Data.DataTable APP_ziti_xiaoshoumingxi_chaxun(string billno) {
            return base.Channel.APP_ziti_xiaoshoumingxi_chaxun(billno);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshoumingxi_chaxunAsync(string billno) {
            return base.Channel.APP_ziti_xiaoshoumingxi_chaxunAsync(billno);
        }
        
        public System.Data.DataTable APP_ziti_xiaoshoudan_chaxun(string billno) {
            return base.Channel.APP_ziti_xiaoshoudan_chaxun(billno);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshoudan_chaxunAsync(string billno) {
            return base.Channel.APP_ziti_xiaoshoudan_chaxunAsync(billno);
        }
        
        public System.Data.DataTable APP_ziti_xiaoshouhuizong_chaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshouhuizong_chaxun(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshouhuizong_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshouhuizong_chaxunAsync(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Data.DataTable APP_ziti_xiaoshouhuizong_yuechaxun(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshouhuizong_yuechaxun(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> APP_ziti_xiaoshouhuizong_yuechaxunAsync(System.DateTime workday33, System.DateTime workday44, string orgname, string djzbillno, string orgcode) {
            return base.Channel.APP_ziti_xiaoshouhuizong_yuechaxunAsync(workday33, workday44, orgname, djzbillno, orgcode);
        }
        
        public System.Data.DataTable HX_yanshoudan_chaxun(System.DateTime workday33, System.DateTime workday44, string SUPNAME) {
            return base.Channel.HX_yanshoudan_chaxun(workday33, workday44, SUPNAME);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_yanshoudan_chaxunAsync(System.DateTime workday33, System.DateTime workday44, string SUPNAME) {
            return base.Channel.HX_yanshoudan_chaxunAsync(workday33, workday44, SUPNAME);
        }
        
        public System.Data.DataTable HX_xiche_zhifufangshi_huizongchaxun(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_xiche_zhifufangshi_huizongchaxun(workday33, workday44);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_xiche_zhifufangshi_huizongchaxunAsync(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_xiche_zhifufangshi_huizongchaxunAsync(workday33, workday44);
        }
        
        public System.Data.DataTable HX_xiangyan_kucunchaxun(string orgcode, string orgname, string plucode) {
            return base.Channel.HX_xiangyan_kucunchaxun(orgcode, orgname, plucode);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_xiangyan_kucunchaxunAsync(string orgcode, string orgname, string plucode) {
            return base.Channel.HX_xiangyan_kucunchaxunAsync(orgcode, orgname, plucode);
        }
        
        public System.Data.DataTable HX_xiaoshou_mingxi_tSalPluDetail(int serialno_start, int serialno_end) {
            return base.Channel.HX_xiaoshou_mingxi_tSalPluDetail(serialno_start, serialno_end);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_xiaoshou_mingxi_tSalPluDetailAsync(int serialno_start, int serialno_end) {
            return base.Channel.HX_xiaoshou_mingxi_tSalPluDetailAsync(serialno_start, serialno_end);
        }
        
        public System.Data.DataTable HX_xiaoshou_maxserialno_tSalPluDetail() {
            return base.Channel.HX_xiaoshou_maxserialno_tSalPluDetail();
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_xiaoshou_maxserialno_tSalPluDetailAsync() {
            return base.Channel.HX_xiaoshou_maxserialno_tSalPluDetailAsync();
        }
        
        public System.Data.DataTable HX_bianlidian_bishu(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname) {
            return base.Channel.HX_bianlidian_bishu(workday33, workday44, orgcode, orgname);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_bianlidian_bishuAsync(System.DateTime workday33, System.DateTime workday44, string orgcode, string orgname) {
            return base.Channel.HX_bianlidian_bishuAsync(workday33, workday44, orgcode, orgname);
        }
        
        public System.Data.DataTable HX_diaobodan(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_diaobodan(workday33, workday44);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_diaobodanAsync(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_diaobodanAsync(workday33, workday44);
        }
        
        public System.Data.DataTable HX_yanshoudan(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_yanshoudan(workday33, workday44);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_yanshoudanAsync(System.DateTime workday33, System.DateTime workday44) {
            return base.Channel.HX_yanshoudanAsync(workday33, workday44);
        }
        
        public System.Data.DataTable HX_tuangoudan_TJ(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            return base.Channel.HX_tuangoudan_TJ(billno, workday_k, workday_d);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_tuangoudan_TJAsync(string billno, System.DateTime workday_k, System.DateTime workday_d) {
            return base.Channel.HX_tuangoudan_TJAsync(billno, workday_k, workday_d);
        }
        
        public System.Data.DataTable HX_cuxiaoshangping_xiaoshouchaxun(System.DateTime workday1, System.DateTime workday2, string billno) {
            return base.Channel.HX_cuxiaoshangping_xiaoshouchaxun(workday1, workday2, billno);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_cuxiaoshangping_xiaoshouchaxunAsync(System.DateTime workday1, System.DateTime workday2, string billno) {
            return base.Channel.HX_cuxiaoshangping_xiaoshouchaxunAsync(workday1, workday2, billno);
        }
        
        public System.Data.DataTable HX_cuxiaodan_TJ(string billno, System.DateTime workday_k) {
            return base.Channel.HX_cuxiaodan_TJ(billno, workday_k);
        }
        
        public System.Threading.Tasks.Task<System.Data.DataTable> HX_cuxiaodan_TJAsync(string billno, System.DateTime workday_k) {
            return base.Channel.HX_cuxiaodan_TJAsync(billno, workday_k);
        }
    }
}
