﻿@{
    //Layout = null;
}


@{
    ViewBag.Title = "Import";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="layui-upload" style="margin-top:20px">
    <button type="button" class="layui-btn layui-btn-normal" id="selectFile" style="margin-left:20px">选择文件</button>
    <button type="button" class="layui-btn" id="upload">开始上传</button>
    <a href="~/Upload/Template/模板.xlsx" class="layui-btn">上传模板下载</a>
</div>

<script>
        layui.use(['upload','form', 'table', 'laydate', 'layer'], function () {
            var $ = layui.jquery, upload = layui.upload, layer = layui.layer ,form = layui.form,table = layui.table;
            var d = new Date();
            var sec = d.getSeconds();
            //选完文件后不自动上传
            upload.render({
                elem: '#selectFile'
                , url:'@Url.Action("Import", "Home")' //改成您自己的上传接口
                , auto: false
                , size: 102400 //限制文件大小，单位 KB
                , exts: 'xlsx' //只允许上传Excel文件
                , bindAction: '#upload'
                , done: function (res) {
                    layer.msg(res.msg);
                }
            });


    });
</script>

 
