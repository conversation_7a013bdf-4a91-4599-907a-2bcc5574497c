using System;
using System.Linq;
using System.Web.Http;
using ceshi_keshan_0315.Areas.BusManagement.App_Start;
using ceshi_keshan_0315.Areas.BusManagement.Models;
using ceshi_keshan_0315.Areas.BusManagement.ViewModels;

namespace ceshi_keshan_0315.Areas.BusManagement.Controllers.Api
{
    /// <summary>
    /// 公车申请 API 控制器
    /// 新增模块：提供公车申请和审批的 RESTful API 接口
    /// </summary>
    [RoutePrefix("BusManagement/api/Application")]
    public class ApplicationApiController : ApiController
    {
        private readonly FreeSql.IFreeSql _freeSql = BusFreeSqlConfig.Instance;

        /// <summary>
        /// 创建公车申请
        /// </summary>
        /// <param name="model">申请信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Create")]
        public IHttpActionResult CreateApplication([FromBody]BusApplication model)
        {
            try
            {
                // 数据验证
                if (model == null)
                {
                    return BadRequest(new { success = false, message = "申请信息不能为空" });
                }

                if (string.IsNullOrEmpty(model.ApplicantName))
                {
                    return BadRequest(new { success = false, message = "申请人姓名不能为空" });
                }

                if (string.IsNullOrEmpty(model.Purpose))
                {
                    return BadRequest(new { success = false, message = "用车目的不能为空" });
                }

                if (model.StartTime >= model.EndTime)
                {
                    return BadRequest(new { success = false, message = "结束时间必须大于开始时间" });
                }

                if (model.StartTime <= DateTime.Now)
                {
                    return BadRequest(new { success = false, message = "开始时间不能早于当前时间" });
                }

                // 检查公车是否存在且可用
                var bus = _freeSql.Select<Bus>()
                    .Where(b => b.Id == model.BusId && !b.IsDeleted)
                    .First();

                if (bus == null)
                {
                    return BadRequest(new { success = false, message = "指定的公车不存在" });
                }

                if (bus.Status != BusStatus.Available)
                {
                    return BadRequest(new { success = false, message = "公车当前不可用" });
                }

                // 检查时间冲突
                var hasConflict = _freeSql.Select<BusApplication>()
                    .Where(a => a.BusId == model.BusId && !a.IsDeleted)
                    .Where(a => a.Status == ApplicationStatus.Approved || a.Status == ApplicationStatus.Pending)
                    .Where(a => a.StartTime < model.EndTime && a.EndTime > model.StartTime)
                    .Any();

                if (hasConflict)
                {
                    return BadRequest(new { success = false, message = "该时段车辆已被预约" });
                }

                // 创建申请记录
                var application = new BusApplication
                {
                    BusId = model.BusId,
                    ApplicantName = model.ApplicantName,
                    Department = model.Department,
                    Phone = model.Phone,
                    Purpose = model.Purpose,
                    Destination = model.Destination,
                    StartTime = model.StartTime,
                    EndTime = model.EndTime,
                    EstimatedPeople = model.EstimatedPeople,
                    Status = ApplicationStatus.Pending,
                    CreateTime = DateTime.Now
                };

                var id = _freeSql.Insert(application).ExecuteIdentity();

                return Ok(new { success = true, id, message = "申请提交成功，等待审批" });
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("创建申请失败：" + ex.Message));
            }
        }

        /// <summary>
        /// 审批申请
        /// </summary>
        /// <param name="model">审批信息</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Review")]
        public IHttpActionResult ReviewApplication([FromBody]ReviewActionVM model)
        {
            try
            {
                if (model == null || model.ApplicationId <= 0)
                {
                    return BadRequest(new { success = false, message = "申请ID不能为空" });
                }

                if (model.ActionType != ApplicationStatus.Approved && model.ActionType != ApplicationStatus.Rejected)
                {
                    return BadRequest(new { success = false, message = "无效的审批操作" });
                }

                // 获取申请记录
                var application = _freeSql.Select<BusApplication>()
                    .Where(a => a.Id == model.ApplicationId && !a.IsDeleted)
                    .First();

                if (application == null)
                {
                    return NotFound();
                }

                if (application.Status != ApplicationStatus.Pending)
                {
                    return BadRequest(new { success = false, message = "该申请已被处理，无法重复审批" });
                }

                // 如果是批准操作，需要再次检查时间冲突
                if (model.ActionType == ApplicationStatus.Approved)
                {
                    var hasConflict = _freeSql.Select<BusApplication>()
                        .Where(a => a.BusId == application.BusId && a.Id != application.Id && !a.IsDeleted)
                        .Where(a => a.Status == ApplicationStatus.Approved)
                        .Where(a => a.StartTime < application.EndTime && a.EndTime > application.StartTime)
                        .Any();

                    if (hasConflict)
                    {
                        return BadRequest(new { success = false, message = "该时段车辆已被其他申请占用，无法批准" });
                    }
                }

                // 更新申请状态
                var updateResult = _freeSql.Update<BusApplication>()
                    .Set(a => a.Status, model.ActionType)
                    .Set(a => a.ReviewerName, "系统管理员") // 这里应该从当前用户获取
                    .Set(a => a.ReviewTime, DateTime.Now)
                    .Set(a => a.ReviewComments, model.Comments)
                    .Set(a => a.UpdateTime, DateTime.Now)
                    .Where(a => a.Id == model.ApplicationId)
                    .ExecuteAffrows();

                if (updateResult > 0)
                {
                    var actionText = model.ActionType == ApplicationStatus.Approved ? "批准" : "拒绝";
                    return Ok(new { success = true, message = $"申请{actionText}成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "审批操作失败" });
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("审批申请失败：" + ex.Message));
            }
        }

        /// <summary>
        /// 取消申请
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Cancel/{id:int}")]
        public IHttpActionResult CancelApplication(int id)
        {
            try
            {
                var application = _freeSql.Select<BusApplication>()
                    .Where(a => a.Id == id && !a.IsDeleted)
                    .First();

                if (application == null)
                {
                    return NotFound();
                }

                if (application.Status != ApplicationStatus.Pending && application.Status != ApplicationStatus.Approved)
                {
                    return BadRequest(new { success = false, message = "当前状态的申请无法取消" });
                }

                // 检查是否已经开始使用
                if (application.Status == ApplicationStatus.Approved && application.StartTime <= DateTime.Now)
                {
                    return BadRequest(new { success = false, message = "申请已开始执行，无法取消" });
                }

                var updateResult = _freeSql.Update<BusApplication>()
                    .Set(a => a.Status, ApplicationStatus.Cancelled)
                    .Set(a => a.UpdateTime, DateTime.Now)
                    .Where(a => a.Id == id)
                    .ExecuteAffrows();

                if (updateResult > 0)
                {
                    return Ok(new { success = true, message = "申请取消成功" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "取消申请失败" });
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("取消申请失败：" + ex.Message));
            }
        }

        /// <summary>
        /// 完成申请（标记为已完成）
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("Complete/{id:int}")]
        public IHttpActionResult CompleteApplication(int id)
        {
            try
            {
                var application = _freeSql.Select<BusApplication>()
                    .Where(a => a.Id == id && !a.IsDeleted)
                    .First();

                if (application == null)
                {
                    return NotFound();
                }

                if (application.Status != ApplicationStatus.Approved)
                {
                    return BadRequest(new { success = false, message = "只有已批准的申请才能标记为完成" });
                }

                if (application.EndTime > DateTime.Now)
                {
                    return BadRequest(new { success = false, message = "申请尚未到结束时间，无法标记为完成" });
                }

                var updateResult = _freeSql.Update<BusApplication>()
                    .Set(a => a.Status, ApplicationStatus.Completed)
                    .Set(a => a.UpdateTime, DateTime.Now)
                    .Where(a => a.Id == id)
                    .ExecuteAffrows();

                if (updateResult > 0)
                {
                    return Ok(new { success = true, message = "申请已标记为完成" });
                }
                else
                {
                    return BadRequest(new { success = false, message = "操作失败" });
                }
            }
            catch (Exception ex)
            {
                return InternalServerError(new Exception("完成申请失败：" + ex.Message));
            }
        }
    }
}
