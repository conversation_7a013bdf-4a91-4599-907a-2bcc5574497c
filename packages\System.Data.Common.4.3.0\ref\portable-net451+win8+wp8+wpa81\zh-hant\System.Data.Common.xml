﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Common</name>
  </assembly>
  <members>
    <member name="T:System.DBNull">
      <summary>表示不存在的值。此類別無法被繼承。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString">
      <summary>傳回空字串 (<see cref="F:System.String.Empty" />)。</summary>
      <returns>空字串 (<see cref="F:System.String.Empty" />)。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString(System.IFormatProvider)">
      <summary>使用指定的 <see cref="T:System.IFormatProvider" /> 來傳回空字串。</summary>
      <returns>空字串 (<see cref="F:System.String.Empty" />)。</returns>
      <param name="provider">用來格式化傳回值的 <see cref="T:System.IFormatProvider" />。-或-null，用來從作業系統的目前地區設定 (Locale) 取得格式資訊。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.DBNull.Value">
      <summary>表示 <see cref="T:System.DBNull" /> 類別的唯一執行個體。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.CommandBehavior">
      <summary>提供查詢結果的描述及其對資料庫的影響。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandBehavior.CloseConnection">
      <summary>當命令執行時，相關聯的 Connection 物件會在相關聯的 DataReader 物件關閉時關閉。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.Default">
      <summary>要求可能傳回多個結果集 (Result Set)。執行查詢可能會影響資料庫狀態。Default 設定為沒有 <see cref="T:System.Data.CommandBehavior" /> 旗標，所以呼叫 ExecuteReader(CommandBehavior.Default) 在功能上相當於呼叫 ExecuteReader()。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.KeyInfo">
      <summary>查詢會傳回資料行和主索引鍵資訊。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SchemaOnly">
      <summary>查詢只會傳回資料行資訊。當使用 <see cref="F:System.Data.CommandBehavior.SchemaOnly" /> 時，.NET Framework Data Provider for SQL Server 會優先於使用 SET FMTONLY ON 執行的陳述式。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SequentialAccess">
      <summary>提供方法來讓 DataReader 使用大型二進位值來處理含有資料行的資料列。SequentialAccess 並不會載入整個資料列，而是啟用 DataReader 來載入資料做為資料流。然後您可以使用 GetBytes 或 GetChars 方法來指定要開始讀取作業的位元組位置和所傳回資料的限制緩衝區大小。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleResult">
      <summary>查詢傳回單一結果集。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleRow">
      <summary>查詢預期會傳回第一個結果集的單一資料列。執行查詢可能會影響資料庫狀態。某些 .NET Framework 資料提供者可以使用這項資訊來最佳化命令的效能，但並不一定需要使用。當您使用 <see cref="T:System.Data.OleDb.OleDbCommand" /> 物件的 <see cref="M:System.Data.OleDb.OleDbCommand.ExecuteReader" /> 方法來指定 <see cref="F:System.Data.CommandBehavior.SingleRow" /> 時，.NET Framework Data Provider for OLE DB 會使用 OLE DB IRow 介面 (如果可用) 來執行繫結。否則，它會使用 IRowset 介面。如果 SQL 陳述式預期只會傳回單一資料列，則指定 <see cref="F:System.Data.CommandBehavior.SingleRow" /> 也可以增進應用程式效能。在執行預期會傳回多個結果集的要求時，可能指定 SingleRow。在這種情況下，也就是同時指定多結果集 SQL 查詢及單一資料列時，傳回的結果將只會包含第一個結果集中的第一個資料列，而不會傳回查詢的其他結果集。</summary>
    </member>
    <member name="T:System.Data.CommandType">
      <summary>指定如何解譯命令字串。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandType.StoredProcedure">
      <summary>預存程序的名稱。</summary>
    </member>
    <member name="F:System.Data.CommandType.TableDirect">
      <summary>資料表的名稱。</summary>
    </member>
    <member name="F:System.Data.CommandType.Text">
      <summary>SQL 文字命令 (預設值)。</summary>
    </member>
    <member name="T:System.Data.ConnectionState">
      <summary>描述連接至資料來源的目前狀態。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ConnectionState.Broken">
      <summary>與資料來源的連接。這只會發生在連接已經開啟之後。這個狀態下的連接可能會先關閉再重新開啟(這個值已保留供後續版本使用)。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Closed">
      <summary>連接關閉。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Connecting">
      <summary>連接物件正連接至資料來源 </summary>
    </member>
    <member name="F:System.Data.ConnectionState.Executing">
      <summary>物件正在執行命令(這個值已保留供後續版本使用)。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Fetching">
      <summary>連接物件正在擷取資料 (這個值已保留供後續版本使用)。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Open">
      <summary>連接開啟。</summary>
    </member>
    <member name="T:System.Data.DbType">
      <summary>指定 .NET Framework 資料提供者 (Data Provider) 的欄位、屬性或 Parameter 物件的資料型別。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.DbType.AnsiString">
      <summary>非 Unicode 字元的可變長度資料流，範圍在 1 和 8,000 字元之間。</summary>
    </member>
    <member name="F:System.Data.DbType.AnsiStringFixedLength">
      <summary>非 Unicode 字元的固定長度資料流。</summary>
    </member>
    <member name="F:System.Data.DbType.Binary">
      <summary>二進位資料的可變長度資料流，範圍在 1 和 8,000 位元組之間。</summary>
    </member>
    <member name="F:System.Data.DbType.Boolean">
      <summary>表示 true 或 false 的布林值的簡單型別。</summary>
    </member>
    <member name="F:System.Data.DbType.Byte">
      <summary>值範圍在 0 到 255 之間的 8 位元不帶正負號整數 (Unsigned Integer)。</summary>
    </member>
    <member name="F:System.Data.DbType.Currency">
      <summary>貨幣值，範圍從 -2 63 (或 -922,337,203,685,477.5808) 到 2 63 -1 (或 +922,337,203,685,477.5807)，正確率為貨幣單位的千分之十。</summary>
    </member>
    <member name="F:System.Data.DbType.Date">
      <summary>代表日期值的型別。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime">
      <summary>表示日期和時間值的型別。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime2">
      <summary>日期和時間資料。日期值範圍是從西元後 1 年 1 月 1 日到西元後 9999 年 12 月31 日。時間值的範圍從 00:00:00 到 23:59:59.9999999，精確度為 100 奈秒。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTimeOffset">
      <summary>具備時區感知功能的日期和時間資料。日期值範圍是從西元後 1 年 1 月 1 日到西元後 9999 年 12 月31 日。時間值的範圍從 00:00:00 到 23:59:59.9999999，精確度為 100 奈秒。時區值範圍從 -14:00 到 +14:00。</summary>
    </member>
    <member name="F:System.Data.DbType.Decimal">
      <summary>簡單型別，表示具有 28-29 個有效位數、從 1.0 x 10 -28 到大約 7.9 x 10 -28 的數值範圍。</summary>
    </member>
    <member name="F:System.Data.DbType.Double">
      <summary>浮點型別，表示具有 15-16 位數精確度、從 5.0 x 10 -324 到大約 1.7 x 10 308 的數值範圍。</summary>
    </member>
    <member name="F:System.Data.DbType.Guid">
      <summary>全域唯一識別項 (或 GUID)。</summary>
    </member>
    <member name="F:System.Data.DbType.Int16">
      <summary>表示帶正負號的 16 位元整數的整數型別，其值介於 -32768 和 32767 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.Int32">
      <summary>表示帶正負號的 32 位元整數的整數型別，其值介於 -2147483648 和 2147483647 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.Int64">
      <summary>表示帶正負號的 64 位元整數的整數型別，其值介於 -9223372036854775808 和 9223372036854775807 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.Object">
      <summary>表示未明確由其他 DbType 值表示的任何參考或實值型別之一般型別。</summary>
    </member>
    <member name="F:System.Data.DbType.SByte">
      <summary>表示帶正負號的 8 位元整數的整數型別，其值介於 -128 和 127 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.Single">
      <summary>浮點型別，表示具有 7 位數精確度、從 1.5 x 10 -45 到大約 3.4 x 10 38 的數值範圍。</summary>
    </member>
    <member name="F:System.Data.DbType.String">
      <summary>表示 Unicode 字元字串的型別。</summary>
    </member>
    <member name="F:System.Data.DbType.StringFixedLength">
      <summary>Unicode 字元的固定長度字串。</summary>
    </member>
    <member name="F:System.Data.DbType.Time">
      <summary>型別，表示 SQL Server DateTime 值。如果您想要使用 SQL Server time 值，請使用 <see cref="F:System.Data.SqlDbType.Time" />。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt16">
      <summary>表示不帶正負號的 16 位元整數的整數型別，其值介於 0 和 65535 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt32">
      <summary>表示不帶正負號的 32 位元整數的整數型別，其值介於 0 和 4294967295 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt64">
      <summary>表示不帶正負號的 64 位元整數的整數型別，其值介於 0 和 18446744073709551615 之間。</summary>
    </member>
    <member name="F:System.Data.DbType.VarNumeric">
      <summary>可變長度數值。</summary>
    </member>
    <member name="F:System.Data.DbType.Xml">
      <summary>XML 文件或片段的剖析表示。</summary>
    </member>
    <member name="T:System.Data.IsolationLevel">
      <summary>指定連接的異動鎖定行為。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.IsolationLevel.Chaos">
      <summary>無法覆寫來自隔離程度更深之異動的暫止變更。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadCommitted">
      <summary>當正在讀取資料來避免 Dirty 讀取時，會使用共用鎖定，但是在異動結束之前，資料可以變更，這將會產生非重複讀取或虛設資料。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadUncommitted">
      <summary>可以進行 Dirty 讀取，這表示未發出共用鎖定，並且沒有生效的獨佔鎖定。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.RepeatableRead">
      <summary>鎖定是加諸於查詢中使用的所有資料，以防止其他使用者更新資料。防止非重複讀取，但是仍然可能造成虛設資料列。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Serializable">
      <summary>範圍鎖定會置於 <see cref="T:System.Data.DataSet" /> 上，以免其他使用者在異動完成前將資料列更新或插入至資料集中。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Snapshot">
      <summary>在其他應用程式正在修改相同資料時，儲存應用程式可以讀取的資料版本，藉此減少封鎖。指出即使重新查詢，您也無法從某個異動看到在其他異動中所產生的變更。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Unspecified">
      <summary>使用與指定不同的隔離等級，但無法判斷該等級。</summary>
    </member>
    <member name="T:System.Data.ParameterDirection">
      <summary>指定與 <see cref="T:System.Data.DataSet" /> 相關查詢中的參數型別。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ParameterDirection.Input">
      <summary>參數為輸入參數。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.InputOutput">
      <summary>參數能夠輸入和輸出。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.Output">
      <summary>參數為輸出參數。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.ReturnValue">
      <summary>參數表示來自預存程序、內建功能或使用者定義函式等等作業的傳回值。</summary>
    </member>
    <member name="T:System.Data.StateChangeEventArgs">
      <summary>為 .NET Framework 資料提供者 (Data Provider) 的狀態變更事件提供資料。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.StateChangeEventArgs.#ctor(System.Data.ConnectionState,System.Data.ConnectionState)">
      <summary>在提供物件的原始狀態和目前狀態時，初始化 <see cref="T:System.Data.StateChangeEventArgs" /> 類別的新執行個體。</summary>
      <param name="originalState">其中一個 <see cref="T:System.Data.ConnectionState" /> 值。</param>
      <param name="currentState">其中一個 <see cref="T:System.Data.ConnectionState" /> 值。</param>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.CurrentState">
      <summary>取得連線的新狀態。當引發事件時，連接物件將會處於新的狀態中。</summary>
      <returns>其中一個 <see cref="T:System.Data.ConnectionState" /> 值。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.OriginalState">
      <summary>取得連線的原始狀態。</summary>
      <returns>其中一個 <see cref="T:System.Data.ConnectionState" /> 值。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.StateChangeEventHandler">
      <summary>表示處理 <see cref="E:System.Data.Common.DbConnection.StateChange" /> 事件的方法。</summary>
      <param name="sender">事件的來源。</param>
      <param name="e">包含事件資料的 <see cref="T:System.Data.StateChangeEventArgs" />。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.UpdateRowSource">
      <summary>指定如何將查詢命令結果套用到正在更新的資料列。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.UpdateRowSource.Both">
      <summary>輸出參數和第一個傳回的資料列都對應至 <see cref="T:System.Data.DataSet" /> 中已變更的資料列。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.FirstReturnedRecord">
      <summary>第一個傳回的資料列中的資料是對應至 <see cref="T:System.Data.DataSet" /> 中已經變更的資料列。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.None">
      <summary>會忽略任何傳回的參數或資料列。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.OutputParameters">
      <summary>輸出參數是對應至 <see cref="T:System.Data.DataSet" /> 中已經變更的資料列。</summary>
    </member>
    <member name="T:System.Data.Common.DbCommand">
      <summary>表示要針對資料來源執行的 SQL 陳述式或預存程序。為表示命令的資料庫特定類別，提供基底類別。<see cref="Overload:System.Data.Common.DbCommand.ExecuteNonQueryAsync" /></summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.#ctor">
      <summary>建構 <see cref="T:System.Data.Common.DbCommand" /> 物件的執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbCommand.Cancel">
      <summary>嘗試取消 <see cref="T:System.Data.Common.DbCommand" /> 的執行。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandText">
      <summary>取得或設定要針對資料來源執行的文字命令。</summary>
      <returns>要執行的文字命令。預設為空字串 ("")。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandTimeout">
      <summary>取得或設定結束執行命令的嘗試並產生錯誤之前的等待時間。</summary>
      <returns>等待命令執行的時間 (以秒為單位)。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandType">
      <summary>指示或指定解譯 <see cref="P:System.Data.Common.DbCommand.CommandText" /> 屬性的方式。</summary>
      <returns>其中一個 <see cref="T:System.Data.CommandType" /> 值。預設值為 Text。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Connection">
      <summary>取得或設定由這個 <see cref="T:System.Data.Common.DbCommand" /> 使用的 <see cref="T:System.Data.Common.DbConnection" />。</summary>
      <returns>與資料來源的連接。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateDbParameter">
      <summary>建立 <see cref="T:System.Data.Common.DbParameter" /> 物件的新執行個體。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 物件。</returns>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateParameter">
      <summary>建立 <see cref="T:System.Data.Common.DbParameter" /> 物件的新執行個體。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbConnection">
      <summary>取得或設定由這個 <see cref="T:System.Data.Common.DbCommand" /> 使用的 <see cref="T:System.Data.Common.DbConnection" />。</summary>
      <returns>與資料來源的連接。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbParameterCollection">
      <summary>取得 <see cref="T:System.Data.Common.DbParameter" /> 物件的集合。</summary>
      <returns>SQL 陳述式或預存程序的參數。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbTransaction">
      <summary>取得或設定 <see cref="P:System.Data.Common.DbCommand.DbTransaction" />，<see cref="T:System.Data.Common.DbCommand" /> 物件將會在其中執行。</summary>
      <returns>交易，.NET Framework 資料提供者的 Command 物件將會在其中執行。預設值為 null 參考 (在 Visual Basic 為 Nothing)。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DesignTimeVisible">
      <summary>取得或設定值，指出命令物件是否應該在自訂介面控制項中顯示。</summary>
      <returns>如果命令物件應該在控制項中顯示，則為 true，否則為 false。預設值為 true。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Dispose"></member>
    <member name="M:System.Data.Common.DbCommand.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
      <summary>針對連接執行命令文字。</summary>
      <returns>表示作業的工作。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 的執行個體。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>提供者應該實作這個方法，以提供 <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> 多載的非預設實作。預設實作會叫用同步<see cref="M:System.Data.Common.DbCommand.ExecuteReader" />方法，並傳回完成的工作，封鎖呼叫的執行緒。如果傳遞一個已經被取消的取消語彙基元，預設實作會傳回已取消的工作。ExecuteReader 擲回的例外狀況會透過傳回的 Task Exception 屬性來傳送。這個方法會接受可以用來要求提早取消作業的取消語彙基元。實作可以忽略這項要求。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="behavior">陳述式執行和資料擷取的選項。</param>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQuery">
      <summary>針對連接物件執行 SQL 陳述式。</summary>
      <returns>受影響的資料列數目。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync">
      <summary>非同步版本的 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />，這個版本會對連接物件執行 SQL 陳述式。以 CancellationToken.None 叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>這是 <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> 的非同步版本。提供者應該覆寫為適當的實作。可以選擇性地接受忽略語彙基元。預設實作會叫用同步<see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />方法，並傳回完成的工作，封鎖呼叫的執行緒。如果傳遞一個已經被取消的取消語彙基元，預設實作會傳回已取消的工作。<see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> 擲回的例外狀況會透過傳回的 Task Exception 屬性溝通。傳回的工作完成之前，不叫用 DbCommand 物件的其他方法和屬性。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader">
      <summary>針對 <see cref="P:System.Data.Common.DbCommand.Connection" /> 執行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，並傳回 <see cref="T:System.Data.Common.DbDataReader" />。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>針對 <see cref="P:System.Data.Common.DbCommand.Connection" /> 執行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，並使用其中一個 <see cref="T:System.Data.CommandBehavior" /> 值來建置 <see cref="T:System.Data.Common.DbDataReader" />。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 物件。</returns>
      <param name="behavior">其中一個 <see cref="T:System.Data.CommandBehavior" /> 值。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync">
      <summary>非同步版本的 <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />，該版本會針對 <see cref="P:System.Data.Common.DbCommand.Connection" /> 執行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，並傳回 <see cref="T:System.Data.Common.DbDataReader" />。以 CancellationToken.None 叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>非同步版本的 <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />，該版本會針對 <see cref="P:System.Data.Common.DbCommand.Connection" /> 執行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，並傳回 <see cref="T:System.Data.Common.DbDataReader" />。叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="behavior">其中一個 <see cref="T:System.Data.CommandBehavior" /> 值。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="behavior">其中一個 <see cref="T:System.Data.CommandBehavior" /> 值。</param>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>非同步版本的 <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />，該版本會針對 <see cref="P:System.Data.Common.DbCommand.Connection" /> 執行 <see cref="P:System.Data.Common.DbCommand.CommandText" />，並傳回 <see cref="T:System.Data.Common.DbDataReader" />。此方法會散佈通知，表示不應取消作業。叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
      <exception cref="T:System.ArgumentException">無效的 <see cref="T:System.Data.CommandBehavior" /> 值。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalar">
      <summary>執行查詢，並傳回查詢所傳回的結果集中第一個資料列的第一個資料行。會忽略所有其他的資料行和資料列。</summary>
      <returns>結果集中第一個資料列的第一個資料行。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync">
      <summary>
        <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 的非同步版本，該版本會執行查詢並傳回查詢所傳回的結果集中第一個資料列的第一個資料行。會忽略所有其他的資料行和資料列。以 CancellationToken.None 叫用 <see cref="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>這是 <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 的非同步版本。提供者應該覆寫為適當的實作。可以選擇性地接受忽略語彙基元。預設實作會叫用同步<see cref="M:System.Data.Common.DbCommand.ExecuteScalar" />方法，並傳回完成的工作，封鎖呼叫的執行緒。如果傳遞一個已經被取消的取消語彙基元，預設實作會傳回已取消的工作。ExecuteScalar 擲回的例外狀況會透過傳回的工作例外狀況屬性來傳送。傳回的工作完成之前，不叫用 DbCommand 物件的其他方法和屬性。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">用來監視是否有取消要求的語彙基元。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="P:System.Data.Common.DbCommand.Parameters">
      <summary>取得 <see cref="T:System.Data.Common.DbParameter" /> 物件的集合。如需參數的詳細資訊，請參閱設定參數和參數資料型別。</summary>
      <returns>SQL 陳述式或預存程序的參數。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Prepare">
      <summary>會在資料來源上建立命令的備製 (或已編譯的) 版本。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Transaction">
      <summary>取得或設定 <see cref="T:System.Data.Common.DbTransaction" />，<see cref="T:System.Data.Common.DbCommand" /> 物件將會在其中執行。</summary>
      <returns>交易，.NET Framework 資料提供者的 Command 物件將會在其中執行。預設值為 null 參考 (在 Visual Basic 為 Nothing)。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.UpdatedRowSource">
      <summary>取得或設定當由 <see cref="T:System.Data.Common.DbDataAdapter" /> 的 Update 方法使用命令結果時，如何套用至 <see cref="T:System.Data.DataRow" />。</summary>
      <returns>其中一個 <see cref="T:System.Data.UpdateRowSource" /> 值。預設值為 Both，除非命令是自動產生的。否則預設值為 None。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbConnection">
      <summary>表示資料庫的連接。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbConnection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
      <summary>開始資料庫交易。</summary>
      <returns>表示新交易的物件。</returns>
      <param name="isolationLevel">指定交易的隔離等級。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction">
      <summary>開始資料庫交易。</summary>
      <returns>表示新交易的物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>使用指定的隔離等級開始資料庫交易。</summary>
      <returns>表示新交易的物件。</returns>
      <param name="isolationLevel">指定交易的隔離等級。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.ChangeDatabase(System.String)">
      <summary>為開啟的連接變更目前的資料庫。</summary>
      <param name="databaseName">指定連接所要使用的資料庫名稱。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Close">
      <summary>關閉對資料庫的連接。這是關閉任何開啟連接的慣用方法。</summary>
      <exception cref="T:System.Data.Common.DbException">當開啟連接時發生的連接層級錯誤。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionString">
      <summary>取得或設定用來開啟連接的字串。</summary>
      <returns>用來建立初始連接的連接字串。連接字串的確切內容取決於這個連接的特定資料來源。預設值為空字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionTimeout">
      <summary>取得在終止嘗試並產生錯誤前建立連接的等待時間。</summary>
      <returns>等待連接開啟的時間 (單位為秒)。預設值是由您使用的連接特定型別所決定。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateCommand">
      <summary>建立並傳回與目前連接相關聯的 <see cref="T:System.Data.Common.DbCommand" /> 物件。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateDbCommand">
      <summary>建立並傳回與目前連接相關聯的 <see cref="T:System.Data.Common.DbCommand" /> 物件。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 物件。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnection.Database">
      <summary>取得連接開啟後的目前資料庫名稱，或連接開啟前連接字串中所指定的資料庫名稱。</summary>
      <returns>目前資料庫的名稱或連接開啟後要使用之資料庫的名稱。預設值為空字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.DataSource">
      <summary>取得要連接的資料庫伺服器名稱。</summary>
      <returns>要連接的資料庫伺服器名稱。預設值為空字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Dispose"></member>
    <member name="M:System.Data.Common.DbConnection.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbConnection.OnStateChange(System.Data.StateChangeEventArgs)">
      <summary>引發 <see cref="E:System.Data.Common.DbConnection.StateChange" /> 事件。</summary>
      <param name="stateChange">包含事件資料的 <see cref="T:System.Data.StateChangeEventArgs" />。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.Open">
      <summary>使用 <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> 所指定的設定，開啟資料庫連接。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync">
      <summary>非同步版本的 <see cref="M:System.Data.Common.DbConnection.Open" />，這個版本會透過 <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> 所指定的設定開啟資料庫連接。這個方法會叫用虛擬方法 <see cref="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)" /> 與 CancellationToken.None。</summary>
      <returns>表示非同步作業的工作。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>這是 <see cref="M:System.Data.Common.DbConnection.Open" /> 的非同步版本。提供者應該覆寫為適當的實作。可以選擇性地接受取消語彙基元。預設實作會叫用同步<see cref="M:System.Data.Common.DbConnection.Open" />呼叫並傳回完成的工作。如果傳遞一個已經被取消的 cancellationToken，預設實作會傳回已取消的工作。Open 擲回的例外狀況會透過傳回的工作例外狀況屬性來傳送。傳回的工作完成之前，不叫用 DbConnection 物件的其他方法和屬性。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">取消指令。</param>
    </member>
    <member name="P:System.Data.Common.DbConnection.ServerVersion">
      <summary>取得字串，表示物件連接之伺服器的版本。</summary>
      <returns>資料庫的版本。傳回的字串格式取決於您所使用的連接特定型別。</returns>
      <exception cref="T:System.InvalidOperationException"> 當傳回的工作未完成，而且連線在呼叫 <see cref="Overload:System.Data.Common.DbConnection.OpenAsync" /> 後並未開啟時呼叫了 <see cref="P:System.Data.Common.DbConnection.ServerVersion" />。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.State">
      <summary>取得字串，描述連接的狀態。</summary>
      <returns>連接的狀態。傳回的字串格式取決於您所使用的連接特定型別。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="E:System.Data.Common.DbConnection.StateChange">
      <summary>當事件的狀態變更時發生。</summary>
    </member>
    <member name="T:System.Data.Common.DbConnectionStringBuilder">
      <summary>提供強型別連接字串產生器的基底類別。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Add(System.String,System.Object)">
      <summary>將有指定索引鍵和數值的項目加入 <see cref="T:System.Data.Common.DbConnectionStringBuilder" />。</summary>
      <param name="keyword">要加入至 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 的索引鍵。</param>
      <param name="value">指定之索引鍵的值。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 為 null 參考 (在 Visual Basic 中為 Nothing)。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是唯讀的。-或-<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定的大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.AppendKeyValuePair(System.Text.StringBuilder,System.String,System.String)">
      <summary>提供有效而安全的方法，將索引鍵和值附加至現有的 <see cref="T:System.Text.StringBuilder" /> 物件。</summary>
      <param name="builder">
        <see cref="T:System.Text.StringBuilder" />，要將索引鍵/值組加入其中。</param>
      <param name="keyword">要加入的索引鍵。</param>
      <param name="value">提供之索引鍵的值。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Clear">
      <summary>清除 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 執行個體的內容。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是唯讀的。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString">
      <summary>取得或設定與 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 關聯的連接字串。</summary>
      <returns>目前的連接字串是從包含在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 之中的索引鍵/值組建立。預設值為空字串。</returns>
      <exception cref="T:System.ArgumentException">提供無效的連接字串引數。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ContainsKey(System.String)">
      <summary>判斷 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是否包含特定索引鍵。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 包含的項目具有指定索引鍵，則為 true，否則為 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中尋找的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 為 null 參考 (在 Visual Basic 中為 Nothing)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Count">
      <summary>取得目前包含在 <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> 屬性之內的索引鍵數目。</summary>
      <returns>索引鍵的數目，這些索引鍵包含在由 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 執行個體所維護的連接字串之內。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.EquivalentTo(System.Data.Common.DbConnectionStringBuilder)">
      <summary>比較此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 物件中的連接資訊與所提供之物件中的連接資訊。</summary>
      <returns>如果兩個 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 物件中的連接資訊產生相等的連接字串，則為 true，否則為 false。</returns>
      <param name="connectionStringBuilder">要與這個 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 物件相比較的 <see cref="T:System.Data.Common.DbConnectionStringBuilder" />。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Item(System.String)">
      <summary>取得或設定和指定索引鍵關聯的值。</summary>
      <returns>與指定索引鍵關聯的值。如果找不到指定的索引鍵，嘗試取得索引鍵將會傳回 null 參考 (在 Visual Basic 中為 Nothing)，而嘗試設定索引鍵則會使用指定的索引鍵建立新項目。傳遞 null (在 Visual Basic 中為 Nothing) 索引鍵將會擲回 <see cref="T:System.ArgumentNullException" />。指定 null 值將會移除索引鍵/值組。</returns>
      <param name="keyword">要取得或設定的項目索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 為 null 參考 (在 Visual Basic 中為 Nothing)。</exception>
      <exception cref="T:System.NotSupportedException">屬性已設定，而且 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是唯讀的。-或-屬性已設定，<paramref name="keyword" /> 不存在於集合中，而且 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Keys">
      <summary>取得在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中含有索引鍵的 <see cref="T:System.Collections.ICollection" />。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中包含索引鍵。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Remove(System.String)">
      <summary>將具有指定索引鍵的項目從 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 執行個體移除。</summary>
      <returns>如果索引鍵存在於連接字串內且已移除，則為 true，否則為 false。</returns>
      <param name="keyword">要從這個 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 的連接字串中移除之索引鍵/值組的索引鍵。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 為 null (在 Visual Basic 中為 Nothing)。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 是唯讀，或 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 具有固定大小。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>表示指定的索引鍵是否存在這個 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 執行個體中。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 包含的項目具有指定索引鍵，則為 true，否則為 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中尋找的索引鍵。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>從特定的 <see cref="T:System.Array" /> 索引開始，複製 <see cref="T:System.Collections.ICollection" /> 項目至 <see cref="T:System.Array" />。</summary>
      <param name="array">一維 <see cref="T:System.Array" />，是從 <see cref="T:System.Collections.ICollection" /> 複製過來之項目的目的端。<see cref="T:System.Array" /> 必須有以零起始的索引。</param>
      <param name="index">
        <paramref name="array" /> 中以零起始的索引，是複製開始的位置。</param>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#IsSynchronized">
      <summary>取得值，這個值表示對 <see cref="T:System.Collections.ICollection" /> 的存取是否同步 (安全執行緒)。</summary>
      <returns>如果對 <see cref="T:System.Collections.ICollection" /> 的存取為同步 (安全執行緒)，則為 true，否則為 false。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#SyncRoot">
      <summary>取得可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</summary>
      <returns>可用來同步存取 <see cref="T:System.Collections.ICollection" /> 的物件。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>將隨附有索引鍵和值的項目加入至 <see cref="T:System.Collections.IDictionary" /> 物件。</summary>
      <param name="keyword">
        <see cref="T:System.Object" />，用做要加入之項目的索引鍵。</param>
      <param name="value">
        <see cref="T:System.Object" />，用做要加入之項目的值。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>判斷 <see cref="T:System.Collections.IDictionary" /> 物件是否包含具有指定索引鍵的項目。</summary>
      <returns>如果 <see cref="T:System.Collections.IDictionary" /> 包含具有該索引鍵的元素，則為 true，否則為 false。</returns>
      <param name="keyword">要在 <see cref="T:System.Collections.IDictionary" /> 物件中尋找的索引鍵。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.IDictionary" /> 物件的 <see cref="T:System.Collections.IDictionaryEnumerator" /> 物件。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> 物件的 <see cref="T:System.Collections.IDictionaryEnumerator" /> 物件。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Item(System.Object)">
      <summary>取得或設定具有指定機碼的項目。</summary>
      <returns>具有指定之索引鍵的項目。</returns>
      <param name="keyword">要取得或設定之項目的索引鍵。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>將有指定索引鍵的項目從 <see cref="T:System.Collections.IDictionary" /> 物件移除。</summary>
      <param name="keyword">要移除之項目的名稱。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IEnumerable#GetEnumerator">
      <summary>傳回會逐一查看集合的列舉程式。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" /> 物件，用於逐一查看集合。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ToString">
      <summary>傳回與此 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 相關聯的連接字串。</summary>
      <returns>目前的 <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> 屬性。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>擷取對應於來自這個 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 的提供之索引鍵的值。</summary>
      <returns>如果在連接字串中找到 <paramref name="keyword" />，則為 true，否則為 false。</returns>
      <param name="keyword">要擷取的項目索引鍵。</param>
      <param name="value">對應於 <paramref name="key" /> 的值</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> 包含 null 值 (在 Visual Basic 中為 Nothing)。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Values">
      <summary>取得 <see cref="T:System.Collections.ICollection" />，此集合包含 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中的值。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />，在 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 中包含值。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="T:System.Data.Common.DbDataReader">
      <summary>從資料來源讀取資料列的單向捲動檢視 (Forward-only) 資料流。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbDataReader" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Depth">
      <summary>取得值，表示目前資料列的巢狀深度。</summary>
      <returns>目前資料列的巢狀深度。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose">
      <summary>將 <see cref="T:System.Data.Common.DbDataReader" /> 類別目前的執行個體所使用的資源全部釋出。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Data.Common.DbDataReader" /> 使用的 Managed 資源，並選擇性釋放 Unmanaged 資源。</summary>
      <param name="disposing">true 表示釋放 Managed 和 Unmanaged 資源，false 表示只釋放 Unmanaged 資源。</param>
    </member>
    <member name="P:System.Data.Common.DbDataReader.FieldCount">
      <summary>取得目前資料列中的資料行數目。</summary>
      <returns>目前資料列中的資料行數目。</returns>
      <exception cref="T:System.NotSupportedException">目前沒有連接至 SQL Server 的執行個體。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBoolean(System.Int32)">
      <summary>取得指定的資料行值做為布林值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetByte(System.Int32)">
      <summary>取得指定資料行的值做為位元組。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>從指定的資料行 (開始於 <paramref name="dataOffset" /> 指定的位置) 將位元組的資料流讀入緩衝區 (開始於 <paramref name="bufferOffset" /> 指定的位置)。</summary>
      <returns>讀取的實際位元組數目。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <param name="dataOffset">要開始讀取作業之資料列中的來源索引。</param>
      <param name="buffer">要複製資料的目的緩衝區。</param>
      <param name="bufferOffset">要將資料複製到其中之緩衝區的索引。</param>
      <param name="length">要讀取的最大字元數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChar(System.Int32)">
      <summary>取得指定資料行的值做為字元。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>從指定的資料行 (開始於 <paramref name="dataOffset" /> 指定的位置) 將字元的資料流讀入緩衝區 (開始於 <paramref name="bufferOffset" /> 指定的位置)。</summary>
      <returns>讀取的實際字元數目。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <param name="dataOffset">要開始讀取作業之資料列中的來源索引。</param>
      <param name="buffer">要複製資料的目的緩衝區。</param>
      <param name="bufferOffset">要將資料複製到其中之緩衝區的索引。</param>
      <param name="length">要讀取的最大字元數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetData(System.Int32)">
      <summary>傳回要求之資料行序數的 <see cref="T:System.Data.Common.DbDataReader" /> 物件。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 物件。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDataTypeName(System.Int32)">
      <summary>取得指定之資料行的資料型別名稱。</summary>
      <returns>字串，表示資料型別的名稱。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDateTime(System.Int32)">
      <summary>取得指定之資料行的值做為 <see cref="T:System.DateTime" /> 物件。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDbDataReader(System.Int32)">
      <summary>傳回要求之資料行序數的 <see cref="T:System.Data.Common.DbDataReader" /> 物件，可以使用提供者特定的實作覆寫此序數。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> 物件。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDecimal(System.Int32)">
      <summary>取得指定之資料行的值做為 <see cref="T:System.Decimal" /> 物件。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDouble(System.Int32)">
      <summary>取得指定資料行的值做為雙精確度浮點數。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetEnumerator">
      <summary>傳回 <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看資料讀取器中的資料列。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看資料讀取器中的資料列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldType(System.Int32)">
      <summary>取得指定之資料行的資料型別。</summary>
      <returns>指定之資料行的資料型別。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValue``1(System.Int32)">
      <summary>同步取得指定之資料行的值做為型別。</summary>
      <returns>要擷取的資料行。</returns>
      <param name="ordinal">要擷取的資料行。</param>
      <typeparam name="T">同步取得指定之資料行的值做為型別。</typeparam>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.SqlClient.SqlDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" />不符合 SQL Server 傳回的型別或無法轉換。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)">
      <summary>非同步取得指定之資料行的值做為型別。</summary>
      <returns>要傳回的值型别。</returns>
      <param name="ordinal">要傳回的值型别。</param>
      <typeparam name="T">要傳回的值型别。如需詳細資訊，請參閱「備註」一節。</typeparam>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> 不符合資料來源傳回的類型或無法轉換。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>非同步取得指定之資料行的值做為型別。</summary>
      <returns>要傳回的值型别。</returns>
      <param name="ordinal">要傳回的值型别。</param>
      <param name="cancellationToken">取消指令，其會傳播說明應該取消作業的通知。這並不保證取消。如果設定為 CancellationToken.None，就會使這個方法相當於 <see cref="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)" />。傳回的工作必須標記為已取消。</param>
      <typeparam name="T">要傳回的值型别。如需詳細資訊，請參閱「備註」一節。</typeparam>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" />不符合資料來源傳回的型別或無法轉換。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFloat(System.Int32)">
      <summary>取得指定資料行的值做為單精確度浮點數。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetGuid(System.Int32)">
      <summary>取得指定之資料行的值做為全域唯一識別項 (GUID)。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt16(System.Int32)">
      <summary>取得指定資料行的值做為 16 位元帶正負號的整數。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt32(System.Int32)">
      <summary>取得指定資料行的值做為 32 位元帶正負號的整數。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt64(System.Int32)">
      <summary>取得指定資料行的值做為 64 位元帶正負號的整數。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetName(System.Int32)">
      <summary>在提供以零起始之資料行序數的情況下，取得資料行的名稱。</summary>
      <returns>指定資料行的名稱。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetOrdinal(System.String)">
      <summary>提供資料行的名稱，取得資料行序數。</summary>
      <returns>以零起始的資料行序數。</returns>
      <param name="name">資料行名稱。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的名稱不是有效的資料行名稱。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>傳回指定之資料行的提供者特定欄位型別。</summary>
      <returns>
        <see cref="T:System.Type" /> 物件，描述指定之資料行的資料型別。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>取得當做 <see cref="T:System.Object" /> 執行個體的指定之資料行值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>取得集合中目前資料列的所有提供者特定的屬性資料行。</summary>
      <returns>陣列中 <see cref="T:System.Object" /> 的執行個體數目。</returns>
      <param name="values">屬性資料行要複製到其中的 <see cref="T:System.Object" /> 陣列。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetStream(System.Int32)">
      <summary>擷取資料做為<see cref="T:System.IO.Stream" />。</summary>
      <returns>已傳回的物件。</returns>
      <param name="ordinal">擷取資料做為<see cref="T:System.IO.Stream" />。</param>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
      <exception cref="T:System.InvalidCastException">傳回的型別不是下列型別：binaryimagevarbinaryUDT</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetString(System.Int32)">
      <summary>取得當做 <see cref="T:System.String" /> 執行個體的指定之資料行值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.InvalidCastException">指定的轉型無效。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)">
      <summary>擷取資料做為<see cref="T:System.IO.TextReader" />。</summary>
      <returns>已傳回的物件。</returns>
      <param name="ordinal">擷取資料做為<see cref="T:System.IO.TextReader" />。</param>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
      <exception cref="T:System.InvalidCastException">傳回的型別不是下列型別：charncharntextnvarchar文字varchar</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValue(System.Int32)">
      <summary>取得當做 <see cref="T:System.Object" /> 執行個體的指定之資料行值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValues(System.Object[])">
      <summary>使用目前資料列的資料行值填入物件陣列。</summary>
      <returns>陣列中 <see cref="T:System.Object" /> 的執行個體數目。</returns>
      <param name="values">屬性資料行要複製到其中的 <see cref="T:System.Object" /> 陣列。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.HasRows">
      <summary>取得值，指出 <see cref="T:System.Data.Common.DbDataReader" /> 是否包含一個或多個資料列。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbDataReader" /> 包含一個或更多資料列，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.IsClosed">
      <summary>取得值，指出 <see cref="T:System.Data.Common.DbDataReader" /> 是否關閉。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbDataReader" /> 關閉，則為 true，否則為 false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Data.SqlClient.SqlDataReader" /> 已經關閉。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)">
      <summary>取得值，指出資料行是否包含不存在或遺漏的值。</summary>
      <returns>如果指定的資料行等於 <see cref="T:System.DBNull" />，則為 true，否則為 false。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> 的非同步版本，該版本會取得值，表示資料行是否包含不存在或遺漏的值。</summary>
      <returns>如果指定的資料行值等於 DBNull，則為 true，否則為 false。</returns>
      <param name="ordinal">要擷取的以零起始的資料行。</param>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> 的非同步版本，該版本會取得值，表示資料行是否包含不存在或遺漏的值。選擇性地傳送通知，表示應取消作業。</summary>
      <returns>如果指定的資料行值等於 DBNull，則為 true，否則為 false。</returns>
      <param name="ordinal">要擷取的以零起始的資料行。</param>
      <param name="cancellationToken">取消指令，其會傳播說明應該取消作業的通知。這並不保證取消。如果設定為 CancellationToken.None，就會使這個方法相當於 <see cref="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)" />。傳回的工作必須標記為已取消。</param>
      <exception cref="T:System.InvalidOperationException">在資料擷取期間，連線中斷或關閉。<see cref="T:System.Data.Common.DbDataReader" />在資料擷取期間已關閉。沒有可供讀取的資料 (例如，第一個 <see cref="M:System.Data.Common.DbDataReader.Read" /> 沒有被呼叫，或傳回 false)。嘗試在循序模式下讀取先前讀取的資料行。有進行中的非同步作業。當在循序模式中執行時，此適用於所有 Get* 方法，因為在讀取資料流時可能會呼叫它們。</exception>
      <exception cref="T:System.IndexOutOfRangeException">嘗試讀取不存在的資料行。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.Int32)">
      <summary>取得當做 <see cref="T:System.Object" /> 執行個體的指定之資料行值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="ordinal">以零起始的資料行序數。</param>
      <exception cref="T:System.IndexOutOfRangeException">傳遞的索引超出 0 到 <see cref="P:System.Data.IDataRecord.FieldCount" /> 的範圍。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.String)">
      <summary>取得當做 <see cref="T:System.Object" /> 執行個體的指定之資料行值。</summary>
      <returns>指定的資料行值。</returns>
      <param name="name">資料行名稱。</param>
      <exception cref="T:System.IndexOutOfRangeException">找不到具有指定名稱的資料行。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResult">
      <summary>讀取批次陳述式的結果時，將讀取器前移至下一個結果。</summary>
      <returns>如果有其他的結果集 (Result Set)，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync">
      <summary>
        <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 的非同步版本，該版本會在讀取批次陳述式的結果時，將讀取器前移至下一個結果。以 CancellationToken.None 叫用 <see cref="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)" />。</summary>
      <returns>表示非同步作業的工作。</returns>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>這是 <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 的非同步版本。提供者應該覆寫為適當的實作。可以選擇性忽略 <paramref name="cancellationToken" />。預設實作會叫用同步<see cref="M:System.Data.Common.DbDataReader.NextResult" />方法，並傳回完成的工作，封鎖呼叫的執行緒。如果傳遞一個已經被取消的 <paramref name="cancellationToken" />，預設實作會傳回已取消的工作。<see cref="M:System.Data.Common.DbDataReader.NextResult" /> 擲回的例外狀況會透過傳回的工作例外狀況屬性來傳送。傳回的工作尚未完成時，不應該叫用 DbDataReader 物件的其他方法和屬性。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">取消指令。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Read">
      <summary>將讀取器前移至結果集的下一個資料錄。</summary>
      <returns>如果有多個資料列，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync">
      <summary>非同步版本的 <see cref="M:System.Data.Common.DbDataReader.Read" />，這個版本會將讀取器前移至結果集中的下一筆記錄。這個方法會叫用 <see cref="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)" /> 與 CancellationToken.None。</summary>
      <returns>表示非同步作業的工作。</returns>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>這是 <see cref="M:System.Data.Common.DbDataReader.Read" /> 的非同步版本。提供者應該覆寫為適當的實作。可以選擇性地忽略 cancellationToken。預設實作會叫用同步<see cref="M:System.Data.Common.DbDataReader.Read" />方法，並傳回完成的工作，封鎖呼叫的執行緒。如果傳遞一個已經被取消的 cancellationToken，預設實作會傳回已取消的工作。Read 擲回的例外狀況會透過傳回的工作例外狀況屬性來傳送。在傳回的工作完成之前，不叫用 DbDataReader 物件的其他方法及屬性。</summary>
      <returns>表示非同步作業的工作。</returns>
      <param name="cancellationToken">取消指令。</param>
      <exception cref="T:System.Data.Common.DbException">執行命令文字時發生錯誤。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.RecordsAffected">
      <summary>取得 SQL 陳述式 (Statement) 的執行所變更、插入或刪除的資料列數目。</summary>
      <returns>已變更、已插入或已刪除資料列的數目。SELECT 陳述式的值為 -1，如果沒有任何資料列受到影響，或是陳述式失敗，則為 0。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.VisibleFieldCount">
      <summary>取得 <see cref="T:System.Data.Common.DbDataReader" /> 中未隱藏的欄位數目。</summary>
      <returns>未隱藏的欄位數目。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbException">
      <summary>代表資料來源擲回之所有例外狀況的基底類別。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbException" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String)">
      <summary>使用指定的錯誤訊息，初始化 <see cref="T:System.Data.Common.DbException" /> 類別的新執行個體。</summary>
      <param name="message">顯示這個例外狀況的訊息。</param>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String,System.Exception)">
      <summary>使用指定的錯誤訊息和造成這個例外狀況原因的內部例外狀況參考，初始化 <see cref="T:System.Data.Common.DbException" /> 類別的新執行個體。</summary>
      <param name="message">錯誤訊息字串。</param>
      <param name="innerException">內部例外狀況參考。</param>
    </member>
    <member name="T:System.Data.Common.DbParameter">
      <summary>表示 <see cref="T:System.Data.Common.DbCommand" /> 的參數及其對 <see cref="T:System.Data.DataSet" /> 資料行的對應 (選擇性)。如需參數的詳細資訊，請參閱設定參數和參數資料型別。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameter.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbParameter" /> 類別的新執行個體。</summary>
    </member>
    <member name="P:System.Data.Common.DbParameter.DbType">
      <summary>取得或設定參數的 <see cref="T:System.Data.DbType" />。</summary>
      <returns>其中一個 <see cref="T:System.Data.DbType" /> 值。預設為 <see cref="F:System.Data.DbType.String" />。</returns>
      <exception cref="T:System.ArgumentException">屬性未設為有效的 <see cref="T:System.Data.DbType" />。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Direction">
      <summary>取得或設定值，指出參數是否為只能輸入、只能輸出、雙向 (Bidirectional) 或預存程序 (Stored Procedure) 傳回值參數。</summary>
      <returns>其中一個 <see cref="T:System.Data.ParameterDirection" /> 值。預設為 Input。</returns>
      <exception cref="T:System.ArgumentException">屬性未設為其中一個有效的 <see cref="T:System.Data.ParameterDirection" /> 值。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.IsNullable">
      <summary>取得或設定值，指出參數是否接受 null 值。</summary>
      <returns>如果接受 null 值，則為 true，否則為 false。預設為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.ParameterName">
      <summary>取得或設定 <see cref="T:System.Data.Common.DbParameter" /> 的名稱。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 的名稱。預設為空字串 ("")。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Precision">
      <summary>[在 .NET Framework 4.5.1 或更新版本中支援]取得或設定用來表示 <see cref="P:System.Data.Common.DbParameter.Value" /> 屬性的最大位數。</summary>
      <returns>用來表示 <see cref="P:System.Data.Common.DbParameter.Value" /> 屬性的最大位數。</returns>
    </member>
    <member name="M:System.Data.Common.DbParameter.ResetDbType">
      <summary>將 DbType 屬性重設為其原始設定。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Scale">
      <summary>[在 .NET Framework 4.5.1 或更新版本中支援]取得或設定解析 <see cref="P:System.Data.Common.DbParameter.Value" /> 的小數位數數目。</summary>
      <returns>解析 <see cref="P:System.Data.Common.DbParameter.Value" /> 的小數位數數目。</returns>
    </member>
    <member name="P:System.Data.Common.DbParameter.Size">
      <summary>取得或設定資料行中資料的最大大小 (以位元組為單位)。</summary>
      <returns>資料行中資料的最大大小 (以位元組為單位)。預設值是由參數值推斷。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumn">
      <summary>取得或設定來源資料行的名稱，這個資料行對應至 <see cref="T:System.Data.DataSet" />，並用來載入或傳回 <see cref="P:System.Data.Common.DbParameter.Value" />。</summary>
      <returns>來源資料行名稱，對應至 <see cref="T:System.Data.DataSet" />。預設為空字串。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumnNullMapping">
      <summary>設定或取得值，這個值表示來源資料行是否可為 Null。這可以讓 <see cref="T:System.Data.Common.DbCommandBuilder" /> 針對可為 Null 的資料行正確地產生 Update 陳述式。</summary>
      <returns>如果來源資料行可為 null，則為 true，否則為 false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Value">
      <summary>取得或設定參數的值。</summary>
      <returns>
        <see cref="T:System.Object" />，為參數的值。預設值為 null。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbParameterCollection">
      <summary>有關 <see cref="T:System.Data.Common.DbCommand" /> 之參數集合的基底類別。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbParameterCollection" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Add(System.Object)">
      <summary>將指定的 <see cref="T:System.Data.Common.DbParameter" /> 物件加入至 <see cref="T:System.Data.Common.DbParameterCollection" /> 中。</summary>
      <returns>集合中 <see cref="T:System.Data.Common.DbParameter" /> 物件的索引。</returns>
      <param name="value">要加入集合的 <see cref="T:System.Data.Common.DbParameter" /> 之 <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.AddRange(System.Array)">
      <summary>將具有指定之值的項目陣列加入至 <see cref="T:System.Data.Common.DbParameterCollection" />。</summary>
      <param name="values">要加入至集合中的 <see cref="T:System.Data.Common.DbParameter" /> 型別之值陣列。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Clear">
      <summary>從 <see cref="T:System.Data.Common.DbParameterCollection" /> 移除所有 <see cref="T:System.Data.Common.DbParameter" /> 值。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.Object)">
      <summary>指出具有指定 <see cref="P:System.Data.Common.DbParameter.Value" /> 的 <see cref="T:System.Data.Common.DbParameter" /> 是否包含於集合中。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbParameter" /> 位於集合中，則為 true，否則為 false。</returns>
      <param name="value">要在集合中尋找的 <see cref="T:System.Data.Common.DbParameter" /> 之 <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.String)">
      <summary>指出具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" /> 是否存在於集合中。</summary>
      <returns>如果 <see cref="T:System.Data.Common.DbParameter" /> 位於集合中，則為 true，否則為 false。</returns>
      <param name="value">要在集合中尋找之 <see cref="T:System.Data.Common.DbParameter" /> 的名稱。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>從指定索引處開始，複製項目陣列至集合。</summary>
      <param name="array">要複製到集合的項目陣列。</param>
      <param name="index">要複製項目的集合中索引。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Count">
      <summary>指定集合中的項目數目。</summary>
      <returns>集合中的項目數。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetEnumerator">
      <summary>公開 (Expose) <see cref="M:System.Collections.IEnumerable.GetEnumerator" /> 方法，這個方法支援 .NET Framework 資料提供者對集合進行簡易的逐一查看。</summary>
      <returns>
        <see cref="T:System.Collections.IEnumerator" />，可用於逐一查看集合。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.Int32)">
      <summary>傳回集合中位於指定索引處的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</summary>
      <returns>位於集合中指定索引處的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</returns>
      <param name="index">集合中 <see cref="T:System.Data.Common.DbParameter" /> 的索引。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.String)">
      <summary>傳回具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</summary>
      <returns>具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</returns>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 的名稱。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.Object)">
      <summary>傳回指定之 <see cref="T:System.Data.Common.DbParameter" /> 物件的索引。</summary>
      <returns>指定之 <see cref="T:System.Data.Common.DbParameter" /> 物件的索引。</returns>
      <param name="value">集合中的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.String)">
      <summary>傳回具有指定之名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件索引。</summary>
      <returns>具有指定之名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件索引。</returns>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 物件的名稱。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Insert(System.Int32,System.Object)">
      <summary>在集合中指定的索引處，插入具有指定名稱之 <see cref="T:System.Data.Common.DbParameter" /> 物件之指定的索引。</summary>
      <param name="index">要插入 <see cref="T:System.Data.Common.DbParameter" /> 的索引。</param>
      <param name="value">要插入集合中的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.Int32)">
      <summary>取得和設定位於指定索引處的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>在指定索引處的 <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="index">參數的以零起始索引。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的索引不存在。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.String)">
      <summary>取得和設定具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" />。</summary>
      <returns>具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="parameterName">參數名稱。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定的索引不存在。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Remove(System.Object)">
      <summary>從集合移除指定的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</summary>
      <param name="value">要移除的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.Int32)">
      <summary>從集合中移除指定索引處的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> 物件所在的索引。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.String)">
      <summary>從集合移除具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件。</summary>
      <param name="parameterName">要移除之 <see cref="T:System.Data.Common.DbParameter" /> 的物件名稱。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
      <summary>將位於指定索引處的 <see cref="T:System.Data.Common.DbParameter" /> 物件設為新值。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> 物件所在的索引。</param>
      <param name="value">新的 <see cref="T:System.Data.Common.DbParameter" /> 值。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
      <summary>將具有指定名稱的 <see cref="T:System.Data.Common.DbParameter" /> 物件設為新值。</summary>
      <param name="parameterName">集合中 <see cref="T:System.Data.Common.DbParameter" /> 物件的名稱。</param>
      <param name="value">新的 <see cref="T:System.Data.Common.DbParameter" /> 值。</param>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.SyncRoot">
      <summary>指定 <see cref="T:System.Object" />，要用來同步對集合的存取。</summary>
      <returns>
        <see cref="T:System.Object" />，要用來同步對 <see cref="T:System.Data.Common.DbParameterCollection" /> 的存取。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#Item(System.Int32)">
      <summary>取得或設定指定之索引處的項目。</summary>
      <returns>在指定之索引處的項目。</returns>
      <param name="index">要取得或設定之以零起始的項目索引。</param>
    </member>
    <member name="T:System.Data.Common.DbProviderFactory">
      <summary>表示用於建立資料來源類別 (class) 之提供者 (Provider) 實作 (Implementation) 執行個體的一組方法。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.#ctor">
      <summary>初始化 <see cref="T:System.Data.Common.DbProviderFactory" /> 類別的新執行個體。</summary>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateCommand">
      <summary>傳回提供者類別的新執行個體，該類別實作 <see cref="T:System.Data.Common.DbCommand" /> 類別。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> 的新執行個體。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnection">
      <summary>傳回提供者類別的新執行個體，該類別實作 <see cref="T:System.Data.Common.DbConnection" /> 類別。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnection" /> 的新執行個體。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnectionStringBuilder">
      <summary>傳回提供者類別的新執行個體，該類別實作 <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 類別。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 的新執行個體。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateParameter">
      <summary>傳回提供者類別的新執行個體，該類別實作 <see cref="T:System.Data.Common.DbParameter" /> 類別。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> 的新執行個體。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbTransaction">
      <summary>交易的基底類別。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.#ctor">
      <summary>初始化新的 <see cref="T:System.Data.Common.DbTransaction" /> 物件。</summary>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Commit">
      <summary>認可資料庫交易。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.Connection">
      <summary>指定與交易關聯的 <see cref="T:System.Data.Common.DbConnection" /> 物件。</summary>
      <returns>與交易相關聯的 <see cref="T:System.Data.Common.DbConnection" /> 物件。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.DbConnection">
      <summary>指定與交易關聯的 <see cref="T:System.Data.Common.DbConnection" /> 物件。</summary>
      <returns>與交易相關聯的 <see cref="T:System.Data.Common.DbConnection" /> 物件。</returns>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose">
      <summary>釋放由 <see cref="T:System.Data.Common.DbTransaction" /> 使用的 Unmanaged 資源。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose(System.Boolean)">
      <summary>釋放 <see cref="T:System.Data.Common.DbTransaction" /> 所使用的 Unmanaged 資源，並選擇性釋放 Managed 資源。</summary>
      <param name="disposing">如果為 true，則這個方法會釋放這個 <see cref="T:System.Data.Common.DbTransaction" /> 參考之任何 Managed 物件所持有的所有資源。</param>
    </member>
    <member name="P:System.Data.Common.DbTransaction.IsolationLevel">
      <summary>指定用於這個交易的 <see cref="T:System.Data.IsolationLevel" />。</summary>
      <returns>用於這個交易的 <see cref="T:System.Data.IsolationLevel" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Rollback">
      <summary>從暫止狀態復原交易。</summary>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>