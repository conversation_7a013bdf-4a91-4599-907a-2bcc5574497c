﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_shxqxfcx" id="workday_shxqxfcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_dhxqxfcx" id="workday_dhxqxfcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/haixinquan_xiaofei_chaxun?workday_shxqxfcx=" + $('#workday_shxqxfcx').val() + "&workday_dhxqxfcx=" + $('#workday_dhxqxfcx').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站名称</td><td>油站编码</td><td>付款ID</td><td>销售状态</td><td>订单号</td><td>销售时间</td><td>订单状态</td><td>券号</td><td>券名称</td><td>订单编码</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGNAME + '</td><td>' + item.ORGCODE + '</td><td>' + item.OPENID + '</td><td>' + item.DAY_XS_CNT + '</td><td>' + item.SALENO + '</td><td>' + item.XSDATE + '</td><td>' + item.IS_QUAN + '%</td><td>' + item.CERTINO + '</td><td>' + item.CERTINAME + '</td><td>' + item.CXBILLNO + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/haixinquan_xiaofei_chaxun_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
