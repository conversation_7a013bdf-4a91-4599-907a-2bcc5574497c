﻿
@{
    ViewBag.Title = "海信销售明细查询 HX_xiaoshoumingxi_read";
}

<h2>海信销售明细查询(HX_tSalPluDetail)</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div class="pull-left">
                <div>
                    分公司名称：<input type="text" name="fengongsi" id="fengongsi" size="20" value="澄迈"/>
                    &nbsp;
                    市县公司名称：<input type="text" name="shixian" id="shixian" size="20" value="临高"/>
                    &nbsp;
                    便利店编码：<input type="text" name="orgcode" id="orgcode" size="20" />
                    &nbsp;
                    便利店名称：<input type="text" name="orgname" id="orgname" size="20" />
                    &nbsp;<br />
                </div>
                <br />
                <div>
                    地理位置：<input type="text" name="weizhi" id="weizhi" size="20" />
                    &nbsp;
                    便利店类型：<input type="text" name="leixing" id="leixing" size="20" />
                    &nbsp;
                    销售类型：<input type="text" name="xxlx" id="xxlx" size="20" />
                    &nbsp;
                    商品名称：<input type="text" name="pluname" id="pluname" size="20" />
                    &nbsp;
                    <br />
                </div>
                <br />
                <div>

                    销售日期：<input type="text" name="rptdate_sta" id="rptdate_sta" size="20" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})"/>  
                    -- <input type="text" name="rptdate_end" id="rptdate_end" size="20" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})"/>
                    &nbsp;
                    商品类型1：<input type="text" name="splx1" id="splx1" size="20" />
                    &nbsp;
                    商品类型2：<input type="text" name="splx2" id="splx2" size="20" />
                    &nbsp;
                    <br />
                </div>
                <br />
                <div>
                    商品类型3：<input type="text" name="splx3" id="splx3" size="20" />
                    &nbsp;
                    大类：<input type="text" name="dalei" id="dalei" size="20" />
                    &nbsp;
                    中类：<input type="text" name="zhonglei" id="zhonglei" size="20" />
                    &nbsp;
                    小类：<input type="text" name="xiaolei" id="xiaolei" size="20" />
                    &nbsp;

                </div>
                <br />
                <div>
                    商品编码：<input type="text" name="plucode" id="plucode" size="20" />
                    &nbsp;
                </div>
                <br />
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
                &nbsp;<input id="btn_tongbu" type="submit" value="备用" />
            </div>
            <!-- div class="pull-right"><input id="btn_xiangyan_cx" type="button" value="香烟分类占比查询" /></div-->
        </form>
        </n>
        <div id="div1"></div>
        <br/>
        <table id="shuju_1" border=1></table>
        
    </div>
</body>
</html>

<script>
    //$(document).ready(function)   当 DOM（document object model 文档对象模型）加载完毕且页面完全加载（包括图像）时发生 ready 事件
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/HX_xiaoshou_mingxi_read?fengongsi=" + $('#fengongsi').val() + "&shixian=" + $('#shixian').val() 
                + "&orgcode=" + $('#orgcode').val() + "&orgname=" + $('#orgname').val() + "&weizhi=" + $('#weizhi').val()
                + "&leixing=" + $('#leixing').val() + "&xxlx=" + $('#xxlx').val() + "&rptdate_sta=" + $('#rptdate_sta').val()
                + "&rptdate_end=" + $('#rptdate_end').val() + "&dalei=" + $('#dalei').val() + "&zhonglei=" + $('#zhonglei').val()
                + "&xiaolei=" + $('#xiaolei').val() + "&plucode=" + $('#plucode').val() + "&pluname=" + $('#pluname').val()
                + "&splx3=" + $('#splx3').val() + "&splx1=" + $('#splx1').val() , function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><th>分公司</th><th>市县公司</th><th>便利店编码</th><th>便利店名称</th><th>便利店位置</th><th>便利店类型</th><th>销售类型</th><th>销售日期</th><th>大类</th><th>中类</th><th>小类</th><th>商品编码</th><th>商品名称</th><th>商品类型1</th><th>商品类型2</th><th>商品类型3</th><th>规格</th><th>计量单位</th><th>销售数量</th><th>单价</th><th>销售金额</th><th>税率</th><th>不含税金额</th><th>不含税销售成本</th><th>不含税毛利额</th><th>不含税毛利率</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.fengongsi + '</td><td>' + item.shixian + '</td><td>' + item.orgcode + '</td><td>' + item.orgname + '</td><td>'
                            + item.weizhi + '</td><td>' + item.leixing + '</td ><td>' + item.xxlx + '</td ><td>' + item.rptdate + '</td ><td>' + item.dalei + '</td><td>'
                            + item.zhonglei + '</td><td>' + item.xiaolei + '</td ><td>' + item.plucode + '</td><td>' + item.pluname + '</td><td>' + item.splx1 + '</td><td>'
                            + item.splx2 + '</td ><td>' + item.splx3 + '</td><td>' + item.unit + '</td><td>' + item.spec + '</td ><td>' + item.xscount + '</td><td>'
                            + item.price + '</td ><td>' + item.hxtotal + '</td ><td>' + item.xtaxrate + '</td><td>' + item.wxtotal + '</td><td>' + item.wjcost + '</td ><td>'
                            + item.wmtotal + '</td ><td>' + item.wlmll    + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });



        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/HX_xiaoshou_mingxi_read_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

       
    });
</script>
