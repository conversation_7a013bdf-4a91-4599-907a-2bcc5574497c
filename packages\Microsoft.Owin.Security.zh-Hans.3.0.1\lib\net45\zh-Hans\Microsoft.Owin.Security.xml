﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Owin.Security</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Owin.Security.AppBuilderSecurityExtensions">
      <summary>提供只有身份验证中间件实现需要的 app.Property 值的扩展方法。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.AppBuilderSecurityExtensions.GetDefaultSignInAsAuthenticationType(Owin.IAppBuilder)">
      <summary>返回前面设置的、外部登录中间件在浏览器导航回返回 URL 时应使用的 AuthenticationType。</summary>
      <param name="app">传递给应用程序启动代码的应用程序生成器</param>
    </member>
    <member name="M:Microsoft.Owin.Security.AppBuilderSecurityExtensions.SetDefaultSignInAsAuthenticationType(Owin.IAppBuilder,System.String)">
      <summary>由中间件调用，用于更改外部登录中间件在浏览器导航回返回 URL 时应使用的 AuthenticationType 的名称。</summary>
      <param name="app">传递给应用程序启动代码的应用程序生成器</param>
      <param name="authenticationType">外部中间件登录时应使用的 AuthenticationType。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.AuthenticationMode">
      <summary>控制身份验证中间件的行为</summary>
    </member>
    <member name="F:Microsoft.Owin.Security.AuthenticationMode.Active">
      <summary>在主动模式下，身份验证中间件将在请求到达时更改用户标识，并在响应离开时更改 401 纯文本。</summary>
    </member>
    <member name="F:Microsoft.Owin.Security.AuthenticationMode.Passive">
      <summary>在被动模式下，身份验证中间件只在提示时提供用户标识，并且只会更改在附加质询数据中指定了身份验证类型的 401 响应。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.AuthenticationOptions">
      <summary>所有身份验证中间件的基本选项</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.AuthenticationOptions.#ctor(System.String)">
      <summary>初始化 AuthenticationOptions 基类的属性</summary>
      <param name="authenticationType">分配给 AuthenticationType 属性</param>
    </member>
    <member name="P:Microsoft.Owin.Security.AuthenticationOptions.AuthenticationMode">
      <summary>如果为 Active，则身份验证中间件将更改传入的请求用户，并更改传出的 401 未授权响应。如果为 Passive，则仅当 AuthenticationType 已进行显式指定时，身份验证中间件才提供标识并更改响应。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.AuthenticationOptions.AuthenticationType">
      <summary>选项中的 AuthenticationType 对应于 IIdentity AuthenticationType 属性。可以分配其他值，以便在一个管道中多次使用同一个身份验证中间件类型。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.AuthenticationOptions.Description">
      <summary>为应用程序提供的有关身份验证类型的附加信息。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.AuthenticationTicket">
      <summary>包含用户标识信息以及附加的身份验证状态。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.AuthenticationTicket.#ctor(System.Security.Claims.ClaimsIdentity,Microsoft.Owin.Security.AuthenticationProperties)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.AuthenticationTicket" /> 类的新实例</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.AuthenticationTicket.Identity">
      <summary>获取已经过身份验证的用户标识。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.AuthenticationTicket.Properties">
      <summary>身份验证会话的附加状态值。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator">
      <summary>根据证书的使用者密钥标识符提供固定证书验证。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator.#ctor(System.Collections.Generic.IEnumerable{System.String})">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator" /> 类的新实例。</summary>
      <param name="validSubjectKeyIdentifiers">一组对 HTTPS 请求有效的使用者密钥标识符。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateSubjectKeyIdentifierValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
      <summary>验证用于身份验证的远程安全套接字层 (SSL) 证书。</summary>
      <returns>一个布尔值，确定是否接受使用指定的证书进行身份验证。</returns>
      <param name="sender">包含此验证的状态信息的对象。</param>
      <param name="certificate">用于对远程方进行身份验证的证书。</param>
      <param name="chain">与远程证书关联的证书颁发机构链。</param>
      <param name="sslPolicyErrors">与远程证书关联的一个或多个错误。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator">
      <summary>实现 http://datatracker.ietf.org/doc/draft-ietf-websec-key-pinning/?include_text=1 上传递的证书固定验证程序</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator.#ctor(System.Collections.Generic.IEnumerable{System.String},Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator" /> 类的新实例。</summary>
      <param name="validBase64EncodedSubjectPublicKeyInfoHashes">证书公钥信息 Blob 的有效 base64 编码哈希集合。</param>
      <param name="algorithm">用于生成哈希的算法。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateSubjectPublicKeyInfoValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
      <summary>验证是否至少有一个已知的 SPKI 哈希。</summary>
      <returns>一个布尔值，确定是否接受使用指定的证书进行身份验证。</returns>
      <param name="sender">包含此验证的状态信息的对象。</param>
      <param name="certificate">用于对远程方进行身份验证的证书。</param>
      <param name="chain">与远程证书关联的证书颁发机构链。</param>
      <param name="sslPolicyErrors">与远程证书关联的一个或多个错误。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.CertificateThumbprintValidator">
      <summary>基于证书指纹提供固定证书验证。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateThumbprintValidator.#ctor(System.Collections.Generic.IEnumerable{System.String})">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.CertificateThumbprintValidator" /> 类的新实例。</summary>
      <param name="validThumbprints">对 HTTPS 请求有效的一组指纹。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.CertificateThumbprintValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
      <summary>验证签名链中的证书指纹是否至少与核准清单中的一个指纹相匹配。</summary>
      <returns>一个布尔值，确定是否接受使用指定的证书进行身份验证。</returns>
      <param name="sender">包含此验证的状态信息的对象。</param>
      <param name="certificate">用于对远程方进行身份验证的证书。</param>
      <param name="chain">与远程证书关联的证书颁发机构链。</param>
      <param name="sslPolicyErrors">与远程证书关联的一个或多个错误。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.ICertificateValidator">
      <summary>用于提供固定证书验证的接口，该验证根据已知良好的证书列表检查 HTTPS 通信，防止不安全的或恶意的 CA 为主机所有者未知的主机颁发证书。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.ICertificateValidator.Validate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
      <summary>验证用于身份验证的远程安全套接字层 (SSL) 证书。</summary>
      <returns>一个布尔值，确定是否接受使用指定的证书进行身份验证。</returns>
      <param name="sender">包含此验证的状态信息的对象。</param>
      <param name="certificate">用于对远程方进行身份验证的证书。</param>
      <param name="chain">与远程证书关联的证书颁发机构链。</param>
      <param name="sslPolicyErrors">与远程证书关联的一个或多个错误。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.ISecureDataFormat`1">
      <summary>表示要保护的数据。</summary>
      <typeparam name="TData">泛型类型数据。</typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.ISecureDataFormat`1.Protect(`0)">
      <summary>保护数据，使其不能被格式化。</summary>
      <returns>要保护的数据。</returns>
      <param name="data">数据。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.ISecureDataFormat`1.Unprotect(System.String)">
      <summary>解密指定文本中的数据。</summary>
      <returns>要解密的数据。</returns>
      <param name="protectedText">受保护的文本。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm">
      <summary>用于生成使用者公钥信息 blob 哈希的算法。</summary>
    </member>
    <member name="F:Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm.Sha1">
      <summary />
    </member>
    <member name="F:Microsoft.Owin.Security.SubjectPublicKeyInfoAlgorithm.Sha256">
      <summary />
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.PropertiesDataFormat"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.PropertiesDataFormat.#ctor(Microsoft.Owin.Security.DataProtection.IDataProtector)"></member>
    <member name="T:Microsoft.Owin.Security.DataHandler.SecureDataFormat`1">
      <typeparam name="TData"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.SecureDataFormat`1.#ctor(Microsoft.Owin.Security.DataHandler.Serializer.IDataSerializer{`0},Microsoft.Owin.Security.DataProtection.IDataProtector,Microsoft.Owin.Security.DataHandler.Encoder.ITextEncoder)"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.SecureDataFormat`1.Protect(`0)"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.SecureDataFormat`1.Unprotect(System.String)"></member>
    <member name="T:Microsoft.Owin.Security.DataHandler.TicketDataFormat"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.TicketDataFormat.#ctor(Microsoft.Owin.Security.DataProtection.IDataProtector)"></member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Encoder.Base64TextEncoder">
      <summary>表示 base64 文本格式编码器。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64TextEncoder.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.DataHandler.Encoder.Base64TextEncoder" /> 类的新实例。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64TextEncoder.Decode(System.String)">
      <summary>为指定的文本解码。</summary>
      <returns>表示解码数据的 <see cref="T:System.Byte" />。</returns>
      <param name="text">要解码的文本。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64TextEncoder.Encode(System.Byte[])">
      <summary>将数据转换为 base64 格式的字符串。</summary>
      <returns>已转换为 base64 格式的数据字符串。</returns>
      <param name="data">要转换的数据。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Encoder.Base64UrlTextEncoder">
      <summary>表示 Base64Url 文本格式编码器。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64UrlTextEncoder.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.DataHandler.Encoder.Base64UrlTextEncoder" /> 类的新实例。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64UrlTextEncoder.Decode(System.String)">
      <summary>为指定的文本解码。</summary>
      <returns>表示解码数据的 <see cref="T:System.Byte" />。</returns>
      <param name="text">要解码的文本。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.Base64UrlTextEncoder.Encode(System.Byte[])">
      <summary>将数据转换为 Base64Url 格式的字符串。</summary>
      <returns>已转换为 Base64Url 格式的数据字符串。</returns>
      <param name="data">要转换的数据。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Encoder.ITextEncoder">
      <summary>表示文本编码器的接口。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.ITextEncoder.Decode(System.String)">
      <summary>为指定的文本解码。</summary>
      <returns>表示解码数据的 <see cref="T:System.Byte" />。</returns>
      <param name="text">要解码的文本。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Encoder.ITextEncoder.Encode(System.Byte[])">
      <summary>将数据转换为字符串。</summary>
      <returns>转换的数据字符串。</returns>
      <param name="data">要转换的数据。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Encoder.TextEncodings">
      <summary>表示文本编码。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.DataHandler.Encoder.TextEncodings.Base64">
      <summary>获取 Base64 文本编码器。</summary>
      <returns>Base64 文本编码器。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.DataHandler.Encoder.TextEncodings.Base64Url">
      <summary>获取 Base64Url 文本编码器。</summary>
      <returns>Base64Url 文本编码器。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Serializer.DataSerializers">
      <summary>提供数据序列化程序。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.DataHandler.Serializer.DataSerializers.Properties"></member>
    <member name="P:Microsoft.Owin.Security.DataHandler.Serializer.DataSerializers.Ticket">
      <summary>获取或设置票证数据序列化程序。</summary>
      <returns>票证数据序列化程序。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Serializer.IDataSerializer`1">
      <summary>表示数据序列化程序的接口。</summary>
      <typeparam name="TModel">数据模型的类型。</typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.IDataSerializer`1.Deserialize(System.Byte[])">
      <summary>反序列化指定的数据。</summary>
      <param name="data">要反序列化的数据。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.IDataSerializer`1.Serialize(`0)">
      <summary>使用给定的模型序列化数据。</summary>
      <param name="model">数据模型。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer.#ctor"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer.Deserialize(System.Byte[])"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer.Read(System.IO.BinaryReader)"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer.Serialize(Microsoft.Owin.Security.AuthenticationProperties)"></member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.PropertiesSerializer.Write(System.IO.BinaryWriter,Microsoft.Owin.Security.AuthenticationProperties)"></member>
    <member name="T:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer">
      <summary>表示票证序列化程序。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer" /> 类的新实例。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer.Deserialize(System.Byte[])">
      <summary>反序列化票证数据。</summary>
      <returns>正在反序列化的数据。</returns>
      <param name="data">要反序列化的数据。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer.Read(System.IO.BinaryReader)">
      <summary>从读取器中读取指定的数据。</summary>
      <returns>正在读取的数据。</returns>
      <param name="reader">要从中读取的读取器。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer.Serialize(Microsoft.Owin.Security.AuthenticationTicket)">
      <summary>使用给定的模型序列化数据。</summary>
      <param name="model">数据模型。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataHandler.Serializer.TicketSerializer.Write(System.IO.BinaryWriter,Microsoft.Owin.Security.AuthenticationTicket)">
      <summary>从指定的写入器写入数据。</summary>
      <param name="writer">写入器。</param>
      <param name="model">数据模型。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataProtection.AppBuilderExtensions">
      <summary>应用程序生成器扩展。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.AppBuilderExtensions.CreateDataProtector(Owin.IAppBuilder,System.String[])">
      <summary>创建数据保护程序。</summary>
      <returns>创建的 <see cref="T:Microsoft.Owin.Security.DataProtection.IDataProtector" />。</returns>
      <param name="app">应用程序生成器。</param>
      <param name="purposes">创建数据保护程序的目的列表。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.AppBuilderExtensions.GetDataProtectionProvider(Owin.IAppBuilder)">
      <summary>检索应用程序的数据保护提供程序。</summary>
      <returns>要获取的 <see cref="T:Microsoft.Owin.Security.DataProtection.IDataProtectionProvider" /> 对象。</returns>
      <param name="app">应用程序生成器。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.AppBuilderExtensions.SetDataProtectionProvider(Owin.IAppBuilder,Microsoft.Owin.Security.DataProtection.IDataProtectionProvider)">
      <summary>设置数据保护提供程序。</summary>
      <param name="app">应用程序生成器。</param>
      <param name="dataProtectionProvider">要设置的数据保护提供程序。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider">
      <summary>用于提供从数据保护 API 派生的数据保护服务。当应用程序未由 ASP.NET 托管，并且所有进程以相同的域标识运行时，这是最佳的数据保护选项。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.#ctor">
      <summary>使用随机应用程序名称初始化新的 DpapiDataProtectionProvider。这只可用于在执行当前应用程序的持续时间内保护数据。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.#ctor(System.String)">
      <summary>初始化一个使用给定 appName 作为保护算法一部分的新 DpapiDataProtectionProvider</summary>
      <param name="appName">用户提供的值，往返访问受保护数据时需要该值。在自托管环境中，默认值取自 IAppBuilder.Properties["owin.AppName"]。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.DpapiDataProtectionProvider.Create(System.String[])">
      <summary>返回提供程序的 IDataProtection 的新实例。</summary>
      <returns>数据保护服务的实例</returns>
      <param name="purposes">附加的熵，用于确保只针对正当目的取消保护受保护的数据。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataProtection.IDataProtectionProvider">
      <summary> 用于创建 IDataProtection 实例的工厂。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtectionProvider.Create(System.String[])">
      <summary> 返回提供程序的 IDataProtection 的新实例。</summary>
      <returns>数据保护服务的实例。</returns>
      <param name="purposes">附加的熵，用于确保只针对正当目的取消保护受保护的数据。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.DataProtection.IDataProtector">
      <summary> 用于保护和取消保护数据的服务。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtector.Protect(System.Byte[])">
      <summary> 调用该项可以保护用户数据。</summary>
      <param name="userData">必须保护的原始数据。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.DataProtection.IDataProtector.Unprotect(System.Byte[])">
      <summary> 调用该项可以取消保护用户数据。</summary>
      <param name="protectedData">对相同的 IDataProtection 服务调用 Protect 后返回的字节数组。</param>
    </member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler">
      <summary> 大多数身份验证中间件执行的基于请求的工作的基类。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler" /> 类的新实例。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseChallengeAsync">
      <summary>如果相关身份验证方案将身份验证交互作为其请求流的一部分处理，则重写此方法以处理 401 质询问题。（例如添加响应标头，或将 401 结果更改为登录页的 302 或外部登录位置。）</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseCoreAsync">
      <summary>可以由处理程序重写的核心方法。默认行为是调用两个公共响应活动，一个用于处理登录/注销问题，另一个用于处理 401 质询。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ApplyResponseGrantAsync">
      <summary>如果相关身份验证方案将授权/吊销作为其请求流的一部分处理，则重写此方法以处理登录/注销问题。（例如设置/删除 Cookie）</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.AuthenticateAsync">
      <summary>针对当前请求最多执行一次 AuthenticateCore 中的身份验证逻辑，并返回结果。多次调用 Authenticate 将始终返回原始值。应始终调用此方法，而不要直接调用 AuthenticateCore。</summary>
      <returns>由身份验证逻辑提供的票证数据</returns>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.AuthenticateCoreAsync">
      <summary>必须由处理程序提供的核心身份验证逻辑。针对每个请求最多调用一次。不要直接调用，而应调用包装 Authenticate 方法。</summary>
      <returns>由身份验证逻辑提供的票证数据</returns>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.BaseInitializeAsync(Microsoft.Owin.Security.AuthenticationOptions,Microsoft.Owin.IOwinContext)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.Context"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.GenerateCorrelationId(Microsoft.Owin.Security.AuthenticationProperties)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.Helper"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.InitializeCoreAsync"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.InvokeAsync">
      <summary>在初始化之后由公共代码调用一次。如果身份验证中间件直接响应已知的专用路径，则必须重写此虚拟路径，将请求路径与其已知路径进行比较，提供适当的响应信息，并停止进一步处理。</summary>
      <returns>若返回 false，则公共代码将按顺序调用下一个中间件。若返回 true，则公共代码将开始异步完成过程，而不调用中间件管道的其余部分。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.Request"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.RequestPathBase"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.Response"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.TeardownCoreAsync"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler.ValidateCorrelationId(Microsoft.Owin.Security.AuthenticationProperties,Microsoft.Owin.Logging.ILogger)"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1">
      <summary>大多数身份验证中间件执行的基于请求的工作的基类。</summary>
      <typeparam name="TOptions">指定 AuthenticationOptions 属性的类型</typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1" /> 类的新实例。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1.Options"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1">
      <summary>表示身份验证中间件。</summary>
      <typeparam name="TOptions">身份验证选项的类型。</typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1.#ctor(Microsoft.Owin.OwinMiddleware,`0)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1" /> 类的新实例。</summary>
      <param name="next">下一个 OWIN 中间件。</param>
      <param name="options">身份验证选项。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1.CreateHandler">
      <summary>创建身份验证处理程序。</summary>
      <returns>创建的 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler`1" />。</returns>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1.Invoke(Microsoft.Owin.IOwinContext)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationMiddleware`1.Options">
      <summary>获取或设置身份验证选项。</summary>
      <returns>完成身份验证的任务。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.ISecureDataFormat{Microsoft.Owin.Security.AuthenticationTicket},Microsoft.Owin.Security.AuthenticationTicket)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext.SerializeTicket"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext.SetToken(System.String)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext.Ticket"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext.Token"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.#ctor"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.Create(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.CreateAsync(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.OnCreate"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.OnCreateAsync"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.OnReceive"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.OnReceiveAsync"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.Receive(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenProvider.ReceiveAsync(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext)"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.ISecureDataFormat{Microsoft.Owin.Security.AuthenticationTicket},System.String)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext.DeserializeTicket(System.String)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext.SetTicket(Microsoft.Owin.Security.AuthenticationTicket)"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext.Ticket"></member>
    <member name="P:Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext.Token"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.IAuthenticationTokenProvider"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.IAuthenticationTokenProvider.Create(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.IAuthenticationTokenProvider.CreateAsync(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenCreateContext)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.IAuthenticationTokenProvider.Receive(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.IAuthenticationTokenProvider.ReceiveAsync(Microsoft.Owin.Security.Infrastructure.AuthenticationTokenReceiveContext)"></member>
    <member name="T:Microsoft.Owin.Security.Infrastructure.SecurityHelper">
      <summary>在实现身份验证中间件时使用的帮助器代码</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.#ctor(Microsoft.Owin.IOwinContext)">
      <summary>在实现身份验证中间件时使用的帮助器代码</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.AddUserIdentity(System.Security.Principal.IIdentity)">
      <summary>将一个附加的 ClaimsIdentity 添加到“server.User”环境键中的 ClaimsPrincipal</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.Equals(Microsoft.Owin.Security.Infrastructure.SecurityHelper)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.Equals(System.Object)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.GetHashCode"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupChallenge(System.String,Microsoft.Owin.Security.AuthenticationMode)">
      <summary>查找特定身份验证中间件的响应质询详细信息</summary>
      <returns>为中间件的行为方式提供指示的信息</returns>
      <param name="authenticationType">要查找的身份验证类型</param>
      <param name="authenticationMode">中间件在运行时所处的身份验证模式</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupSignIn(System.String)">
      <summary>查找特定身份验证中间件的响应登录详细信息</summary>
      <returns>为中间件的行为方式提供指示的信息</returns>
      <param name="authenticationType">要查找的身份验证类型</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.LookupSignOut(System.String,Microsoft.Owin.Security.AuthenticationMode)">
      <summary>查找特定身份验证中间件的响应注销详细信息</summary>
      <returns>为中间件的行为方式提供指示的信息</returns>
      <param name="authenticationType">要查找的身份验证类型</param>
      <param name="authenticationMode">中间件在运行时所处的身份验证模式</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.op_Equality(Microsoft.Owin.Security.Infrastructure.SecurityHelper,Microsoft.Owin.Security.Infrastructure.SecurityHelper)"></member>
    <member name="M:Microsoft.Owin.Security.Infrastructure.SecurityHelper.op_Inequality(Microsoft.Owin.Security.Infrastructure.SecurityHelper,Microsoft.Owin.Security.Infrastructure.SecurityHelper)"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.AuthenticationFailedNotification`2">
      <typeparam name="TMessage"></typeparam>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.AuthenticationFailedNotification`2.#ctor(Microsoft.Owin.IOwinContext,`1)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.AuthenticationFailedNotification`2.Exception"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.AuthenticationFailedNotification`2.ProtocolMessage"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.BaseNotification`1">
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.BaseNotification`1.#ctor(Microsoft.Owin.IOwinContext,`0)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.BaseNotification`1.HandledResponse"></member>
    <member name="M:Microsoft.Owin.Security.Notifications.BaseNotification`1.HandleResponse">
      <summary>停止针对此请求的所有处理并返回到客户端。调用方负责生成完整响应。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Notifications.BaseNotification`1.Skipped"></member>
    <member name="M:Microsoft.Owin.Security.Notifications.BaseNotification`1.SkipToNextMiddleware">
      <summary>停止处理当前中间件中的请求并将控制权传递给下一个项。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Notifications.BaseNotification`1.State"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.MessageReceivedNotification`2">
      <typeparam name="TMessage"></typeparam>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.MessageReceivedNotification`2.#ctor(Microsoft.Owin.IOwinContext,`1)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.MessageReceivedNotification`2.ProtocolMessage"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.NotificationResultState"></member>
    <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.Continue">
      <summary>继续正常处理。</summary>
    </member>
    <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.HandledResponse">
      <summary>停止针对此请求的所有处理。</summary>
    </member>
    <member name="F:Microsoft.Owin.Security.Notifications.NotificationResultState.Skipped">
      <summary>停止处理当前中间件中的请求并将控制权传递给下一个项。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2">
      <typeparam name="TMessage"></typeparam>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.#ctor(Microsoft.Owin.IOwinContext,`1)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.HandledResponse"></member>
    <member name="M:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.HandleResponse">
      <summary>停止针对此请求的所有处理并返回到客户端。调用方负责生成完整响应。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.ProtocolMessage"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.RedirectToIdentityProviderNotification`2.State"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.SecurityTokenReceivedNotification`2">
      <typeparam name="TMessage"></typeparam>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.SecurityTokenReceivedNotification`2.#ctor(Microsoft.Owin.IOwinContext,`1)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.SecurityTokenReceivedNotification`2.ProtocolMessage"></member>
    <member name="T:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2">
      <typeparam name="TMessage"></typeparam>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.#ctor(Microsoft.Owin.IOwinContext,`1)"></member>
    <member name="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.AuthenticationTicket">
      <summary>获取或设置 <see cref="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.AuthenticationTicket" /></summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Notifications.SecurityTokenValidatedNotification`2.ProtocolMessage">
      <summary>获取或设置协议消息</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Provider.BaseContext">
      <summary>表示基上下文。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.BaseContext.#ctor(Microsoft.Owin.IOwinContext)"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext.OwinContext"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext.Request"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext.Response"></member>
    <member name="T:Microsoft.Owin.Security.Provider.BaseContext`1">
      <summary>用于特定事件上下文的基类</summary>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.BaseContext`1.#ctor(Microsoft.Owin.IOwinContext,`0)"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext`1.Options"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext`1.OwinContext"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext`1.Request"></member>
    <member name="P:Microsoft.Owin.Security.Provider.BaseContext`1.Response"></member>
    <member name="T:Microsoft.Owin.Security.Provider.EndpointContext">
      <summary>表示终结点上下文。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.EndpointContext.#ctor(Microsoft.Owin.IOwinContext)"></member>
    <member name="P:Microsoft.Owin.Security.Provider.EndpointContext.IsRequestCompleted">
      <summary>获取或设置一个值，该值指示此上下文的请求是否已完成。</summary>
      <returns>如果此上下文的请求已完成，则为 true；否则为 false。</returns>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.EndpointContext.RequestCompleted">
      <summary>指示上下文的已完成请求。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Provider.EndpointContext`1">
      <summary>用于特定事件上下文的基类</summary>
      <typeparam name="TOptions"></typeparam>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.EndpointContext`1.#ctor(Microsoft.Owin.IOwinContext,`0)">
      <summary>创建此上下文的实例</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Provider.EndpointContext`1.IsRequestCompleted">
      <summary>如果不应由其他组件进一步处理请求，则返回 True。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.EndpointContext`1.RequestCompleted">
      <summary>阻止其他组件进一步处理请求。调用后，IsRequestCompleted 将为 true。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Provider.ReturnEndpointContext">
      <summary>表示返回终结点上下文。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Provider.ReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)"></member>
    <member name="P:Microsoft.Owin.Security.Provider.ReturnEndpointContext.Identity">
      <summary>获取或设置此上下文的标识。</summary>
      <returns>此上下文的标识。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Provider.ReturnEndpointContext.Properties"></member>
    <member name="P:Microsoft.Owin.Security.Provider.ReturnEndpointContext.RedirectUri">
      <summary>获取或设置返回此上下文时使用的 URI。</summary>
      <returns>返回此上下文时使用的 URI。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Provider.ReturnEndpointContext.SignInAsAuthenticationType">
      <summary>获取或设置作为身份验证类型登录的上下文。</summary>
      <returns>作为身份验证类型登录的上下文。</returns>
    </member>
  </members>
</doc>