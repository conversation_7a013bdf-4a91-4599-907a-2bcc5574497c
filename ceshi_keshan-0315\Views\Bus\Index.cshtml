@model List<ceshi_keshan_0315.ViewModels.BusVM>
@{
    ViewBag.Title = "公车信息管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container">
    <div class="page-header">
        <h1 class="text-success">✅ @ViewBag.Title</h1>
        <p class="text-muted">@ViewBag.Message</p>
        <p><strong>当前时间：</strong> @ViewBag.CurrentTime</p>
    </div>

    @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
    {
        <div class="alert alert-danger">
            <strong>错误：</strong> @ViewBag.ErrorMessage
        </div>
    }

    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 路由测试成功</h3>
        </div>
        <div class="panel-body">
            <p><strong>控制器：</strong> <code>ceshi_keshan_0315.Controllers.BusController</code></p>
            <p><strong>动作：</strong> <code>Index</code></p>
            <p><strong>访问路径：</strong> <code>/Bus/Index</code></p>
            <p><strong>数据总数：</strong> @ViewBag.TotalCount</p>
        </div>
    </div>

    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔗 功能测试</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h4>基础功能</h4>
                    <a href="/Bus/ExportToExcel" class="btn btn-success btn-block">导出CSV文件</a>
                    <a href="/Test/Index" class="btn btn-info btn-block">路由测试页面</a>
                </div>
                <div class="col-md-6">
                    <h4>导航链接</h4>
                    <a href="/BusApplication/Create" class="btn btn-primary btn-block">创建申请</a>
                    <a href="/home/<USER>" class="btn btn-default btn-block">返回原系统</a>
                </div>
            </div>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">公车列表 <span class="badge">@ViewBag.TotalCount</span></h3>
            </div>
            <div class="panel-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>车牌号</th>
                            <th>车型</th>
                            <th>品牌</th>
                            <th>状态</th>
                            <th>座位数</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var bus in Model)
                        {
                            <tr>
                                <td><strong>@bus.PlateNumber</strong></td>
                                <td>@bus.Model</td>
                                <td>@bus.Brand</td>
                                <td><span class="label label-success">@bus.StatusText</span></td>
                                <td>@bus.SeatCount 座</td>
                                <td>@bus.Remarks</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <h4>暂无数据</h4>
            <p>没有找到公车信息，这是正常的测试状态。</p>
        </div>
    }

    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">✅ 整合成功</h3>
        </div>
        <div class="panel-body">
            <p><strong>恭喜！</strong>公车管理模块已成功整合到主项目中。</p>
            <p><strong>当前访问路径：</strong> <code>/Bus/Index</code></p>
            <p><strong>控制器：</strong> <code>ceshi_keshan_0315.Controllers.BusController</code></p>
            <p><strong>视图：</strong> <code>Views/Bus/Index.cshtml</code></p>
            <hr>
            <p><strong>可用功能：</strong></p>
            <ul>
                <li>✅ 公车列表查看</li>
                <li>✅ CSV 文件导出</li>
                <li>✅ 路由测试</li>
                <li>✅ 基础功能验证</li>
            </ul>
        </div>
    </div>
</div>
