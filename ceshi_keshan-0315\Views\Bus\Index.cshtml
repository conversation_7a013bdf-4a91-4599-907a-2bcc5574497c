@model List<ceshi_keshan_0315.ViewModels.BusVM>
@{
    ViewBag.Title = "公车信息管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- 新增模块：公车信息管理页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">管理公车基础信息，查看车辆状态和使用情况</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success">
                    <strong>成功：</strong> @TempData["SuccessMessage"]
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @TempData["ErrorMessage"]
                </div>
            }

            <!-- 功能按钮 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">功能操作</h4>
                </div>
                <div class="panel-body">
                    <a href="@Url.Action("ExportToExcel", "Bus")" class="btn btn-success">
                        <i class="glyphicon glyphicon-download-alt"></i> 导出CSV
                    </a>
                    <a href="@Url.Action("Create", "BusApplication")" class="btn btn-primary">
                        <i class="glyphicon glyphicon-plus"></i> 创建申请
                    </a>
                    <a href="@Url.Action("Index", "BusApplication")" class="btn btn-info">
                        <i class="glyphicon glyphicon-list"></i> 申请列表
                    </a>
                    <a href="@Url.Action("Review", "BusApplication")" class="btn btn-warning">
                        <i class="glyphicon glyphicon-check"></i> 申请审批
                    </a>
                    <a href="/home/<USER>" class="btn btn-default">
                        <i class="glyphicon glyphicon-arrow-left"></i> 返回原系统
                    </a>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">筛选条件</h4>
                </div>
                <div class="panel-body">
                    @using (Html.BeginForm("Index", "Bus", FormMethod.Get, new { @class = "form-inline" }))
                    {
                        <div class="form-group">
                            @Html.Label("plateNumber", "车牌号：", new { @class = "control-label" })
                            @Html.TextBox("plateNumber", ViewBag.PlateNumber, new { @class = "form-control", placeholder = "请输入车牌号" })
                        </div>

                        <div class="form-group">
                            @Html.Label("status", "状态：", new { @class = "control-label" })
                            @Html.DropDownList("status", new List<SelectListItem>
                            {
                                new SelectListItem { Text = "全部", Value = "" },
                                new SelectListItem { Text = "可用", Value = "1" },
                                new SelectListItem { Text = "使用中", Value = "2" },
                                new SelectListItem { Text = "维修中", Value = "3" },
                                new SelectListItem { Text = "已报废", Value = "4" }
                            }, new { @class = "form-control" })
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="glyphicon glyphicon-search"></i> 查询
                        </button>

                        <a href="@Url.Action("Index", "Bus")" class="btn btn-default">
                            <i class="glyphicon glyphicon-refresh"></i> 重置
                        </a>
                    }
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        公车列表
                        <span class="badge">共 @ViewBag.TotalCount 辆</span>
                    </h4>
                </div>
                <div class="panel-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>车型</th>
                                        <th>品牌</th>
                                        <th>状态</th>
                                        <th>座位数</th>
                                        <th>购买日期</th>
                                        <th>使用年限</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var bus in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@bus.PlateNumber</strong>
                                            </td>
                                            <td>@bus.Model</td>
                                            <td>@bus.Brand</td>
                                            <td>
                                                <span class="label @GetStatusClass(bus.Status)">
                                                    @bus.StatusText
                                                </span>
                                            </td>
                                            <td>@bus.SeatCount 座</td>
                                            <td>@bus.PurchaseDate.ToString("yyyy-MM-dd")</td>
                                            <td>@bus.UsageYears 年</td>
                                            <td>@bus.Remarks</td>
                                            <td>
                                                <a href="@Url.Action("Details", "Bus", new { id = bus.Id })"
                                                   class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="glyphicon glyphicon-eye-open"></i>
                                                </a>
                                                @if (bus.IsAvailable)
                                                {
                                                    <a href="@Url.Action("Create", "BusApplication", new { busId = bus.Id })"
                                                       class="btn btn-sm btn-success" title="申请用车">
                                                        <i class="glyphicon glyphicon-plus"></i>
                                                    </a>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        @if (ViewBag.TotalPages > 1)
                        {
                            <nav aria-label="分页导航">
                                <ul class="pagination">
                                    @if (ViewBag.HasPreviousPage)
                                    {
                                        <li>
                                            <a href="@Url.Action("Index", new { pageIndex = ViewBag.PageIndex - 1, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status })" aria-label="上一页">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    }

                                    @for (int i = 1; i <= ViewBag.TotalPages; i++)
                                    {
                                        <li class="@(i == ViewBag.PageIndex ? "active" : "")">
                                            <a href="@Url.Action("Index", new { pageIndex = i, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status })">@i</a>
                                        </li>
                                    }

                                    @if (ViewBag.HasNextPage)
                                    {
                                        <li>
                                            <a href="@Url.Action("Index", new { pageIndex = ViewBag.PageIndex + 1, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status })" aria-label="下一页">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h4>暂无数据</h4>
                            <p>没有找到符合条件的公车信息</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusClass(ceshi_keshan_0315.Models.BusStatus status)
    {
        switch (status)
        {
            case ceshi_keshan_0315.Models.BusStatus.Available:
                return "label-success";
            case ceshi_keshan_0315.Models.BusStatus.InUse:
                return "label-warning";
            case ceshi_keshan_0315.Models.BusStatus.Maintenance:
                return "label-danger";
            case ceshi_keshan_0315.Models.BusStatus.Retired:
                return "label-default";
            default:
                return "label-default";
        }
    }
}

    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔗 功能测试</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h4>基础功能</h4>
                    <a href="/Bus/ExportToExcel" class="btn btn-success btn-block">导出CSV文件</a>
                    <a href="/Test/Index" class="btn btn-info btn-block">路由测试页面</a>
                </div>
                <div class="col-md-6">
                    <h4>导航链接</h4>
                    <a href="/BusApplication/Create" class="btn btn-primary btn-block">创建申请</a>
                    <a href="/home/<USER>" class="btn btn-default btn-block">返回原系统</a>
                </div>
            </div>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">公车列表 <span class="badge">@ViewBag.TotalCount</span></h3>
            </div>
            <div class="panel-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>车牌号</th>
                            <th>车型</th>
                            <th>品牌</th>
                            <th>状态</th>
                            <th>座位数</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var bus in Model)
                        {
                            <tr>
                                <td><strong>@bus.PlateNumber</strong></td>
                                <td>@bus.Model</td>
                                <td>@bus.Brand</td>
                                <td><span class="label label-success">@bus.StatusText</span></td>
                                <td>@bus.SeatCount 座</td>
                                <td>@bus.Remarks</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <h4>暂无数据</h4>
            <p>没有找到公车信息，这是正常的测试状态。</p>
        </div>
    }

    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">✅ 整合成功</h3>
        </div>
        <div class="panel-body">
            <p><strong>恭喜！</strong>公车管理模块已成功整合到主项目中。</p>
            <p><strong>当前访问路径：</strong> <code>/Bus/Index</code></p>
            <p><strong>控制器：</strong> <code>ceshi_keshan_0315.Controllers.BusController</code></p>
            <p><strong>视图：</strong> <code>Views/Bus/Index.cshtml</code></p>
            <hr>
            <p><strong>可用功能：</strong></p>
            <ul>
                <li>✅ 公车列表查看</li>
                <li>✅ CSV 文件导出</li>
                <li>✅ 路由测试</li>
                <li>✅ 基础功能验证</li>
            </ul>
        </div>
    </div>
</div>
