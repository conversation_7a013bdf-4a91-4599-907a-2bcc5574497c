﻿@model List<ceshi_keshan_0315.ViewModels.BusVM>
@{
    ViewBag.Title = "公车信息管理";
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - 公车管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/Site.css" rel="stylesheet" />

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        .main-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-header h1 {
            margin: 0;
            font-weight: 300;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .panel {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
        }
        .btn-group-custom {
            margin-bottom: 20px;
        }
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stats-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #007bff;
        }
        .footer-custom {
            margin-top: 50px;
            padding: 20px 0;
            background: #fff;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>

    <!-- 页面头部 -->
    <div class="main-header">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1><i class="glyphicon glyphicon-road"></i> @ViewBag.Title</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">管理公车基础信息，查看车辆状态和使用情况</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="container">
        <div class="breadcrumb-custom">
            <ol class="breadcrumb" style="margin: 0; background: none;">
                <li><a href="/home/<USER>"><i class="glyphicon glyphicon-home"></i> 首页</a></li>
                <li class="active">公车信息管理</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <!-- 统计卡片 -->
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number">@(ViewBag.TotalCount ?? 0)</div>
                    <div class="text-muted">总车辆数</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-success">@(Model?.Count(b => b.Status == ceshi_keshan_0315.Models.BusStatus.Available) ?? 0)</div>
                    <div class="text-muted">可用车辆</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-warning">@(Model?.Count(b => b.Status == ceshi_keshan_0315.Models.BusStatus.InUse) ?? 0)</div>
                    <div class="text-muted">使用中</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="stats-number text-danger">@(Model?.Count(b => b.Status == ceshi_keshan_0315.Models.BusStatus.Maintenance) ?? 0)</div>
                    <div class="text-muted">维修中</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success">
                    <strong>成功：</strong> @TempData["SuccessMessage"]
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @TempData["ErrorMessage"]
                </div>
            }

                <!-- 功能按钮 -->
                <div class="btn-group-custom">
                    <a href="@Url.Action("ExportToExcel", "Bus")" class="btn btn-success btn-lg">
                        <i class="glyphicon glyphicon-download-alt"></i> 导出CSV
                    </a>
                    <a href="@Url.Action("Create", "BusApplication")" class="btn btn-primary btn-lg">
                        <i class="glyphicon glyphicon-plus"></i> 创建申请
                    </a>
                    <a href="@Url.Action("Index", "BusApplication")" class="btn btn-info btn-lg">
                        <i class="glyphicon glyphicon-list"></i> 申请列表
                    </a>
                    <a href="@Url.Action("Review", "BusApplication")" class="btn btn-warning btn-lg">
                        <i class="glyphicon glyphicon-check"></i> 申请审批
                    </a>
                    <a href="/home/<USER>" class="btn btn-default btn-lg">
                        <i class="glyphicon glyphicon-arrow-left"></i> 返回原系统
                    </a>
                </div>

                <!-- 数据表格 -->
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="glyphicon glyphicon-list"></i> 公车列表
                            <span class="badge pull-right">共 @(ViewBag.TotalCount ?? 0) 辆</span>
                        </h4>
                    </div>
                    <div class="panel-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>车型</th>
                                        <th>品牌</th>
                                        <th>状态</th>
                                        <th>座位数</th>
                                        <th>购买日期</th>
                                        <th>备注</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var bus in Model)
                                    {
                                        <tr>
                                            <td><strong>@bus.PlateNumber</strong></td>
                                            <td>@bus.Model</td>
                                            <td>@bus.Brand</td>
                                            <td>
                                                <span class="label label-success">
                                                    @bus.StatusText
                                                </span>
                                            </td>
                                            <td>@bus.SeatCount 座</td>
                                            <td>@bus.PurchaseDate.ToString("yyyy-MM-dd")</td>
                                            <td>@bus.Remarks</td>
                                            <td>
                                                <a href="@Url.Action("Details", "Bus", new { id = bus.Id })"
                                                   class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="glyphicon glyphicon-eye-open"></i> 详情
                                                </a>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h4><i class="glyphicon glyphicon-info-sign"></i> 暂无数据</h4>
                            <p>没有找到符合条件的公车信息</p>
                            <a href="@Url.Action("Create", "BusApplication")" class="btn btn-primary">
                                <i class="glyphicon glyphicon-plus"></i> 创建第一个申请
                            </a>
                        </div>
                    }
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面底部 -->
    <div class="footer-custom">
        <div class="container">
            <p>&copy; @DateTime.Now.Year 公车管理系统 - 让出行更便捷</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 表格行点击效果
            $('.table tbody tr').hover(
                function() { $(this).addClass('info'); },
                function() { $(this).removeClass('info'); }
            );

            // 状态标签颜色优化
            $('.table tbody tr').each(function() {
                var statusText = $(this).find('td:eq(3) .label').text().trim();
                var label = $(this).find('td:eq(3) .label');

                if (statusText === '可用') {
                    label.removeClass('label-success').addClass('label-success');
                } else if (statusText === '使用中') {
                    label.removeClass('label-success').addClass('label-warning');
                } else if (statusText === '维修中') {
                    label.removeClass('label-success').addClass('label-danger');
                }
            });

            // 自动隐藏提示消息
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);

            // 统计卡片动画效果
            $('.stats-card').hover(
                function() { $(this).css('transform', 'translateY(-5px)'); },
                function() { $(this).css('transform', 'translateY(0)'); }
            );
        });
    </script>
</body>
</html>
