﻿

@{
    ViewBag.Title = "分公司团购订单查询";
}

<h2>海信团购单预警信息查询</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div class="pull-left">
                内部订单号：<input type="text" name="billno" id="billno" value="" size="20" />
                &nbsp;
                <input id="Button1" type="button" value="团购单预警信息查询" />
                &nbsp;<input id="btn" type="submit" value="团购单预警信息导出" />
                &nbsp;<input id="btn_tongbu" type="submit" value="团购单-同步" />
            </div>
            <div class="pull-right"><input id="btn_xiangyan_cx" type="button" value="香烟分类占比查询" /></div>
        </form>
        </n>
        <div id="div1"></div>
        <br /><br /><br /><br />
        <table id="shuju_1" border=1></table>
        <!--caption>团购订单预警信息查询报表</caption-->
        @*<tr>
                <td>billno订单号</td>
                <td>商品序号</td>
                <td>海信内码</td>
                <td>海信编码</td>
                <td>商品名称</td>
                <td>商品编码</td>
                <td>小类</td>
                <td>中类</td>
                <td>小类</td>
                <td>规格</td>
                <td>单位</td>
                <td>箱装单位</td>
                <td>商品数量</td>
                <td>团购数量</td>
                <td>库存成本价（含税）</td>
                <td>合同最新成本价（含税）</td>
                <td>系统售价</td>
                <td>团购申请价</td>
                <td>团购规定价（含税）</td>
                <td>团购价差</td>
                <td>库存成本团购单位毛利（不含税）</td>
                <td>最新成本团购单位毛利（不含税）</td>
                <td>应收金额</td>
                <td>实收金额</td>
                <td>规定团购价销售金额（不含税）</td>
                <td>实际团购价销售金额（不含税）</td>
                <td>实际比规定团购价增减额（含税）</td>
                <td>库存成本价销售毛利额</td>
                <td>最新成本价销售毛利额</td>
                <td>库存成本价销售毛利率</td>
                <td>最新成本价销售毛利率</td>
                <td>税率</td>
                <td>销项税额</td>
                <td>预估成本</td>
                <td>预估毛利</td>
                <td>预估毛利率</td>
                <td>HCOST</td>
            </tr>*@
    </div>
</body>
</html>

<script>
    //$(document).ready(function)   当 DOM（document object model 文档对象模型）加载完毕且页面完全加载（包括图像）时发生 ready 事件
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/HX_tuangoudan_chaxun?billno=" + $('#billno').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><th>店铺编码</th><th>店铺名称</th><th>商品序号</th><th>海信编码</th><th>商品编码</th><th>商品名称</th><th>香烟类别</th><th>大类</th><th>中类</th><th>小类</th><th>规格</th><th>单位</th><th>箱装单位</th><th>商品数量</th><th>团购数量</th><th>系统售价</th><th>团购申请价</th><th>团购规定价</th><th>实际团购价格差异</th><th>规定团购价销售金额（不含税）</th><th>实际团购价销售金额（不含税）</th><th>实际减去规定团购金额（不含税）</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.orgcode + '</td><td>' + item.orgname + '</td><td>'
                            + item.SERIALNO + '</td><td>' + item.PLUCODE + '</td><td>' + item.BARCODE + '</td><td>'
                            + item.PLUNAME + '</td><td>'  + item.LEIBIE_YAN + '</td><td>'
                            + item.dalei_name + '</td ><td>' + item.zhonglei_name + '</td ><td>' + item.xiaolei_name + '</td ><td>'
                            + item.SPEC + '</td><td>' + item.UNIT + '</td><td>' + item.PACKUNIT + '</td ><td>'
                            + item.PACKQTY + '</td><td>' + item.PFCOUNT + '</td><td>'
                            //+ item.HJPRICE + '</td><td>' + item.HETONG_price + '</td ><td>'   <th>库存成本价（含税）</th><th>合同最新成本价（含税）</th>
                            + item.PRICE + '</td><td>'
                            // + item.PFPRICE + '</td><td>'   <th>团购成本价</th>
                            + item.guiding_TG_price + '</td ><td>' + item.shiji_price + '</td ><td>'
                            + item.TGJC + '</td><td>'        
                            + item.W + '</td ><td>' + item.X + '</td ><td>' + item.Y 
                            //+ item.XTAXTOTAL + '</td><td>' + item.HJTOTAL + '</td><td>' + item.HMLTOTAL + '</td><td>' + item.HMLRATE + '</td><td>' + item.HCOST
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        $('#btn_tongbu').click(function () {
            //$.getJSON("/api/values/HX_tuangoudan_tongbu?billno=" + $('#billno').val(), function (data) { }
            $("#form1").attr("action", "/Home/HX_tuangoudan_tongbu_Exta");//根据促发表单，把action 替换成后面的路劲
            //这个是同步的功能

            $('#btn_tongbu').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
         });

        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/HX_tuangoudan_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

        $('#btn_xiangyan_cx').click(function () {

            $.getJSON("/api/values/HX_tuangoudan_yancao?billno=" + $('#billno').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><th>billno订单号</th><th>香烟类别</th><th>订单库存数量</th><th>订单库存（总）占比</th><th>订单库存（总）</th><th>实际库存数量</th></tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.billno + '</td><td>' + item.leibie_yan + '</td><td>'
                            //+ item.UNIT + '</td ><td>'
                            + item.pfcount + '</td><td>' + item.sum_pfcount_zhanbi + '%</td ><td>' + item.sum_pfcount + '</td ><td>'
                            + item.SUM_KCCOUNT  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });
    });
</script>
