using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.Models;
using ceshi_keshan_0315.ViewModels;
using System.IO;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <param name="plateNumber">车牌号筛选</param>
        /// <param name="status">状态筛选</param>
        /// <param name="purchaseDate">购买日期筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewBag.Title = "公车信息管理";
            ViewBag.Message = "✅ 公车管理模块路由测试成功！";
            ViewBag.CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // 简化的测试数据
            var busVMs = new System.Collections.Generic.List<BusVM>();

            try
            {
                busVMs.Add(new BusVM
                {
                    Id = 1,
                    PlateNumber = "粤A12345",
                    Model = "丰田凯美瑞",
                    Brand = "丰田",
                    Status = BusStatus.Available,
                    StatusText = "可用",
                    SeatCount = 5,
                    PurchaseDate = DateTime.Now.AddYears(-2),
                    Remarks = "测试车辆1"
                });
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "创建测试数据时发生错误：" + ex.Message;
            }

            ViewBag.TotalCount = busVMs.Count;
            return View(busVMs);
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                // 临时测试数据
                var busVM = new BusVM
                {
                    Id = id,
                    PlateNumber = "粤A12345",
                    Model = "丰田凯美瑞",
                    Brand = "丰田",
                    Status = BusStatus.Available,
                    StatusText = "可用",
                    SeatCount = 5,
                    PurchaseDate = DateTime.Now.AddYears(-2),
                    Remarks = "测试车辆详情"
                };

                return View(busVM);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取公车详情时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 导出公车信息到Excel（简化版本）
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportToExcel()
        {
            try
            {
                // 临时：返回CSV格式，避免NPOI依赖问题
                var csvContent = "车牌号,车型,品牌,状态,座位数,购买日期,备注\n";
                csvContent += "粤A12345,丰田凯美瑞,丰田,可用,5," + DateTime.Now.AddYears(-2).ToString("yyyy-MM-dd") + ",测试车辆1\n";
                csvContent += "粤A67890,本田雅阁,本田,使用中,5," + DateTime.Now.AddYears(-1).ToString("yyyy-MM-dd") + ",测试车辆2\n";

                var fileName = $"公车清单_{DateTime.Now:yyyyMMdd}.csv";
                var bytes = System.Text.Encoding.UTF8.GetBytes(csvContent);

                return File(bytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "导出文件时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(BusStatus status)
        {
            switch (status)
            {
                case BusStatus.Available:
                    return "可用";
                case BusStatus.InUse:
                    return "使用中";
                case BusStatus.Maintenance:
                    return "维修中";
                case BusStatus.Retired:
                    return "已报废";
                default:
                    return "未知";
            }
        }
    }
}
