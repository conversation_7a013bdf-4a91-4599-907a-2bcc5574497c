using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.App_Start;
using ceshi_keshan_0315.Models;
using ceshi_keshan_0315.ViewModels;
using NPOI.XSSF.UserModel;
using System.IO;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <param name="plateNumber">车牌号筛选</param>
        /// <param name="status">状态筛选</param>
        /// <param name="purchaseDate">购买日期筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <returns></returns>
        public ActionResult Index(string plateNumber = "", BusStatus? status = null, 
            DateTime? purchaseDate = null, int pageIndex = 1)
        {
            ViewBag.Title = "公车信息管理";
            
            try
            {
                // 临时：创建测试数据，避免数据库连接问题
                var busVMs = new System.Collections.Generic.List<BusVM>
                {
                    new BusVM
                    {
                        Id = 1,
                        PlateNumber = "粤A12345",
                        Model = "丰田凯美瑞",
                        Brand = "丰田",
                        Status = BusStatus.Available,
                        StatusText = "可用",
                        SeatCount = 5,
                        PurchaseDate = DateTime.Now.AddYears(-2),
                        Remarks = "测试车辆1"
                    },
                    new BusVM
                    {
                        Id = 2,
                        PlateNumber = "粤A67890",
                        Model = "本田雅阁",
                        Brand = "本田",
                        Status = BusStatus.InUse,
                        StatusText = "使用中",
                        SeatCount = 5,
                        PurchaseDate = DateTime.Now.AddYears(-1),
                        Remarks = "测试车辆2"
                    }
                };

                // 分页信息
                ViewBag.TotalCount = busVMs.Count;
                ViewBag.PageIndex = pageIndex;
                ViewBag.PageSize = 20;
                ViewBag.TotalPages = 1;
                ViewBag.HasPreviousPage = false;
                ViewBag.HasNextPage = false;

                // 筛选条件回传
                ViewBag.PlateNumber = plateNumber;
                ViewBag.Status = status;
                ViewBag.PurchaseDate = purchaseDate?.ToString("yyyy-MM-dd");

                return View(busVMs);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "查询公车信息时发生错误：" + ex.Message;
                return View(new System.Collections.Generic.List<BusVM>());
            }
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                // 临时测试数据
                var busVM = new BusVM
                {
                    Id = id,
                    PlateNumber = "粤A12345",
                    Model = "丰田凯美瑞",
                    Brand = "丰田",
                    Status = BusStatus.Available,
                    StatusText = "可用",
                    SeatCount = 5,
                    PurchaseDate = DateTime.Now.AddYears(-2),
                    Remarks = "测试车辆详情"
                };

                return View(busVM);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取公车详情时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 导出公车信息到Excel
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportToExcel()
        {
            try
            {
                // 临时测试数据
                var buses = new[]
                {
                    new { PlateNumber = "粤A12345", Model = "丰田凯美瑞", Brand = "丰田", Status = "可用", SeatCount = 5, PurchaseDate = DateTime.Now.AddYears(-2), Remarks = "测试车辆1" },
                    new { PlateNumber = "粤A67890", Model = "本田雅阁", Brand = "本田", Status = "使用中", SeatCount = 5, PurchaseDate = DateTime.Now.AddYears(-1), Remarks = "测试车辆2" }
                };

                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("公车清单");

                // 创建标题行
                var headerRow = sheet.CreateRow(0);
                var headers = new[] { "车牌号", "车型", "品牌", "状态", "座位数", "购买日期", "备注" };
                for (int i = 0; i < headers.Length; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(headers[i]);
                }

                // 填充数据
                for (int i = 0; i < buses.Length; i++)
                {
                    var row = sheet.CreateRow(i + 1);
                    var bus = buses[i];
                    
                    row.CreateCell(0).SetCellValue(bus.PlateNumber);
                    row.CreateCell(1).SetCellValue(bus.Model ?? "");
                    row.CreateCell(2).SetCellValue(bus.Brand ?? "");
                    row.CreateCell(3).SetCellValue(bus.Status);
                    row.CreateCell(4).SetCellValue(bus.SeatCount);
                    row.CreateCell(5).SetCellValue(bus.PurchaseDate.ToString("yyyy-MM-dd"));
                    row.CreateCell(6).SetCellValue(bus.Remarks ?? "");
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 返回文件流
                var stream = new MemoryStream();
                workbook.Write(stream);
                var fileName = $"公车清单_{DateTime.Now:yyyyMMdd}.xlsx";
                
                return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "导出Excel时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(BusStatus status)
        {
            return status switch
            {
                BusStatus.Available => "可用",
                BusStatus.InUse => "使用中",
                BusStatus.Maintenance => "维修中",
                BusStatus.Retired => "已报废",
                _ => "未知"
            };
        }
    }
}
