using System;
using System.Web.Mvc;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewBag.Title = "公车信息管理";
            ViewBag.Message = "✅ 公车管理模块路由测试成功！";
            ViewBag.CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.TotalCount = 0;

            return Content("✅ BusController.Index 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            return Content($"✅ BusController.Details 工作正常！ID: {id}，时间：{DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        }

        /// <summary>
        /// 导出公车信息到CSV
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportToExcel()
        {
            return Content("✅ BusController.ExportToExcel 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }
}
