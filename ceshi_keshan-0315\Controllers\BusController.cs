using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.Models;
using ceshi_keshan_0315.ViewModels;
using System.IO;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <param name="plateNumber">车牌号筛选</param>
        /// <param name="status">状态筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <returns></returns>
        public ActionResult Index(string plateNumber = "", BusStatus? status = null, int pageIndex = 1)
        {
            ViewBag.Title = "公车信息管理";

            try
            {
                // 创建测试数据（实际项目中应该从数据库获取）
                var busVMs = new System.Collections.Generic.List<BusVM>
                {
                    new BusVM
                    {
                        Id = 1,
                        PlateNumber = "粤A12345",
                        Model = "丰田凯美瑞",
                        Brand = "丰田",
                        Status = BusStatus.Available,
                        StatusText = "可用",
                        SeatCount = 5,
                        PurchaseDate = DateTime.Now.AddYears(-2),
                        Remarks = "公务用车"
                    },
                    new BusVM
                    {
                        Id = 2,
                        PlateNumber = "粤A67890",
                        Model = "本田雅阁",
                        Brand = "本田",
                        Status = BusStatus.InUse,
                        StatusText = "使用中",
                        SeatCount = 5,
                        PurchaseDate = DateTime.Now.AddYears(-1),
                        Remarks = "领导专车"
                    },
                    new BusVM
                    {
                        Id = 3,
                        PlateNumber = "粤A11111",
                        Model = "大众帕萨特",
                        Brand = "大众",
                        Status = BusStatus.Maintenance,
                        StatusText = "维修中",
                        SeatCount = 5,
                        PurchaseDate = DateTime.Now.AddYears(-3),
                        Remarks = "定期保养"
                    }
                };

                // 应用筛选条件
                if (!string.IsNullOrEmpty(plateNumber))
                {
                    busVMs = busVMs.Where(b => b.PlateNumber.Contains(plateNumber)).ToList();
                }

                if (status.HasValue)
                {
                    busVMs = busVMs.Where(b => b.Status == status.Value).ToList();
                }

                // 分页信息
                ViewBag.TotalCount = busVMs.Count;
                ViewBag.PageIndex = pageIndex;
                ViewBag.PageSize = 20;
                ViewBag.TotalPages = (int)Math.Ceiling((double)busVMs.Count / 20);
                ViewBag.HasPreviousPage = pageIndex > 1;
                ViewBag.HasNextPage = pageIndex < ViewBag.TotalPages;

                // 筛选条件回传
                ViewBag.PlateNumber = plateNumber;
                ViewBag.Status = status;

                return View(busVMs);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "查询公车信息时发生错误：" + ex.Message;
                return View(new System.Collections.Generic.List<BusVM>());
            }
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                // 创建测试数据（实际项目中应该从数据库获取）
                var busVM = new BusVM
                {
                    Id = id,
                    PlateNumber = "粤A12345",
                    Model = "丰田凯美瑞",
                    Brand = "丰田",
                    Status = BusStatus.Available,
                    StatusText = "可用",
                    SeatCount = 5,
                    PurchaseDate = DateTime.Now.AddYears(-2),
                    Remarks = "公务用车，状态良好"
                };

                return View(busVM);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取公车详情时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 导出公车信息到CSV
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportToExcel()
        {
            try
            {
                // 获取数据（实际项目中应该从数据库获取）
                var buses = new[]
                {
                    new { PlateNumber = "粤A12345", Model = "丰田凯美瑞", Brand = "丰田", Status = "可用", SeatCount = 5, PurchaseDate = DateTime.Now.AddYears(-2), Remarks = "公务用车" },
                    new { PlateNumber = "粤A67890", Model = "本田雅阁", Brand = "本田", Status = "使用中", SeatCount = 5, PurchaseDate = DateTime.Now.AddYears(-1), Remarks = "领导专车" },
                    new { PlateNumber = "粤A11111", Model = "大众帕萨特", Brand = "大众", Status = "维修中", SeatCount = 5, PurchaseDate = DateTime.Now.AddYears(-3), Remarks = "定期保养" }
                };

                // 生成CSV内容
                var csvContent = "车牌号,车型,品牌,状态,座位数,购买日期,备注\n";
                foreach (var bus in buses)
                {
                    csvContent += string.Format("{0},{1},{2},{3},{4},{5},{6}\n",
                        bus.PlateNumber, bus.Model, bus.Brand, bus.Status,
                        bus.SeatCount, bus.PurchaseDate.ToString("yyyy-MM-dd"), bus.Remarks);
                }

                var fileName = string.Format("公车清单_{0}.csv", DateTime.Now.ToString("yyyyMMdd"));
                var bytes = System.Text.Encoding.UTF8.GetBytes(csvContent);

                return File(bytes, "text/csv", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "导出文件时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(BusStatus status)
        {
            switch (status)
            {
                case BusStatus.Available:
                    return "可用";
                case BusStatus.InUse:
                    return "使用中";
                case BusStatus.Maintenance:
                    return "维修中";
                case BusStatus.Retired:
                    return "已报废";
                default:
                    return "未知";
            }
        }
    }
}
