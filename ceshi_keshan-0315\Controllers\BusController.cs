using System;
using System.Web.Mvc;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <returns></returns>
        public string Index()
        {
            return "✅ BusController.Index 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public string Details(int id)
        {
            return "✅ BusController.Details 工作正常！ID: " + id + "，时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        public string ExportToExcel()
        {
            return "✅ BusController.ExportToExcel 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
