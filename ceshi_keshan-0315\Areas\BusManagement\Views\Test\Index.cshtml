@{
    ViewBag.Title = ViewBag.Title ?? "路由测试页面";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <title>@ViewBag.Title</title>
    <link href="~/Content/bootstrap.css" rel="stylesheet" />
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .success { color: #28a745; font-weight: bold; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="success">✅ Area 路由测试成功！</h1>
        <p class="info">如果您看到这个页面，说明 BusManagement Area 的路由配置正常工作。</p>

        <div class="test-section">
            <h3>🎯 路由信息</h3>
            <table class="table table-bordered">
                <tr>
                    <td><strong>Area</strong></td>
                    <td class="code">@ViewBag.AreaName</td>
                </tr>
                <tr>
                    <td><strong>Controller</strong></td>
                    <td class="code">@ViewBag.ControllerName</td>
                </tr>
                <tr>
                    <td><strong>Action</strong></td>
                    <td class="code">@ViewBag.ActionName</td>
                </tr>
                <tr>
                    <td><strong>当前时间</strong></td>
                    <td class="code">@ViewBag.CurrentTime</td>
                </tr>
                @if (ViewBag.TestId != null)
                {
                    <tr>
                        <td><strong>测试ID</strong></td>
                        <td class="code">@ViewBag.TestId</td>
                    </tr>
                }
                @if (ViewBag.TestName != null)
                {
                    <tr>
                        <td><strong>测试名称</strong></td>
                        <td class="code">@ViewBag.TestName</td>
                    </tr>
                }
            </table>
        </div>

        <div class="test-section">
            <h3>🔗 测试链接</h3>
            <div class="row">
                <div class="col-md-6">
                    <h4>Area 内部链接</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="/BusManagement/Test/Index">测试首页</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/BusManagement/Test/TestParams/123?name=测试参数">参数测试</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/BusManagement/Test/RouteInfo">路由信息 JSON</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/BusManagement/Bus/Index">公车管理页面</a>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>外部链接</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="/home/<USER>">返回原系统首页</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/api/values/chaxun_daozhang_yujin?workday=2023-06-12&quyuid=&jine=1000000">原系统API测试</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/Help">API帮助页面</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 AJAX 测试</h3>
            <button id="testAjax" class="btn btn-primary">测试 AJAX 调用</button>
            <div id="ajaxResult" class="code" style="margin-top: 10px; display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📋 路由配置说明</h3>
            <p><strong>当前 URL 模式：</strong></p>
            <div class="code">
                BusManagement/{controller}/{action}/{id}
            </div>
            <p><strong>匹配的路由：</strong></p>
            <div class="code">
                BusManagement_default (在 BusManagementAreaRegistration.cs 中定义)
            </div>
            <p><strong>控制器命名空间：</strong></p>
            <div class="code">
                ceshi_keshan_0315.Areas.BusManagement.Controllers
            </div>
        </div>

        <div class="alert alert-success">
            <h4>✅ 路由测试结论</h4>
            <p>如果您能看到这个页面，说明以下配置都是正确的：</p>
            <ul>
                <li>Global.asax.cs 中的 AreaRegistration.RegisterAllAreas() 调用</li>
                <li>BusManagementAreaRegistration.cs 的路由配置</li>
                <li>控制器的命名空间和类名</li>
                <li>视图文件的位置和配置</li>
            </ul>
        </div>
    </div>

    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script>
        $(document).ready(function() {
            $('#testAjax').click(function() {
                $.get('/BusManagement/Test/RouteInfo', function(data) {
                    $('#ajaxResult').html(JSON.stringify(data, null, 2)).show();
                }).fail(function() {
                    $('#ajaxResult').html('AJAX 调用失败').show();
                });
            });
        });
    </script>
</body>
</html>
