﻿
@{
    ViewBag.Title = "lingshou_fengongsi_HS_baobiao";
}

<h2>lingshou_fengongsi_HS_baobiao</h2>

@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            日期：<input type="text" name="workday" id="workday" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
            <input id="Button1" type="button" value="查询" />
            &nbsp;<input id="btn" type="submit" value="导出" />
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>



<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            $.getJSON("/api/values/lingshou_fengongsi_HS_baobiao?workday=" + $('#workday').val(), function (data) {   //$('#workday').html()  取页面信息值

                var jsonobj = $.parseJSON(data);//转化data的格式
                var htmltable = "";
                htmltable += '<tr><td>分公司</td><td>当月计划量</td><td>当月应日均</td><td>当月实际日均</td><td>当月应完成进度</td><td>当月实际完成进度</td><td>当月欠进度</td><td>当月累日销量合计</td><td>当月累日销量92#</td><td>当月累日销量95#</td><td>当月累日销量98#</td><td>当月累日销量0#</td><td>当月累日销量燃料油</td><td>当月累日销量CNG</td><td>当月累日销量LNG</td><td>当日销量合计</td><td>当日销量92#</td><td>当日销量95#</td><td>当日销量98#</td><td>当日销量dr0</td><td>当日销量燃料油</td><td>当日销量CNG</td><td>当日销量LNG</td><td>同比情况同期销量总量</td><td>同比情况同期销量92#</td><td>同比情况同期销量95#</td><td>同比情况同期销量98#</td><td>同比情况同期销量0#</td><td>同比情况同期销量燃料油</td><td>同比增幅总量</td><td>同比增幅汽油</td><td>同比增幅柴油</td><td>同比增幅燃料油</td><td>同比总量</td><td>同比汽油</td><td>同比柴油</td><td>同比燃料油</td><td>上月环比总销量</td><td>上月环比92#销量</td><td>上月环比95#销量</td><td>上月环比92#销量</td><td>上月环比燃料油销量</td><td>环比增幅总量</td><td>环比增幅汽油</td><td>环比增幅柴油</td><td>环比增幅燃料油</td><td>环比总量</td><td>环比汽油</td><td>环比柴油</td><td>环比燃料油</td><td>状态</tr>';
                $.each(jsonobj, function (i,item) {
                    htmltable += '<tr><td>' + item.BigAreaname + '</td><td>' + item.dyMonSalesPlan + '</td><td>' + item.dyyrj + '</td><td>' + item.dysjrj
                        + '</td><td>' + item.dyywcjd + '%</td><td>' + item.dysjwcjd + '%</td><td>' + item.dyqjd + '%</td><td>' + item.MonthlySales
                        + '</td><td>' + item.lr92 + '</td><td>' + item.lr95 + '</td><td>' + item.lr98 + '</td><td>' + item.lr0
                        + '</td><td>' + item.lrrly + '</td><td>' + item.lrCNG + '</td><td>' + item.lrLNG + '</td><td>' + item.SalesVol
                        + '</td><td>' + item.dr92 + '</td><td>' + item.dr95 + '</td><td>' + item.dr98 + '</td><td>' + item.dr0
                        + '</td><td>' + item.drrly + '</td><td>' + item.drCNG + '</td><td>' + item.drLNG + '</td><td>' + item.tqMonthlySales
                        + '</td><td>' + item.tq92 + '</td><td>' + item.tq95 + '</td><td>' + item.tq98 + '</td><td>' + item.tq0
                        + '</td><td>' + item.tqrly + '</td><td>' + item.tbzfzl + '</td><td>' + item.tbzfqy + '</td><td>' + item.tbzfcy
                        + '</td><td>' + item.tbzfrly + '</td><td>' + item.tbzl + '%</td><td>' + item.tbqy + '%</td><td>' + item.tbcy
                        + '%</td><td>' + item.tbrly + '%</td><td>' + item.syMonthlySales + '</td><td>' + item.sy92 + '</td><td>' + item.sy95
                        + '</td><td>' + item.sy98 + '</td><td>' + item.sy0 + '</td><td>' + item.syrly + '</td><td>' + item.hbzfzl
                        + '</td><td>' + item.hbzfqy + '</td><td>' + item.hbzfcy + '</td><td>' + item.hbzfrly + '</td><td>' + item.hbzl
                        + '%</td><td>' + item.hbqy + '%</td><td>' + item.hbcy + '%</td><td>' + item.hbrly + '%</tr>';
                });  //循环each   json数组     <tr>行  <td>列
                $('#shuju_1').html(htmltable);
                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/lingshou_fengongsi_HS_baobiao_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>

