﻿
@{
    ViewBag.Title = "HX_diaobodan";
}


<h2>海信调拨单查询</h2>


<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_shxdbdcx" id="workday_shxdbdcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_dhxdbdcx" id="workday_dhxdbdcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/HX_diaobodan?workday_shxdbdcx=" + $('#workday_shxdbdcx').val() + "&workday_dhxdbdcx=" + $('#workday_dhxdbdcx').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>单据号</td><td>录入日期</td><td>录入人ID</td><td>录入人编码</td><td>录入人姓名</td><td>记账日期'
                        + '</td><td>记账人ID</td><td>记账人编码</td><td>记账人姓名</td><td>报表日期</td><td>经手人ID</td><td>经手人编码'
                        + '</td><td>经手人姓名</td><td>部门ID</td><td>调入组织编码</td><td>调入组织名称</td><td>调出仓库编码</td><td>调出仓库名称'
                        + '</td><td>调入组织编码</td><td>调入组织名称</td><td>调拨数量</td><td>无税进价金额</td><td>含税进价金额</td><td>备注'
                        + '</td><td>内部组织编码</td><td>调入内部组织编码</td><td>调出调入含税进价金额</td><td>调出调入无税进价金额</td><td>售价金额</td><td>序号'
                        + '</td><td>商品Id</td><td>商品编码</td><td>商品名称</td><td>商品条码</td><td>规格</td><td>单位'
                        + '</td><td>单件件数</td><td>调拨数量</td><td>调出含税进价</td><td>调出无税进价</td><td>售价</td><td>进项税率'
                        + '</td><td>调出含税进价金额</td><td>调出无税进价金额</td><td>调入部门ID</td><td>调入含税进价金额</td><td>调入无税进价金额</td><td>售价金额'
                        + '</td><td>调入含税进价</td><td>调入无税进价</tr>';  
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.BILLNO + '</td><td>' + item.LRDATE + '</td><td>' + item.USERID
                            + '</td><td>' + item.USERCODE + '</td><td>' + item.USERNAME + '</td><td>' + item.JZDATE
                            + '</td><td>' + item.JZRID + '</td><td>' + item.JZRCODE + '</td><td>' + item.JZRNAME
                            + '</td><td>' + item.RPTDATE + '</td><td>' + item.EMPID + '</td><td>' + item.EMPCODE
                            + '</td><td>' + item.EMPNAME + '</td><td>' + item.DEPID + '</td><td>' + item.SHORGCODE
                            + '</td><td>' + item.SHORGNAME + '</td><td>' + item.CKCODE + '</td><td>' + item.CKNAME
                            + '</td><td>' + item.SHORGCODE1 + '</td><td>' + item.SHORGNAME1 + '</td><td>' + item.DBCOUNT
                            + '</td><td>' + item.WCOST + '</td><td>' + item.HCOST + '</td><td>' + item.REMARK
                            + '</td><td>' + item.INORGCODE + '</td><td>' + item.INSHORGCODE + '</td><td>' + item.RHCOST
                            + '</td><td>' + item.RWCOST + '</td><td>' + item.STOTAL + '</td><td>' + item.SERIALNO
                            + '</td><td>' + item.PLUID + '</td><td>' + item.PLUCODE + '</td><td>' + item.PLUNAME
                            + '</td><td>' + item.BARCODE + '</td><td>' + item.SPEC + '</td><td>' + item.UNIT
                            + '</td><td>' + item.SGLCOUNT + '</td><td>' + item.DBCOUNT1 + '</td><td>' + item.HJPRICE
                            + '</td><td>' + item.WJPRICE + '</td><td>' + item.PRICE + '</td><td>' + item.JTAXRATE
                            + '</td><td>' + item.HCOST1 + '</td><td>' + item.WCOST1 + '</td><td>' + item.SHDEPID
                            + '</td><td>' + item.RHCOST1 + '</td><td>' + item.RWCOST1 + '</td><td>' + item.STOTAL1
                            + '</td><td>' + item.RHJPRICE + '</td><td>' + item.RWJPRICE
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/HX_diaobodan_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
