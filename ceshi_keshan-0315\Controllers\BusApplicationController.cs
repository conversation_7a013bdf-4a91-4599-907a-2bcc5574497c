using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.Models;
using ceshi_keshan_0315.ViewModels;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车申请控制器
    /// 新增模块：处理公车申请的创建、查看、审批和管理功能
    /// </summary>
    public class BusApplicationController : Controller
    {
        /// <summary>
        /// 申请列表页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewBag.Title = "公车申请管理";

            try
            {
                // 创建测试数据（实际项目中应该从数据库获取）
                var applications = new System.Collections.Generic.List<BusApplicationVM>
                {
                    new BusApplicationVM
                    {
                        Id = 1,
                        PlateNumber = "粤A12345",
                        Model = "丰田凯美瑞",
                        ApplicantName = "张三",
                        Department = "行政部",
                        Phone = "13800138000",
                        Purpose = "出差办事",
                        Destination = "广州市",
                        StartTime = DateTime.Now.AddDays(1),
                        EndTime = DateTime.Now.AddDays(1).AddHours(8),
                        EstimatedPeople = 3,
                        Status = ApplicationStatus.Pending,
                        StatusText = "待审批",
                        CreateTime = DateTime.Now
                    },
                    new BusApplicationVM
                    {
                        Id = 2,
                        PlateNumber = "粤A67890",
                        Model = "本田雅阁",
                        ApplicantName = "李四",
                        Department = "销售部",
                        Phone = "13900139000",
                        Purpose = "客户拜访",
                        Destination = "深圳市",
                        StartTime = DateTime.Now.AddDays(2),
                        EndTime = DateTime.Now.AddDays(2).AddHours(6),
                        EstimatedPeople = 2,
                        Status = ApplicationStatus.Approved,
                        StatusText = "已批准",
                        ReviewerName = "王经理",
                        ReviewTime = DateTime.Now.AddHours(-2),
                        CreateTime = DateTime.Now.AddHours(-3)
                    }
                };

                return View(applications);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "查询申请记录时发生错误：" + ex.Message;
                return View(new System.Collections.Generic.List<BusApplicationVM>());
            }
        }

        /// <summary>
        /// 创建申请页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Create()
        {
            ViewBag.Title = "创建公车申请";

            try
            {
                // 使用 List<SelectListItem> 替代匿名对象
                var availableBuses = new List<SelectListItem>
                {
                    new SelectListItem { Value = "1", Text = "粤A12345 (丰田凯美瑞, 5座)" },
                    new SelectListItem { Value = "2", Text = "粤A67890 (本田雅阁, 5座)" },
                    new SelectListItem { Value = "3", Text = "粤A11111 (大众帕萨特, 5座)" }
                };

                ViewBag.AvailableBuses = availableBuses;

                return View();
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "加载创建页面时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 提交申请
        /// </summary>
        /// <param name="model">申请信息</param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult Create(BusApplication model)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // 这里应该保存到数据库
                    // 临时返回成功消息
                    TempData["SuccessMessage"] = "申请提交成功，申请ID：" + new Random().Next(1000, 9999);
                    return RedirectToAction("Index");
                }

                // 如果模型验证失败，重新加载页面
                ViewBag.Title = "创建公车申请";
                var availableBuses = new List<SelectListItem>
                {
                    new SelectListItem { Value = "1", Text = "粤A12345 (丰田凯美瑞, 5座)" },
                    new SelectListItem { Value = "2", Text = "粤A67890 (本田雅阁, 5座)" },
                    new SelectListItem { Value = "3", Text = "粤A11111 (大众帕萨特, 5座)" }
                };

                ViewBag.AvailableBuses = availableBuses;

                return View(model);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "提交申请时发生错误：" + ex.Message;
                return View(model);
            }
        }

        /// <summary>
        /// 申请详情页面
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                // 创建测试数据（实际项目中应该从数据库获取）
                var application = new BusApplicationVM
                {
                    Id = id,
                    PlateNumber = "粤A12345",
                    Model = "丰田凯美瑞",
                    ApplicantName = "张三",
                    Department = "行政部",
                    Phone = "13800138000",
                    Purpose = "出差办事",
                    Destination = "广州市",
                    StartTime = DateTime.Now.AddDays(1),
                    EndTime = DateTime.Now.AddDays(1).AddHours(8),
                    EstimatedPeople = 3,
                    Status = ApplicationStatus.Pending,
                    StatusText = "待审批",
                    CreateTime = DateTime.Now
                };

                return View(application);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取申请详情时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 审批页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Review()
        {
            ViewBag.Title = "申请审批";

            try
            {
                // 获取待审批的申请（实际项目中应该从数据库获取）
                var pendingApplications = new System.Collections.Generic.List<BusApplicationVM>
                {
                    new BusApplicationVM
                    {
                        Id = 1,
                        PlateNumber = "粤A12345",
                        Model = "丰田凯美瑞",
                        ApplicantName = "张三",
                        Department = "行政部",
                        Purpose = "出差办事",
                        StartTime = DateTime.Now.AddDays(1),
                        EndTime = DateTime.Now.AddDays(1).AddHours(8),
                        EstimatedPeople = 3,
                        CreateTime = DateTime.Now
                    },
                    new BusApplicationVM
                    {
                        Id = 4,
                        PlateNumber = "粤A67890",
                        Model = "本田雅阁",
                        ApplicantName = "赵六",
                        Department = "技术部",
                        Purpose = "技术支持",
                        StartTime = DateTime.Now.AddDays(3),
                        EndTime = DateTime.Now.AddDays(3).AddHours(6),
                        EstimatedPeople = 2,
                        CreateTime = DateTime.Now.AddHours(-1)
                    }
                };

                ViewBag.PendingCount = pendingApplications.Count;
                return View(pendingApplications);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "加载审批页面时发生错误：" + ex.Message;
                return View(new System.Collections.Generic.List<BusApplicationVM>());
            }
        }

        /// <summary>
        /// 审批操作
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <param name="action">操作类型</param>
        /// <param name="comments">审批意见</param>
        /// <returns></returns>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult ReviewAction(int id, string action, string comments)
        {
            try
            {
                // 这里应该更新数据库
                var actionText = action == "approve" ? "批准" : "拒绝";
                TempData["SuccessMessage"] = string.Format("申请#{0} {1}成功", id, actionText);

                return RedirectToAction("Review");
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "审批操作失败：" + ex.Message;
                return RedirectToAction("Review");
            }
        }
    }
}