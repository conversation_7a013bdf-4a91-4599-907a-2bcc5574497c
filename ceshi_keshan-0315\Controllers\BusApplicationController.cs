using System;
using System.Web.Mvc;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 公车申请控制器
    /// 新增模块：处理公车申请的创建、查看和管理功能
    /// </summary>
    public class BusApplicationController : Controller
    {
        /// <summary>
        /// 申请列表页面
        /// </summary>
        /// <returns></returns>
        public string Index()
        {
            return "✅ BusApplicationController.Index 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 创建申请页面
        /// </summary>
        /// <returns></returns>
        public string Create()
        {
            return "✅ BusApplicationController.Create 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 申请详情页面
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns></returns>
        public string Details(int id)
        {
            return "✅ BusApplicationController.Details 工作正常！ID: " + id + "，时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }

        /// <summary>
        /// 审批页面
        /// </summary>
        /// <returns></returns>
        public string Review()
        {
            return "✅ BusApplicationController.Review 工作正常！时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
}
