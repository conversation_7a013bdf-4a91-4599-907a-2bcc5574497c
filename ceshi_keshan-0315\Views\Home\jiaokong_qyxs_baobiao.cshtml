﻿
@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_jml" id="workday_jml" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                -<input type="text" name="workday_d_jml" id="workday_d_jml" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />

                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/jiaokong_qyxs_baobiao?workday_jml=" + $('#workday_jml').val() + "&workday_d_jml=" + $('#workday_d_jml').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>销售组织</td><td>订单类型</td><td>唯一识别号</td><td>单据日期</td><td>客户</td><td>业务员</td><td>部门</td><td>开票客户</td><td>行号</td><td>结算方式</td><td>发货仓库</td><td>物料编码</td><td>体积</td><td>密度</td><td>销售金额</td><td>销售数量</td><td>税率</td><td>汽油通行附加费</td><td>附加费单价</td><td>营业收入(含税)</td><td>税额</td><td>不含税金额</td><td>无税单价</td><td>含税单价</td><td>单品折扣</td><td>折扣额</td></tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.xszz + '</td><td>' + item.ddlx + '</td><td>' + item.wybsh + '</td><td>'
                            + item.WorkDay + '</td><td>' + item.kh + '</td><td>' + item.ywy + '</td><td>'
                            + item.bm + '</td><td>' + item.kpkh + '</td><td>' + item.hangh + '</td><td>'
                            + item.jsfs + '</td><td>' + item.fhck + '</td><td>' + item.fhwl + '</td><td>'
                            + item.xssl + '</td><td>' + item.MD + '</td><td>' + item.xsje + '</td><td>'
                            + item.xskg + '</td><td>' + item.shuilv + '</td><td>' + item.qyfjtxf + '</td><td>'
                            + item.fjfdj + '</td><td>' + item.yysrhs + '</td><td>' + item.shuie + '</td><td>'
                            + item.bhsje + '</td><td>' + item.wsdj + '</td><td>' + item.hsdj + '</td><td>'
                            + item.dpzk + '</td><td>' + item.zke
                            + '</td></tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    $('#shuju_1').html(htmltable);
                }
                else
                { alert("查询为空"); }
                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/jiaokong_qyxs_baobiao_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>


