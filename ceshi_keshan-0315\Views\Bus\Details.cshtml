﻿@model ceshi_keshan_0315.ViewModels.BusVM
@{
    ViewBag.Title = "公车详情";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - 公车管理系统</title>
    
    <!-- Bootstrap CSS -->
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/Site.css" rel="stylesheet" />
    
    <!-- 自定义样式 -->
    <style>
        body { 
            background-color: #f5f5f5; 
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        .main-header {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-header h1 {
            margin: 0;
            font-weight: 300;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .panel {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
        }
        .dl-horizontal dt {
            width: 120px;
            font-weight: 600;
        }
        .dl-horizontal dd {
            margin-left: 140px;
        }
        .status-badge {
            font-size: 1.2em;
            padding: 8px 16px;
        }
        .footer-custom {
            margin-top: 50px;
            padding: 20px 0;
            background: #fff;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>

    <!-- 页面头部 -->
    <div class="main-header">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1><i class="glyphicon glyphicon-road"></i> @ViewBag.Title</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">车牌号：@Model.PlateNumber</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="container">
        <div class="breadcrumb-custom">
            <ol class="breadcrumb" style="margin: 0; background: none;">
                <li><a href="/home/<USER>"><i class="glyphicon glyphicon-home"></i> 首页</a></li>
                <li><a href="/Bus/Index">公车信息管理</a></li>
                <li class="active">公车详情</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-8 col-md-offset-2">

                @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
                {
                    <div class="alert alert-danger alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert">&times;</button>
                        <strong><i class="glyphicon glyphicon-exclamation-sign"></i> 错误：</strong> @ViewBag.ErrorMessage
                    </div>
                }

                <!-- 基本信息 -->
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <i class="glyphicon glyphicon-info-sign"></i> 基本信息
                        </h4>
                    </div>
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="dl-horizontal">
                                    <dt>车牌号：</dt>
                                    <dd><strong class="text-primary">@Model.PlateNumber</strong></dd>
                                    <dt>车型：</dt>
                                    <dd>@Model.Model</dd>
                                    <dt>品牌：</dt>
                                    <dd>@Model.Brand</dd>
                                    <dt>座位数：</dt>
                                    <dd>@Model.SeatCount 座</dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="dl-horizontal">
                                    <dt>当前状态：</dt>
                                    <dd>
                                        @if (Model.Status == ceshi_keshan_0315.Models.BusStatus.Available)
                                        {
                                            <span class="label label-success status-badge">@Model.StatusText</span>
                                        }
                                        else if (Model.Status == ceshi_keshan_0315.Models.BusStatus.InUse)
                                        {
                                            <span class="label label-warning status-badge">@Model.StatusText</span>
                                        }
                                        else if (Model.Status == ceshi_keshan_0315.Models.BusStatus.Maintenance)
                                        {
                                            <span class="label label-danger status-badge">@Model.StatusText</span>
                                        }
                                        else
                                        {
                                            <span class="label label-default status-badge">@Model.StatusText</span>
                                        }
                                    </dd>
                                    <dt>购买日期：</dt>
                                    <dd>@Model.PurchaseDate.ToString("yyyy年MM月dd日")</dd>
                                    <dt>使用年限：</dt>
                                    <dd>@Model.UsageYears 年</dd>
                                    <dt>是否可用：</dt>
                                    <dd>
                                        @if (Model.IsAvailable)
                                        {
                                            <span class="text-success"><i class="glyphicon glyphicon-ok"></i> 可用</span>
                                        }
                                        else
                                        {
                                            <span class="text-danger"><i class="glyphicon glyphicon-remove"></i> 不可用</span>
                                        }
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 备注信息 -->
                @if (!string.IsNullOrEmpty(Model.Remarks))
                {
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <i class="glyphicon glyphicon-comment"></i> 备注信息
                            </h4>
                        </div>
                        <div class="panel-body">
                            <p class="lead">@Model.Remarks</p>
                        </div>
                    </div>
                }

                <!-- 操作按钮 -->
                <div class="panel panel-default">
                    <div class="panel-body text-center">
                        <a href="/Bus/Index" class="btn btn-default btn-lg">
                            <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                        </a>
                        @if (Model.IsAvailable)
                        {
                            <a href="/BusApplication/Create" class="btn btn-primary btn-lg">
                                <i class="glyphicon glyphicon-plus"></i> 申请用车
                            </a>
                        }
                        <a href="/BusApplication/Index" class="btn btn-info btn-lg">
                            <i class="glyphicon glyphicon-list"></i> 申请记录
                        </a>
                        <a href="/Bus/ExportToExcel" class="btn btn-success btn-lg">
                            <i class="glyphicon glyphicon-download-alt"></i> 导出数据
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面底部 -->
    <div class="footer-custom">
        <div class="container">
            <p>&copy; @DateTime.Now.Year 公车管理系统 - 让出行更便捷</p>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script src="~/Scripts/bootstrap.min.js"></script>
    <script>
        $(document).ready(function() {
            // 自动隐藏提示消息
            setTimeout(function() {
                $('.alert').fadeOut('slow');
            }, 5000);
            
            // 状态标签动画效果
            $('.status-badge').hover(
                function() { $(this).css('transform', 'scale(1.1)'); },
                function() { $(this).css('transform', 'scale(1)'); }
            );
        });
    </script>
</body>
</html>
