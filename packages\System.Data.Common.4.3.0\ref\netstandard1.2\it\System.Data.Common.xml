﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Common</name>
  </assembly>
  <members>
    <member name="T:System.DBNull">
      <summary>Rappresenta un valore non esistente.La classe non può essere ereditata.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString">
      <summary>Restituisce una stringa vuota (<see cref="F:System.String.Empty" />).</summary>
      <returns>Una stringa vuota (<see cref="F:System.String.Empty" />).</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString(System.IFormatProvider)">
      <summary>Restituisce una stringa vuota usando il <see cref="T:System.IFormatProvider" /> specificato.</summary>
      <returns>Una stringa vuota (<see cref="F:System.String.Empty" />).</returns>
      <param name="provider">L'oggetto <see cref="T:System.IFormatProvider" /> utilizzato per formattare il valore restituito.- oppure - null per ottenere le informazioni sul formato dall'impostazione locale corrente del sistema operativo. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.DBNull.Value">
      <summary>Rappresenta l'unica istanza della classe <see cref="T:System.DBNull" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.CommandBehavior">
      <summary>Fornisce una descrizione dei risultati della query e dei relativi effetti sul database.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandBehavior.CloseConnection">
      <summary>Durante l'esecuzione del comando, l'oggetto Connection associato viene chiuso alla chiusura dell'oggetto DataReader associato.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.Default">
      <summary>È possibile che la query restituisca più set di risultati.L'esecuzione della query può influire sullo stato del database.Default non imposta flag <see cref="T:System.Data.CommandBehavior" />, quindi chiamare ExecuteReader(CommandBehavior.Default) equivale dal punto di vista funzionale a chiamare ExecuteReader().</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.KeyInfo">
      <summary>La query restituisce informazioni sulle colonne e sulle chiavi primarie. </summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SchemaOnly">
      <summary>La query restituisce solo le informazioni sulla colonna.Se si utilizza il campo <see cref="F:System.Data.CommandBehavior.SchemaOnly" />, il provider di dati .NET Framework Data per SQL Server precede l'istruzione in fase di esecuzione con SET FMTONLY ON.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SequentialAccess">
      <summary>Consente a DataReader di gestire le righe contenenti colonne con valori binari di grandi dimensioni.Anziché caricare l'intera riga, SequentialAccess consente a DataReader di caricare i dati come flusso.È quindi possibile utilizzare il metodo GetBytes o GetChars per specificare una posizione di byte in cui avviare l'operazione di lettura e una dimensione limitata del buffer per i dati restituiti.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleResult">
      <summary>La query restituisce un singolo set di risultati.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleRow">
      <summary>È previsto che la query restituisca una singola riga del primo set di risultati.L'esecuzione della query può influire sullo stato del database.È possibile, ma non necessario, che alcuni provider di dati .NET Framework utilizzino queste informazioni per ottimizzare le prestazioni del comando.Quando si specifica <see cref="F:System.Data.CommandBehavior.SingleRow" /> con il metodo <see cref="M:System.Data.OleDb.OleDbCommand.ExecuteReader" /> dell'oggetto <see cref="T:System.Data.OleDb.OleDbCommand" />, il provider di dati .NET Framework di OLE DB esegue l'associazione mediante l'interfaccia IRow OLE DB, se disponibile.In caso contrario, utilizza l'interfaccia IRowset.Se si prevede che l'istruzione SQL restituisca solo una singola riga, è possibile specificare <see cref="F:System.Data.CommandBehavior.SingleRow" /> per migliorare le prestazioni dell'applicazione.È possibile specificare SingleRow quando si eseguono query che devono restituire più set di risultati.  In tal caso, dove vengono specificate sia una query SQL con più set di risultati sia una singola riga, il risultato restituito conterrà solo la prima riga del primo set di risultati.Gli altri set di risultati della query non verranno restituiti.</summary>
    </member>
    <member name="T:System.Data.CommandType">
      <summary>Specifica il modo in cui viene interpretata una stringa di comando.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandType.StoredProcedure">
      <summary>Nome di una stored procedure.</summary>
    </member>
    <member name="F:System.Data.CommandType.TableDirect">
      <summary>Nome di una tabella.</summary>
    </member>
    <member name="F:System.Data.CommandType.Text">
      <summary>Comando di testo SQL (valore predefinito). </summary>
    </member>
    <member name="T:System.Data.ConnectionState">
      <summary>Descrive lo stato corrente della connessione a un'origine dati.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ConnectionState.Broken">
      <summary>La connessione all'origine dati è interrotta.Questa situazione può verificarsi solo dopo l'apertura della connessione.Una connessione in questo stato può essere chiusa e riaperta. Questo valore è riservato per le future versioni del prodotto.</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Closed">
      <summary>La connessione è chiusa.</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Connecting">
      <summary>L'oggetto connessione sta eseguendo la connessione all'origine dati.</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Executing">
      <summary>L'oggetto connessione sta eseguendo un comando. Questo valore è riservato per le future versioni del prodotto. </summary>
    </member>
    <member name="F:System.Data.ConnectionState.Fetching">
      <summary>L'oggetto connessione sta recuperando i dati. Questo valore è riservato per le future versioni del prodotto. </summary>
    </member>
    <member name="F:System.Data.ConnectionState.Open">
      <summary>La connessione è attivata.</summary>
    </member>
    <member name="T:System.Data.DbType">
      <summary>Specifica il tipo di dati di un campo, di una proprietà o di un oggetto Parameter di un provider di dati .NET Framework.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.DbType.AnsiString">
      <summary>Flusso di caratteri non Unicode di lunghezza variabile compresi tra 1 e 8.000 caratteri.</summary>
    </member>
    <member name="F:System.Data.DbType.AnsiStringFixedLength">
      <summary>Flusso di caratteri non Unicode di lunghezza fissa.</summary>
    </member>
    <member name="F:System.Data.DbType.Binary">
      <summary>Flusso di dati binari di lunghezza variabile compresi tra 1 e 8.000 byte.</summary>
    </member>
    <member name="F:System.Data.DbType.Boolean">
      <summary>Tipo semplice che rappresenta i valori booleani true o false.</summary>
    </member>
    <member name="F:System.Data.DbType.Byte">
      <summary>Unsigned Integer a 8 bit compreso tra 0 e 255.</summary>
    </member>
    <member name="F:System.Data.DbType.Currency">
      <summary>Valore di valuta compreso nell'intervallo tra -2 63 (o -922.337.203.685.477,5808) e 2 63 -1 (o +922.337.203.685.477,5807) con un'approssimazione pari a dieci millesimi di unità di valuta.</summary>
    </member>
    <member name="F:System.Data.DbType.Date">
      <summary>Tipo che rappresenta un valore di data.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime">
      <summary>Tipo che rappresenta un valore di data e di ora.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime2">
      <summary>Dati relativi a data e ora.L'intervallo dei valori relativi alla data è compreso tra il 1° gennaio 1 d.C. e il 31 dicembre 9999 d.C.L'intervallo dei valori relativi all'ora è compreso tra le ore 00.00.00 e 23.59.59,9999999 con un'approssimazione di 100 nanosecondi.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTimeOffset">
      <summary>Dati relativi a data e ora con dipendenza dal fuso orario.L'intervallo dei valori relativi alla data è compreso tra il 1° gennaio 1 d.C. e il 31 dicembre 9999 d.C.L'intervallo dei valori relativi all'ora è compreso tra le ore 00.00.00 e 23.59.59,9999999 con un'approssimazione di 100 nanosecondi.L'intervallo dei valori relativi al fuso orario è compreso tra -14.00 e +14.00.</summary>
    </member>
    <member name="F:System.Data.DbType.Decimal">
      <summary>Tipo semplice che rappresenta valori compresi tra 1,0 x 10 -28 e approssimativamente 7,9 x 10 28 con 28-29 cifre significative.</summary>
    </member>
    <member name="F:System.Data.DbType.Double">
      <summary>Tipo a virgola mobile che rappresenta valori compresi tra 5,0 x 10 -324 e 1,7 x 10 308 con un'approssimazione di 15-16 cifre.</summary>
    </member>
    <member name="F:System.Data.DbType.Guid">
      <summary>Identificatore univoco globale o GUID.</summary>
    </member>
    <member name="F:System.Data.DbType.Int16">
      <summary>Tipo integrale che rappresenta interi con segno a 16 bit compresi tra -32768 e 32767.</summary>
    </member>
    <member name="F:System.Data.DbType.Int32">
      <summary>Tipo integrale che rappresenta interi con segno a 32 bit compresi tra -2147483648 e 2147483647.</summary>
    </member>
    <member name="F:System.Data.DbType.Int64">
      <summary>Tipo integrale che rappresenta interi con segno a 64 bit compresi tra -9223372036854775808 e 9223372036854775807.</summary>
    </member>
    <member name="F:System.Data.DbType.Object">
      <summary>Tipo generale che rappresenta qualsiasi tipo di valore o riferimento non rappresentato in modo esplicito da un altro valore di DbType.</summary>
    </member>
    <member name="F:System.Data.DbType.SByte">
      <summary>Tipo integrale che rappresenta interi con segno a 8 bit compresi tra -128 e 127.</summary>
    </member>
    <member name="F:System.Data.DbType.Single">
      <summary>Tipo a virgola mobile che rappresenta valori compresi tra 1,5 x 10 -45 e 3,4 x 10 38 con un'approssimazione di 7 cifre.</summary>
    </member>
    <member name="F:System.Data.DbType.String">
      <summary>Tipo che rappresenta stringhe di caratteri Unicode.</summary>
    </member>
    <member name="F:System.Data.DbType.StringFixedLength">
      <summary>Stringa a lunghezza fissa di caratteri Unicode.</summary>
    </member>
    <member name="F:System.Data.DbType.Time">
      <summary>Tipo che rappresenta un valore DateTime di SQL Server.Se si desidera utilizzare un valore time di SQL Server, utilizzare <see cref="F:System.Data.SqlDbType.Time" />.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt16">
      <summary>Tipo integrale che rappresenta interi senza segno a 16 bit compresi tra 0 e 65535.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt32">
      <summary>Tipo integrale che rappresenta interi senza segno a 32 bit compresi tra 0 e 4294967295.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt64">
      <summary>Tipo integrale che rappresenta interi senza segno a 64 bit compresi tra 0 e 18446744073709551615.</summary>
    </member>
    <member name="F:System.Data.DbType.VarNumeric">
      <summary>Valore numerico a lunghezza variabile.</summary>
    </member>
    <member name="F:System.Data.DbType.Xml">
      <summary>Rappresentazione analizzata di un documento o frammento XML.</summary>
    </member>
    <member name="T:System.Data.IsolationLevel">
      <summary>Specifica il comportamento di blocco della transazione per la connessione.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.IsolationLevel.Chaos">
      <summary>Le modifiche in sospeso dalle transazioni più isolate non possono essere sovrascritte.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadCommitted">
      <summary>La condivisione dei blocchi viene mantenuta durante la lettura dei dati per evitare letture dirty, anche se è possibile modificare i dati prima del termine della transazione, con conseguente produzione di letture non ripetibili o dati fantasma.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadUncommitted">
      <summary>È possibile una lettura dirty, ovvero non verrà emesso alcun blocco condiviso, né verrà rispettato alcun blocco esclusivo.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.RepeatableRead">
      <summary>I blocchi sono posizionati su tutti i dati utilizzati in una query, impedendo l'aggiornamento dei dati da parte di altri utenti.Vengono impedite le letture non ripetibili, ma possono essere presenti righe fantasma.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Serializable">
      <summary>Viene posizionato un blocco di intervallo sull'oggetto <see cref="T:System.Data.DataSet" />, per impedire ad altri utenti di aggiornare o immettere righe nel dataset fino al termine della transazione.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Snapshot">
      <summary>Riduce il blocco archiviando una versione dei dati che può essere letta da un'applicazione mentre gli stessi dati vengono modificati da un'altra applicazione.Indica che da una transazione non è possibile vedere le modifiche apportate in altre transazioni, anche se viene ripetuta la query.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Unspecified">
      <summary>Verrà utilizzato un livello di isolamento diverso da quello specificato, ma il livello non potrà essere determinato.</summary>
    </member>
    <member name="T:System.Data.ParameterDirection">
      <summary>Specifica il tipo di un parametro contenuto in una query relativa all'oggetto <see cref="T:System.Data.DataSet" />.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ParameterDirection.Input">
      <summary>Il parametro è un parametro di input.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.InputOutput">
      <summary>Il parametro può essere sia di input sia di output.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.Output">
      <summary>Il parametro è un parametro di output.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.ReturnValue">
      <summary>Il parametro rappresenta un valore restituito da un'operazione quale una stored procedure, una funzione predefinita o una funzione definita dall'utente.</summary>
    </member>
    <member name="T:System.Data.StateChangeEventArgs">
      <summary>Fornisce i dati per l'evento di variazione di stato di un provider di dati .NET Framework.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.StateChangeEventArgs.#ctor(System.Data.ConnectionState,System.Data.ConnectionState)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.StateChangeEventArgs" /> quando sono forniti lo stato originale e lo stato corrente dell'oggetto.</summary>
      <param name="originalState">Uno dei valori di <see cref="T:System.Data.ConnectionState" />. </param>
      <param name="currentState">Uno dei valori di <see cref="T:System.Data.ConnectionState" />. </param>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.CurrentState">
      <summary>Ottiene il nuovo stato della connessione.L'oggetto connessione sarà già nel nuovo stato quando verrà generato l'evento.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.ConnectionState" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.OriginalState">
      <summary>Ottiene lo stato originale della connessione.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.ConnectionState" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.StateChangeEventHandler">
      <summary>Rappresenta il metodo in base al quale verrà gestito l'evento <see cref="E:System.Data.Common.DbConnection.StateChange" />.</summary>
      <param name="sender">Origine dell’evento. </param>
      <param name="e">Classe <see cref="T:System.Data.StateChangeEventArgs" /> che contiene i dati dell'evento. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.UpdateRowSource">
      <summary>Specifica il modo in cui vengono applicati i risultati dei comandi di query alla riga da aggiornare.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.UpdateRowSource.Both">
      <summary>Viene eseguito il mapping dei parametri di output e della prima riga restituita alla riga modificata nell'oggetto <see cref="T:System.Data.DataSet" />.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.FirstReturnedRecord">
      <summary>Viene eseguito il mapping dei dati contenuti nella prima riga restituita alla riga modificata nell'oggetto <see cref="T:System.Data.DataSet" />.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.None">
      <summary>Eventuali parametri o righe restituite vengono ignorati.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.OutputParameters">
      <summary>Viene eseguito il mapping dei parametri di output alla riga modificata nell'oggetto <see cref="T:System.Data.DataSet" />.</summary>
    </member>
    <member name="T:System.Data.Common.DbCommand">
      <summary>Rappresenta un'istruzione SQL o una stored procedure da eseguire in relazione a un'origine dati.Fornisce una classe base per le classi specifiche del database che rappresentano i comandi.<see cref="Overload:System.Data.Common.DbCommand.ExecuteNonQueryAsync" /></summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.#ctor">
      <summary>Crea un'istanza dell'oggetto <see cref="T:System.Data.Common.DbCommand" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbCommand.Cancel">
      <summary>Tenta di annullare l'esecuzione di una classe <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandText">
      <summary>Ottiene o imposta il comando di testo da eseguire sull'origine dati.</summary>
      <returns>Comando di testo da eseguire.Il valore predefinito è una stringa vuota ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandTimeout">
      <summary>Ottiene o imposta il tempo di attesa prima di terminare il tentativo di esecuzione di un comando e di generare un errore.</summary>
      <returns>Tempo di attesa in secondi per l'esecuzione del comando.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandType">
      <summary>Indica o specifica la modalità di interpretazione della proprietà <see cref="P:System.Data.Common.DbCommand.CommandText" />.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.CommandType" />.Il valore predefinito è Text.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Connection">
      <summary>Ottiene o imposta la classe <see cref="T:System.Data.Common.DbConnection" /> utilizzata da questa classe <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <returns>Connessione all'origine dati.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateDbParameter">
      <summary>Consente di creare una nuova istanza dell'oggetto <see cref="T:System.Data.Common.DbParameter" />.</summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbParameter" />.</returns>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateParameter">
      <summary>Consente di creare una nuova istanza dell'oggetto <see cref="T:System.Data.Common.DbParameter" />.</summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbParameter" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbConnection">
      <summary>Ottiene o imposta la classe <see cref="T:System.Data.Common.DbConnection" /> utilizzata da questa classe <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <returns>Connessione all'origine dati.</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbParameterCollection">
      <summary>Ottiene la raccolta di oggetti <see cref="T:System.Data.Common.DbParameter" />.</summary>
      <returns>Parametri dell'istruzione SQL o della stored procedure.</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbTransaction">
      <summary>Ottiene o imposta l'oggetto <see cref="P:System.Data.Common.DbCommand.DbTransaction" /> in cui viene eseguito l'oggetto <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <returns>Transazione all'interno della quale viene eseguito un oggetto Command per un provider di dati .NET Framework.Il valore predefinito è un riferimento null (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DesignTimeVisible">
      <summary>Ottiene o imposta un valore che indica se l'oggetto comando deve essere visibile in un controllo di interfaccia personalizzato.</summary>
      <returns>true se l'oggetto comando deve essere visibile in un controllo; in caso contrario, false.Il valore predefinito è true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Dispose"></member>
    <member name="M:System.Data.Common.DbCommand.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
      <summary>Esegue il testo del comando a fronte della connessione.</summary>
      <returns>Attività che rappresenta l'operazione.</returns>
      <param name="behavior">Un'istanza di <see cref="T:System.Data.CommandBehavior" />.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>I provider devono implementare questo metodo per fornire un'implementazione non predefinita per gli overload di <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> .L'implementazione predefinita richiama il metodo sincrono <see cref="M:System.Data.Common.DbCommand.ExecuteReader" /> e restituisce un'attività completata, bloccando il thread chiamante.L'implementazione predefinita restituirà un'attività annullata se viene passato un token di annullamento già annullato.Le eccezioni generate da ExecuteReader verranno passate mediante la proprietà Task Exception restituita.Questo metodo accetta un token di annullamento che può essere utilizzato per richiedere che l'operazione venga annullata in anticipo.Le implementazioni possono ignorare la richiesta.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="behavior">Opzioni per l'esecuzione di istruzioni e il recupero dei dati.</param>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQuery">
      <summary>Esegue un'istruzione SQL a fronte di un oggetto connessione.</summary>
      <returns>Numero di righe interessate.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync">
      <summary>Versione asincrona di <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />, che esegue un'istruzione SQL a fronte di un oggetto di connessione.Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>Si tratta della versione asincrona di <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />.I provider devono eseguire l'override con un'implementazione appropriata.Il token di annullamento può facoltativamente essere ignorato.L'implementazione predefinita richiama il metodo sincrono <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> e restituisce un'attività completata, bloccando il thread chiamante.L'implementazione predefinita restituirà un'attività annullata se viene passato un token di annullamento già annullato.  Le eccezioni generate da <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> verranno passate mediante la proprietà Task Exception restituita.Non richiamare altri metodi e proprietà dell'oggetto DbCommand finché l'attività restituita non viene completata.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader">
      <summary>Esegue la proprietà <see cref="P:System.Data.Common.DbCommand.CommandText" /> sulla proprietà <see cref="P:System.Data.Common.DbCommand.Connection" /> e restituisce un oggetto <see cref="T:System.Data.Common.DbDataReader" />.</summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbDataReader" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>Esegue la proprietà <see cref="P:System.Data.Common.DbCommand.CommandText" /> sulla proprietà <see cref="P:System.Data.Common.DbCommand.Connection" /> e restituisce un oggetto <see cref="T:System.Data.Common.DbDataReader" /> utilizzando uno dei valori di <see cref="T:System.Data.CommandBehavior" />. </summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbDataReader" />.</returns>
      <param name="behavior">Uno dei valori di <see cref="T:System.Data.CommandBehavior" />.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync">
      <summary>Versione asincrona di <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, che esegue <see cref="P:System.Data.Common.DbCommand.CommandText" /> in un <see cref="P:System.Data.Common.DbCommand.Connection" /> e restituisce un <see cref="T:System.Data.Common.DbDataReader" />Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>Versione asincrona di <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, che esegue <see cref="P:System.Data.Common.DbCommand.CommandText" /> in un <see cref="P:System.Data.Common.DbCommand.Connection" /> e restituisce un <see cref="T:System.Data.Common.DbDataReader" />Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="behavior">Uno dei valori di <see cref="T:System.Data.CommandBehavior" />.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="behavior">Uno dei valori di <see cref="T:System.Data.CommandBehavior" />.</param>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>Versione asincrona di <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, che esegue <see cref="P:System.Data.Common.DbCommand.CommandText" /> in un <see cref="P:System.Data.Common.DbCommand.Connection" /> e restituisce un <see cref="T:System.Data.Common.DbDataReader" />Questo metodo propaga la notifica di richiesta di annullamento delle operazioni.Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" />.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
      <exception cref="T:System.ArgumentException">Valore di <see cref="T:System.Data.CommandBehavior" /> non valido.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalar">
      <summary>Esegue la query e restituisce la prima colonna della prima riga nel gruppo di risultati restituito dalla query.Tutte le altre righe e colonne vengono ignorate.</summary>
      <returns>Prima colonna della prima riga nel gruppo di risultati.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync">
      <summary>Una versione asincrona di <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> che esegue la query e restituisce la prima colonna della prima riga nel set di risultati restituito dalla query.Tutte le altre righe e colonne vengono ignorate.Richiama <see cref="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>Si tratta della versione asincrona di <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" />.I provider devono eseguire l'override con un'implementazione appropriata.Il token di annullamento può facoltativamente essere ignorato.L'implementazione predefinita richiama il metodo sincrono <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> e restituisce un'attività completata, bloccando il thread chiamante.L'implementazione predefinita restituirà un'attività annullata se viene passato un token di annullamento già annullato.Le eccezioni generate da ExecuteScalar verranno passate mediante la proprietà Task Exception restituita.Non richiamare altri metodi e proprietà dell'oggetto DbCommand finché l'attività restituita non viene completata.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">Token da monitorare per le richieste di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="P:System.Data.Common.DbCommand.Parameters">
      <summary>Ottiene la raccolta di oggetti <see cref="T:System.Data.Common.DbParameter" />.Per ulteriori informazioni sui parametri Configurazione di parametri e di tipi di dati dei parametri, vedere .</summary>
      <returns>Parametri dell'istruzione SQL o della stored procedure.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Prepare">
      <summary>Crea una versione preparata o compilata del comando sull'origine dati.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Transaction">
      <summary>Ottiene o imposta l'oggetto <see cref="T:System.Data.Common.DbTransaction" /> in cui viene eseguito l'oggetto <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <returns>Transazione in cui viene eseguito l'oggetto Command di un provider di dati .NET Framework.Il valore predefinito è un riferimento null (Nothing in Visual Basic).</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.UpdatedRowSource">
      <summary>Ottiene o imposta il modo in cui i risultati del comando vengono applicati alla classe <see cref="T:System.Data.DataRow" /> quando sono utilizzati dal metodo Update di una classe <see cref="T:System.Data.Common.DbDataAdapter" />.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.UpdateRowSource" />.Il valore predefinito è Both a meno che il commando non venga generato automaticamente.In tal caso, il valore predefinito sarà None.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbConnection">
      <summary>Rappresenta una connessione a un database. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbConnection" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
      <summary>Avvia una transazione di database.</summary>
      <returns>Oggetto che rappresenta la nuova transazione.</returns>
      <param name="isolationLevel">Specifica il livello di isolamento per la transazione.</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction">
      <summary>Avvia una transazione di database.</summary>
      <returns>Oggetto che rappresenta la nuova transazione.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>Inizia una transazione di database con il livello di isolamento specificato.</summary>
      <returns>Oggetto che rappresenta la nuova transazione.</returns>
      <param name="isolationLevel">Specifica il livello di isolamento per la transazione.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.ChangeDatabase(System.String)">
      <summary>Cambia il database corrente per una connessione aperta.</summary>
      <param name="databaseName">Specifica il nome del database per la connessione da utilizzare.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Close">
      <summary>Chiude la connessione al database.È il metodo preferito di chiusura di una connessione aperta.</summary>
      <exception cref="T:System.Data.Common.DbException">Errore a livello di connessione che si è verificato durante l'apertura della connessione. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionString">
      <summary>Ottiene o imposta la stringa utilizzata per aprire la connessione.</summary>
      <returns>Stringa di connessione utilizzata per stabilire la connessione iniziale.L'esatto contenuto della stringa di connessione dipende dall'origine dati specifica per la connessione.Il valore predefinito è una stringa vuota.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionTimeout">
      <summary>Ottiene il tempo di attesa specificato per stabilire una connessione prima di terminare il tentativo e generare un errore.</summary>
      <returns>Tempo di attesa in secondi per l'apertura di una connessione.Il valore predefinito è determinato dal particolare tipo di connessione che si sta utilizzando.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateCommand">
      <summary>Crea e restituisce un oggetto <see cref="T:System.Data.Common.DbCommand" /> associato alla connessione corrente.</summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbCommand" />.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateDbCommand">
      <summary>Crea e restituisce un oggetto <see cref="T:System.Data.Common.DbCommand" /> associato alla connessione corrente.</summary>
      <returns>Un oggetto <see cref="T:System.Data.Common.DbCommand" />.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnection.Database">
      <summary>Ottiene il nome del database corrente dopo che è stata aperta la connessione o il nome del database specificato nella stringa di connessione prima che la connessione venga aperta.</summary>
      <returns>Nome del database corrente o del database da utilizzare una volta aperta una connessione.Il valore predefinito è una stringa vuota.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.DataSource">
      <summary>Ottiene il nome del server database a cui connettersi.</summary>
      <returns>Nome del server database a cui connettersi.Il valore predefinito è una stringa vuota.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Dispose"></member>
    <member name="M:System.Data.Common.DbConnection.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbConnection.OnStateChange(System.Data.StateChangeEventArgs)">
      <summary>Genera l'evento <see cref="E:System.Data.Common.DbConnection.StateChange" />.</summary>
      <param name="stateChange">Classe <see cref="T:System.Data.StateChangeEventArgs" /> che contiene i dati dell'evento.</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.Open">
      <summary>Apre una connessione a un database con le impostazioni specificate dalla proprietà <see cref="P:System.Data.Common.DbConnection.ConnectionString" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync">
      <summary>Versione asincrona di <see cref="M:System.Data.Common.DbConnection.Open" />, che apre una connessione di database con le impostazioni specificate da <see cref="P:System.Data.Common.DbConnection.ConnectionString" />.Questo metodo richiama il metodo virtuale <see cref="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>Si tratta della versione asincrona di <see cref="M:System.Data.Common.DbConnection.Open" />.I provider devono eseguire l'override con un'implementazione appropriata.Il token di annullamento può facoltativamente essere rispettato.L'implementazione predefinita richiama la chiamata sincrona <see cref="M:System.Data.Common.DbConnection.Open" /> e restituisce un'attività completata.L'implementazione predefinita restituirà un'attività annullata se viene passato un cancellationToken già annullato.Le eccezioni generate da Open verranno passate mediante la proprietà Task Exception restituita.Non richiamare altri metodi e proprietà dell'oggetto DbConnection finché l'attività restituita non viene completata.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">L'istruzione di annullamento.</param>
    </member>
    <member name="P:System.Data.Common.DbConnection.ServerVersion">
      <summary>Ottiene una stringa che rappresenta la versione del server a cui l'oggetto è connesso.</summary>
      <returns>Versione del database.Il formato della stringa restituita dipende dal particolare tipo di connessione utilizzato.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Data.Common.DbConnection.ServerVersion" /> è stato chiamato mentre l'attività restituita non è stata completata e la connessione non è stata aperta dopo una chiamata a <see cref="Overload:System.Data.Common.DbConnection.OpenAsync" />.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.State">
      <summary>Ottiene una stringa che descrive lo stato della connessione.</summary>
      <returns>Stato della connessione.Il formato della stringa restituita dipende dal particolare tipo di connessione utilizzato.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="E:System.Data.Common.DbConnection.StateChange">
      <summary>Viene generato quando lo stato dell'evento cambia.</summary>
    </member>
    <member name="T:System.Data.Common.DbConnectionStringBuilder">
      <summary>Fornisce una classe base per generatori di stringhe di connessione fortemente tipizzate.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Add(System.String,System.Object)">
      <summary>Aggiunge una voce con la chiave e il valore specificati all'interno della classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <param name="keyword">Chiave da aggiungere alla classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</param>
      <param name="value">Valore della chiave specificata.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> è un riferimento null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">L'<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è in sola lettura. In alternativaL'oggetto <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è di dimensioni fisse.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.AppendKeyValuePair(System.Text.StringBuilder,System.String,System.String)">
      <summary>Fornisce una modalità sicura ed efficiente per aggiungere una chiave e un valore a un oggetto <see cref="T:System.Text.StringBuilder" /> esistente.</summary>
      <param name="builder">Classe <see cref="T:System.Text.StringBuilder" /> a cui aggiungere la coppia chiave/valore.</param>
      <param name="keyword">Chiave da aggiungere.</param>
      <param name="value">Valore della chiave fornita.</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Clear">
      <summary>Consente di cancellare il contenuto dell'istanza <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <exception cref="T:System.NotSupportedException">L'<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è in sola lettura.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString">
      <summary>Ottiene o imposta la stringa di connessione associata alla classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>Stringa di connessione corrente, creata dalle coppie chiave/valore contenute all'interno della classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.Il valore predefinito è una stringa vuota.</returns>
      <exception cref="T:System.ArgumentException">È stato fornito un argomento non valido della stringa di connessione.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ContainsKey(System.String)">
      <summary>Stabilisce se la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> contiene una chiave specifica.</summary>
      <returns>true se la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> contiene una voce con la chiave specificata; in caso contrario, false.</returns>
      <param name="keyword">Chiave da individuare nell'interfaccia <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> è un riferimento null (Nothing in Visual Basic).</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Count">
      <summary>Ottiene il numero corrente di chiavi contenute all'interno della proprietà <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" />.</summary>
      <returns>Numero di chiavi contenute all'interno della stringa di connessione gestita dall'istanza <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.EquivalentTo(System.Data.Common.DbConnectionStringBuilder)">
      <summary>Confronta le informazioni sulla connessione in questo oggetto <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> con le informazioni sulla connessione nell'oggetto fornito.</summary>
      <returns>true se le informazioni sulla connessione in entrambi gli oggetti <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> generano una stringa di connessione equivalente; in caso contrario, false.</returns>
      <param name="connectionStringBuilder">Oggetto <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> da confrontare a questo oggetto <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Item(System.String)">
      <summary>Ottiene o imposta il valore associato alla chiave specificata.</summary>
      <returns>Valore associato alla chiave specificata.Se la chiave specificata non viene trovata, tentando di ottenerla viene restituito un riferimento null (Nothing in Visual Basic), mentre tentando di impostarla viene creato un nuovo elemento con la chiave specificata.Se si passa una chiave null (Nothing in Visual Basic), viene generata una classe <see cref="T:System.ArgumentNullException" />.L'assegnazione di un valore null rimuove la coppia chiave/valore.</returns>
      <param name="keyword">Chiave dell'elemento da ottenere o impostare.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> è un riferimento null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">La proprietà è impostata e la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è in sola lettura. In alternativaLa proprietà è impostata, <paramref name="keyword" /> non esiste nella raccolta e <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è di dimensioni fisse.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Keys">
      <summary>Ottiene un'interfaccia <see cref="T:System.Collections.ICollection" /> contenente le chiavi presenti nella classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente le chiavi presenti nella classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Remove(System.String)">
      <summary>Rimuove la voce con la chiave specificata dall'istanza <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>true nel caso di una chiave esistente nella stringa di connessione e poi rimossa; false se la chiave è inesistente.</returns>
      <param name="keyword">La chiave della coppia chiave/valore da rimuovere dalla stringa di connessione è presente in questa classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> è null (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">La classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è in sola lettura o la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> è di dimensioni fisse.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>Indica se la chiave specificata esiste in questa istanza di <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>true se la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> contiene una voce con la chiave specificata; in caso contrario, false.</returns>
      <param name="keyword">Chiave da individuare nell'interfaccia <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Copia gli elementi dell'interfaccia <see cref="T:System.Collections.ICollection" /> in un oggetto <see cref="T:System.Array" />, a partire da un particolare indice della matrice <see cref="T:System.Array" />.</summary>
      <param name="array">Oggetto <see cref="T:System.Array" /> unidimensionale che rappresenta la destinazione degli elementi copiati dall'oggetto <see cref="T:System.Collections.ICollection" />.L'indicizzazione di <see cref="T:System.Array" /> deve essere in base zero.</param>
      <param name="index">Indice in base zero della matrice specificata nel parametro <paramref name="array" /> in corrispondenza del quale ha inizio la copia.</param>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#IsSynchronized">
      <summary>Ottiene un valore che indica se l'accesso a <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe).</summary>
      <returns>true se l'accesso all'oggetto <see cref="T:System.Collections.ICollection" /> è sincronizzato (thread-safe); in caso contrario, false.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#SyncRoot">
      <summary>Ottiene un oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</summary>
      <returns>Oggetto che può essere utilizzato per sincronizzare l'accesso a <see cref="T:System.Collections.ICollection" />.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Aggiunge un elemento con la chiave e il valore forniti all'oggetto <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="keyword">
        <see cref="T:System.Object" /> da utilizzare come chiave dell'elemento da aggiungere.</param>
      <param name="value">
        <see cref="T:System.Object" /> da utilizzare come valore dell'elemento da aggiungere.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Determina se l'oggetto <see cref="T:System.Collections.IDictionary" /> contiene un elemento con la chiave specificata.</summary>
      <returns>true se <see cref="T:System.Collections.IDictionary" /> contiene un elemento contenente la chiave; in caso contrario, false.</returns>
      <param name="keyword">Chiave da individuare nell'oggetto <see cref="T:System.Collections.IDictionary" />.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#GetEnumerator">
      <summary>Restituisce un oggetto <see cref="T:System.Collections.IDictionaryEnumerator" /> per l'oggetto <see cref="T:System.Collections.IDictionary" />.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IDictionaryEnumerator" /> per l'oggetto <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ottiene o imposta l'elemento con la chiave specificata.</summary>
      <returns>Elemento con la chiave specificata.</returns>
      <param name="keyword">Chiave dell'elemento da ottenere o impostare.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Rimuove dall'oggetto <see cref="T:System.Collections.IDictionary" /> l'elemento con la chiave specificata.</summary>
      <param name="keyword">Chiave dell'elemento da rimuovere.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Restituisce un enumeratore che consente di scorrere una raccolta.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzato per scorrere la raccolta.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ToString">
      <summary>Restituisce la stringa di connessione associata a questa classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>Proprietà <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> corrente.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>Recupera un valore corrispondente alla chiave fornita da questa classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>true se <paramref name="keyword" /> viene trovato nella stringa di connessione. In caso contrario, false.</returns>
      <param name="keyword">Chiave dell'elemento da recuperare.</param>
      <param name="value">Valore corrispondente a <paramref name="key" />.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> contiene un valore null (Nothing in Visual Basic).</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Values">
      <summary>Ottiene un oggetto <see cref="T:System.Collections.ICollection" /> che contiene i valori di <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.ICollection" /> contenente i valori della classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="T:System.Data.Common.DbDataReader">
      <summary>Legge un flusso di righe di tipo forward-only da un'origine dati.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbDataReader" />.</summary>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Depth">
      <summary>Ottiene un valore che indica il livello di annidamento della riga corrente.</summary>
      <returns>Livello di annidamento della riga corrente.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose">
      <summary>Rilascia tutte le risorse utilizzate dall'istanza corrente della classe <see cref="T:System.Data.Common.DbDataReader" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose(System.Boolean)">
      <summary>Libera le risorse gestite utilizzate dalla classe <see cref="T:System.Data.Common.DbDataReader" /> ed eventualmente libera le risorse non gestite.</summary>
      <param name="disposing">true per liberare le risorse gestite e non gestite; false per liberare solo le risorse non gestite.</param>
    </member>
    <member name="P:System.Data.Common.DbDataReader.FieldCount">
      <summary>Ottiene il numero delle colonne nella riga corrente.</summary>
      <returns>Numero di colonne nella riga corrente.</returns>
      <exception cref="T:System.NotSupportedException">Non esiste alcuna connessione corrente a un'istanza di SQL Server. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBoolean(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come valore Boolean.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetByte(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di byte.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Legge un flusso di byte dalla colonna specificata, partendo dalla posizione indicata da <paramref name="dataOffset" /> e, nel buffer, partendo dalla posizione indicata da <paramref name="bufferOffset" />.</summary>
      <returns>Numero effettivo di byte letti.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <param name="dataOffset">Indice all'interno della riga dal quale iniziare l'operazione di lettura.</param>
      <param name="buffer">Buffer in cui copiare i dati.</param>
      <param name="bufferOffset">Indice con il buffer in cui i dati vengono copiati.</param>
      <param name="length">Numero massimo di caratteri da leggere.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChar(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di carattere singolo.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Legge un flusso di caratteri dalla colonna specificata, partendo dalla posizione indicata da <paramref name="dataOffset" /> e, nel buffer, partendo dalla posizione indicata da <paramref name="bufferOffset" />.</summary>
      <returns>Numero effettivo di caratteri letti.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <param name="dataOffset">Indice all'interno della riga dal quale iniziare l'operazione di lettura.</param>
      <param name="buffer">Buffer in cui copiare i dati.</param>
      <param name="bufferOffset">Indice con il buffer in cui i dati vengono copiati.</param>
      <param name="length">Numero massimo di caratteri da leggere.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetData(System.Int32)">
      <summary>Restituisce un oggetto <see cref="T:System.Data.Common.DbDataReader" /> per l'ordinale della colonna richiesta.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbDataReader" />.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDataTypeName(System.Int32)">
      <summary>Ottiene il nome del tipo di dati della colonna specificata.</summary>
      <returns>Stringa che rappresenta il nome del tipo di dati.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDateTime(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di oggetto <see cref="T:System.DateTime" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDbDataReader(System.Int32)">
      <summary>Restituisce un oggetto <see cref="T:System.Data.Common.DbDataReader" /> per l'ordinale della colonna richiesta, che può essere sottoposto a override tramite un'implementazione specifica del provider.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbDataReader" />.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDecimal(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di oggetto <see cref="T:System.Decimal" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDouble(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di numero in virgola mobile e precisione doppia.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetEnumerator">
      <summary>Restituisce un oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzato per scorrere le righe nel visualizzatore di dati.</summary>
      <returns>Oggetto <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzato per scorrere le righe nel visualizzatore di dati.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldType(System.Int32)">
      <summary>Ottiene il tipo di dati della colonna specificata.</summary>
      <returns>Tipo di dati della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValue``1(System.Int32)">
      <summary>Ottiene in modo sincrono il valore della colonna specificata sotto forma di tipo.</summary>
      <returns>Colonna da recuperare.</returns>
      <param name="ordinal">Colonna da recuperare.</param>
      <typeparam name="T">Ottiene in modo sincrono il valore della colonna specificata sotto forma di tipo.</typeparam>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.SqlClient.SqlDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> non è stato chiamato, o è stato restituito false).Ha tentato di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> non corrisponde al tipo restituito da SQL Server o non è possibile eseguire il cast.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)">
      <summary>Ottiene in modo sincrono il valore della colonna specificata come tipo.</summary>
      <returns>Tipo di valore da restituire.</returns>
      <param name="ordinal">Tipo di valore da restituire.</param>
      <typeparam name="T">Tipo di valore da restituire.Per ulteriori informazioni, vedere la sezione relativa alle note.</typeparam>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Ha tentato di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> non corrisponde al tipo restituito dall'origine dati o non è possibile eseguire il cast.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>Ottiene in modo sincrono il valore della colonna specificata come tipo.</summary>
      <returns>Tipo di valore da restituire.</returns>
      <param name="ordinal">Tipo di valore da restituire.</param>
      <param name="cancellationToken">L'istruzione di annullamento, che passa una notifica relativa alle operazioni che devono essere annullate.Ciò non garantisce l'annullamento.Impostazione di CancellationToken.None che rende questo metodo equivalente a <see cref="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)" />.L'attività restituita deve essere contrassegnata come annullata.</param>
      <typeparam name="T">Tipo di valore da restituire.Per ulteriori informazioni, vedere la sezione relativa alle note.</typeparam>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Ha tentato di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> non corrisponde al tipo restituito dall'origine dati o non è possibile eseguire il cast.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFloat(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di numero in virgola mobile e precisione singola.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetGuid(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come identificatore univoco globale (GUID).</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt16(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di intero con segno a 16 bit.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt32(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di intero con segno a 32 bit.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt64(System.Int32)">
      <summary>Ottiene il valore della colonna specificata sotto forma di intero con segno a 64 bit.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetName(System.Int32)">
      <summary>Ottiene il nome della colonna, dato l'ordinale della colonna in base zero.</summary>
      <returns>Nome della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetOrdinal(System.String)">
      <summary>Ottiene l'ordinale della colonna, dato il nome della colonna.</summary>
      <returns>Posizione ordinale della colonna in base zero.</returns>
      <param name="name">Nome della colonna.</param>
      <exception cref="T:System.IndexOutOfRangeException">Il nome specificato non è un nome di colonna valido.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>Restituisce il tipo di campo specifico del provider per la colonna specificata.</summary>
      <returns>Oggetto <see cref="T:System.Type" /> che descrive il tipo di dati della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come istanza della classe <see cref="T:System.Object" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>Ottiene tutte le colonne attributo specifiche del provider nell'insieme per la riga corrente.</summary>
      <returns>Numero di istanze di <see cref="T:System.Object" /> nella matrice.</returns>
      <param name="values">Matrice di <see cref="T:System.Object" /> in cui copiare le colonne attributo.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetStream(System.Int32)">
      <summary>Recupera i dati come <see cref="T:System.IO.Stream" />.</summary>
      <returns>Oggetto restituito.</returns>
      <param name="ordinal">Recupera i dati come <see cref="T:System.IO.Stream" />.</param>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Ha tentato di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
      <exception cref="T:System.InvalidCastException">Il tipo restituito non è uno dei tipi di seguito:binaryimagevarbinaryudt</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetString(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come istanza della classe <see cref="T:System.String" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.InvalidCastException">Il cast specificato non è valido. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)">
      <summary>Recupera i dati come <see cref="T:System.IO.TextReader" />.</summary>
      <returns>Oggetto restituito.</returns>
      <param name="ordinal">Recupera i dati come <see cref="T:System.IO.TextReader" />.</param>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Ha tentato di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
      <exception cref="T:System.InvalidCastException">Il tipo restituito non è uno dei tipi di seguito:charncharntextnvarchartestovarchar</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValue(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come istanza della classe <see cref="T:System.Object" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValues(System.Object[])">
      <summary>Popola una matrice di oggetti con i valori della colonna della riga corrente.</summary>
      <returns>Numero di istanze di <see cref="T:System.Object" /> nella matrice.</returns>
      <param name="values">Matrice di <see cref="T:System.Object" /> in cui copiare le colonne attributo.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.HasRows">
      <summary>Ottiene un valore che indica se la classe <see cref="T:System.Data.Common.DbDataReader" /> contiene una o più righe.</summary>
      <returns>true se la classe <see cref="T:System.Data.Common.DbDataReader" /> contiene una o più righe. In caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.IsClosed">
      <summary>Ottiene un valore che indica se la classe <see cref="T:System.Data.Common.DbDataReader" /> è chiusa.</summary>
      <returns>true se l'oggetto <see cref="T:System.Data.Common.DbDataReader" /> è chiuso. In caso contrario, false.</returns>
      <exception cref="T:System.InvalidOperationException">La classe <see cref="T:System.Data.SqlClient.SqlDataReader" /> è chiusa. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)">
      <summary>Ottiene un valore che indica se la colonna contiene valori non esistenti o mancanti.</summary>
      <returns>true se la colonna specificata equivale a <see cref="T:System.DBNull" />, in caso contrario, false.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)">
      <summary>Versione asincrona di <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> che ottiene un valore che indica se la colonna contiene valori non esistenti o mancanti.</summary>
      <returns>true se il valore della colonna specificata equivale a DBNull, in caso contrario, false.</returns>
      <param name="ordinal">Colonna in base zero da recuperare.</param>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Tentativo di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>Versione asincrona di <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> che ottiene un valore che indica se la colonna contiene valori non esistenti o mancanti.Invia facoltativamente una notifica di richiesta di annullamento delle operazioni.</summary>
      <returns>true se il valore della colonna specificata equivale a DBNull, in caso contrario, false.</returns>
      <param name="ordinal">Colonna in base zero da recuperare.</param>
      <param name="cancellationToken">L'istruzione di annullamento, che passa una notifica relativa alle operazioni che devono essere annullate.Ciò non garantisce l'annullamento.Impostazione di CancellationToken.None che rende questo metodo equivalente a <see cref="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)" />.L'attività restituita deve essere contrassegnata come annullata.</param>
      <exception cref="T:System.InvalidOperationException">La connessione viene eliminata o chiusa durante il richiamo dei dati.<see cref="T:System.Data.Common.DbDataReader" /> viene chiuso durante il richiamo dei dati.Non sono disponibili dati pronti per essere letti (ad esempio, il primo <see cref="M:System.Data.Common.DbDataReader.Read" /> non è stato chiamato, o è stato restituito false).Tentativo di leggere una colonna letta in precedenza in modalità sequenziale.Operazione asincrona in corso.Si applica a tutti i metodi Get* durante l'esecuzione in modalità sequenziale, in quanto possono essere chiamati durante la lettura di un flusso.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Tentativo di leggere una colonna che non esiste.</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.Int32)">
      <summary>Ottiene il valore della colonna specificata come istanza della classe <see cref="T:System.Object" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="ordinal">Posizione ordinale della colonna in base zero.</param>
      <exception cref="T:System.IndexOutOfRangeException">L'indice passato non rientrava nell'intervallo compreso tra 0 e <see cref="P:System.Data.IDataRecord.FieldCount" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.String)">
      <summary>Ottiene il valore della colonna specificata come istanza della classe <see cref="T:System.Object" />.</summary>
      <returns>Valore della colonna specificata.</returns>
      <param name="name">Nome della colonna.</param>
      <exception cref="T:System.IndexOutOfRangeException">Non è stata trovata alcuna colonna con il nome specificato. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResult">
      <summary>Sposta il lettore sul risultato successivo durante la lettura dei risultati di un batch di istruzioni.</summary>
      <returns>true se sono presenti più gruppi di risultati; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync">
      <summary>Una versione asincrona di <see cref="M:System.Data.Common.DbDataReader.NextResult" /> che sposta il lettore sul risultato successivo durante la lettura dei risultati di un batch di istruzioni.Richiama <see cref="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>Si tratta della versione asincrona di <see cref="M:System.Data.Common.DbDataReader.NextResult" />.I provider devono eseguire l'override con un'implementazione appropriata.Il <paramref name="cancellationToken" /> può facoltativamente essere ignorato.L'implementazione predefinita richiama il metodo sincrono <see cref="M:System.Data.Common.DbDataReader.NextResult" /> e restituisce un'attività completata, bloccando il thread chiamante.L'implementazione predefinita restituirà un'attività annullata se viene passato un <paramref name="cancellationToken" /> già annullato.Le eccezioni generate da <see cref="M:System.Data.Common.DbDataReader.NextResult" /> verranno passate mediante la proprietà Task Exception restituita.Non si devono richiamare altri metodi e proprietà dell'oggetto DbDataReader mentre l'attività restituita non è ancora completata.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">L'istruzione di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Read">
      <summary>Sposta il lettore al record successivo in un gruppo di risultati.</summary>
      <returns>true se sono presenti più righe; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync">
      <summary>Versione asincrona di <see cref="M:System.Data.Common.DbDataReader.Read" />, che avanza il lettore al record successivo di un set di risultati.Questo metodo richiama <see cref="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)" /> con CancellationToken.None.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>Si tratta della versione asincrona di <see cref="M:System.Data.Common.DbDataReader.Read" />.  I provider devono eseguire l'override con un'implementazione appropriata.Il token di annullamento può facoltativamente essere ignorato.L'implementazione predefinita richiama il metodo sincrono <see cref="M:System.Data.Common.DbDataReader.Read" /> e restituisce un'attività completata, bloccando il thread chiamante.L'implementazione predefinita restituirà un'attività annullata se viene passato un cancellationToken già annullato.  Le eccezioni generate da Read verranno passate mediante la proprietà Task Exception restituita.Non richiamare altri metodi e proprietà dell'oggetto DbDataReader finché l'attività restituita non viene completata.</summary>
      <returns>Attività che rappresenta l'operazione asincrona.</returns>
      <param name="cancellationToken">L'istruzione di annullamento.</param>
      <exception cref="T:System.Data.Common.DbException">Errore che si è verificato durante l'esecuzione del testo del comando.</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.RecordsAffected">
      <summary>Ottiene il numero di righe modificate, inserite o eliminate dall'esecuzione dell'istruzione SQL. </summary>
      <returns>Numero di righe modificate, inserite o eliminate. -1 per le istruzioni SELECT; 0 se nessuna riga viene alterata o se l'istruzione ha esito negativo.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.VisibleFieldCount">
      <summary>Ottiene il numero di campi nella classe <see cref="T:System.Data.Common.DbDataReader" /> che non sono nascosti.</summary>
      <returns>Numero di campi non nascosti.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbException">
      <summary>La classe base per tutte le eccezioni generate per conto dell'origine dati.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbException" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbException" /> con il messaggio di errore specificato.</summary>
      <param name="message">Messaggio da visualizzare per questa eccezione.</param>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String,System.Exception)">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbException" /> con il messaggio di errore specificato e un riferimento all'eccezione interna che è la causa dell'eccezione.</summary>
      <param name="message">La stringa del messaggio di errore</param>
      <param name="innerException">Riferimento all'eccezione interna.</param>
    </member>
    <member name="T:System.Data.Common.DbParameter">
      <summary>Rappresenta un parametro di un oggetto <see cref="T:System.Data.Common.DbCommand" /> e, facoltativamente, il relativo mapping a una colonna <see cref="T:System.Data.DataSet" />.Per ulteriori informazioni sui parametri Configurazione di parametri e di tipi di dati dei parametri, vedere .</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameter.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbParameter" />.</summary>
    </member>
    <member name="P:System.Data.Common.DbParameter.DbType">
      <summary>Ottiene o imposta l'oggetto <see cref="T:System.Data.DbType" /> del parametro.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.DbType" />.Il valore predefinito è <see cref="F:System.Data.DbType.String" />.</returns>
      <exception cref="T:System.ArgumentException">La proprietà non è impostata su un oggetto <see cref="T:System.Data.DbType" /> valido.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Direction">
      <summary>Ottiene o imposta un valore che indica se il parametro è solo di input, solo di output, bidirezionale o è un parametro di valore restituito da una stored procedure.</summary>
      <returns>Uno dei valori di <see cref="T:System.Data.ParameterDirection" />.Il valore predefinito è Input.</returns>
      <exception cref="T:System.ArgumentException">La proprietà non è impostata su uno dei valori validi di <see cref="T:System.Data.ParameterDirection" />.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.IsNullable">
      <summary>Ottiene o imposta un valore che indica se il parametro accetta valori null.</summary>
      <returns>true se sono consentiti valori null; in caso contrario, false.Il valore predefinito è false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.ParameterName">
      <summary>Ottiene o imposta il nome della classe <see cref="T:System.Data.Common.DbParameter" />.</summary>
      <returns>Nome di <see cref="T:System.Data.Common.DbParameter" />.Il valore predefinito è una stringa vuota ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Precision">
      <summary>[Supportato in .NET Framework 4.5.1 e nelle versioni successive] Ottiene o imposta il numero massimo di cifre utilizzate per rappresentare la proprietà <see cref="P:System.Data.Common.DbParameter.Value" />.</summary>
      <returns>Numero massimo di cifre utilizzate per rappresentare la proprietà <see cref="P:System.Data.Common.DbParameter.Value" />.</returns>
    </member>
    <member name="M:System.Data.Common.DbParameter.ResetDbType">
      <summary>Ripristina le impostazioni originali della proprietà DbType.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Scale">
      <summary>[Supportato in .NET Framework 4.5.1 e nelle versioni successive] Ottiene o imposta il numero di posizioni decimali in cui viene risolta la proprietà <see cref="P:System.Data.Common.DbParameter.Value" />.</summary>
      <returns>Numero di posizioni decimali in cui la proprietà <see cref="P:System.Data.Common.DbParameter.Value" /> viene risolta.</returns>
    </member>
    <member name="P:System.Data.Common.DbParameter.Size">
      <summary>Ottiene o imposta la dimensione massima, in byte, dei dati all'interno della colonna.</summary>
      <returns>Dimensione massima in byte dei dati all'interno della colonna.Il valore predefinito viene dedotto dal valore di parametro.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumn">
      <summary>Ottiene o imposta il nome della colonna di origine mappata alla classe <see cref="T:System.Data.DataSet" /> e utilizzata per il caricamento o la restituzione della proprietà <see cref="P:System.Data.Common.DbParameter.Value" />.</summary>
      <returns>Nome della colonna di origine mappata all'oggetto <see cref="T:System.Data.DataSet" />.Il valore predefinito è una stringa vuota.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumnNullMapping">
      <summary>Ottiene o imposta un valore che indica se la colonna di origine è nullable.Ciò consente alla classe <see cref="T:System.Data.Common.DbCommandBuilder" /> di generare correttamente istruzioni Update per le colonne nullable.</summary>
      <returns>true se la colonna di origine è nullable; in caso contrario, false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Value">
      <summary>Ottiene o imposta il valore del parametro.</summary>
      <returns>Oggetto <see cref="T:System.Object" /> che rappresenta il valore del parametro.Il valore predefinito è null.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbParameterCollection">
      <summary>La classe base per un insieme di parametri pertinenti per una classe <see cref="T:System.Data.Common.DbCommand" />. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.#ctor">
      <summary>Inizializza una nuova istanza della classe <see cref="T:System.Data.Common.DbParameterCollection" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Add(System.Object)">
      <summary>Aggiunge l'oggetto <see cref="T:System.Data.Common.DbParameter" /> specificato a <see cref="T:System.Data.Common.DbParameterCollection" />.</summary>
      <returns>Indice di un oggetto <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</returns>
      <param name="value">Proprietà <see cref="P:System.Data.Common.DbParameter.Value" /> della classe <see cref="T:System.Data.Common.DbParameter" /> da aggiungere all'insieme.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.AddRange(System.Array)">
      <summary>Aggiunge una matrice di elementi con i valori specificati alla classe <see cref="T:System.Data.Common.DbParameterCollection" />.</summary>
      <param name="values">Matrice di valori di tipo <see cref="T:System.Data.Common.DbParameter" /> da aggiungere all'insieme.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Clear">
      <summary>Rimuove tutti i valori <see cref="T:System.Data.Common.DbParameter" /> da <see cref="T:System.Data.Common.DbParameterCollection" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.Object)">
      <summary>Indica se una classe <see cref="T:System.Data.Common.DbParameter" /> con la proprietà <see cref="P:System.Data.Common.DbParameter.Value" /> specificata è contenuta nell'insieme.</summary>
      <returns>true se l'oggetto <see cref="T:System.Data.Common.DbParameter" /> è nell'insieme; in caso contrario, false.</returns>
      <param name="value">Oggetto <see cref="P:System.Data.Common.DbParameter.Value" /> dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> da ricercare nell'insieme.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.String)">
      <summary>Indica se una classe <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato è presente nell'insieme.</summary>
      <returns>true se l'oggetto <see cref="T:System.Data.Common.DbParameter" /> è nell'insieme; in caso contrario, false.</returns>
      <param name="value">Nome della classe <see cref="T:System.Data.Common.DbParameter" /> da ricercare nell'insieme.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>Copia una matrice di elementi nell'insieme, partendo dall'indice specificato.</summary>
      <param name="array">Matrice di elementi da copiare nell'insieme.</param>
      <param name="index">Indice nell'insieme in cui copiare gli elementi.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Count">
      <summary>Specifica il numero di elementi nell'insieme.</summary>
      <returns>Numero di elementi contenuti nell'insieme.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetEnumerator">
      <summary>Consente di esporre il metodo <see cref="M:System.Collections.IEnumerable.GetEnumerator" />, che supporta un'iterazione semplice su un insieme eseguita da un provider di dati .NET Framework.</summary>
      <returns>Interfaccia <see cref="T:System.Collections.IEnumerator" /> che può essere utilizzata per scorrere la raccolta.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.Int32)">
      <summary>Restituisce l'oggetto <see cref="T:System.Data.Common.DbParameter" /> nell'insieme in corrispondenza dell'indice specificato.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbParameter" /> presente nell'insieme in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice della classe <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.String)">
      <summary>Restituisce la classe <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</returns>
      <param name="parameterName">Nome della classe <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.Object)">
      <summary>Restituisce l'indice dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> specificato.</summary>
      <returns>Indice dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> specificato.</returns>
      <param name="value">Oggetto <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.String)">
      <summary>Restituisce l'indice dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</summary>
      <returns>Indice dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</returns>
      <param name="parameterName">Nome dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Insert(System.Int32,System.Object)">
      <summary>Inserisce l'indice specificato dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato nell'insieme in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in corrispondenza del quale inserire l'oggetto <see cref="T:System.Data.Common.DbParameter" />.</param>
      <param name="value">Oggetto <see cref="T:System.Data.Common.DbParameter" /> da inserire nell'insieme.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.Int32)">
      <summary>Ottiene e imposta la classe <see cref="T:System.Data.Common.DbParameter" /> in corrispondenza dell'indice specificato.</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice in base zero del parametro.</param>
      <exception cref="T:System.IndexOutOfRangeException">L'indice specificato non esiste. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.String)">
      <summary>Ottiene e imposta la classe <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</summary>
      <returns>Classe <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato.</returns>
      <param name="parameterName">Nome del parametro.</param>
      <exception cref="T:System.IndexOutOfRangeException">L'indice specificato non esiste. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Remove(System.Object)">
      <summary>Rimuove l'oggetto <see cref="T:System.Data.Common.DbParameter" /> specificato dall'insieme.</summary>
      <param name="value">Oggetto <see cref="T:System.Data.Common.DbParameter" /> da rimuovere.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.Int32)">
      <summary>Rimuove l'oggetto <see cref="T:System.Data.Common.DbParameter" /> dall'insieme in corrispondenza dell'indice specificato.</summary>
      <param name="index">Indice in cui si trova l'oggetto <see cref="T:System.Data.Common.DbParameter" />.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.String)">
      <summary>Rimuove l'oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato dall'insieme.</summary>
      <param name="parameterName">Nome dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> da rimuovere.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
      <summary>Imposta l'oggetto <see cref="T:System.Data.Common.DbParameter" /> in corrispondenza dell'indice specificato su un nuovo valore. </summary>
      <param name="index">Indice in cui si trova l'oggetto <see cref="T:System.Data.Common.DbParameter" />.</param>
      <param name="value">Nuovo valore <see cref="T:System.Data.Common.DbParameter" />.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
      <summary>Imposta l'oggetto <see cref="T:System.Data.Common.DbParameter" /> con il nome specificato su un nuovo valore.</summary>
      <param name="parameterName">Nome dell'oggetto <see cref="T:System.Data.Common.DbParameter" /> nell'insieme.</param>
      <param name="value">Nuovo valore <see cref="T:System.Data.Common.DbParameter" />.</param>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.SyncRoot">
      <summary>Specifica la classe <see cref="T:System.Object" /> da utilizzare per sincronizzare l'accesso all'insieme.</summary>
      <returns>Classe <see cref="T:System.Object" /> da utilizzare per sincronizzare l'accesso alla classe <see cref="T:System.Data.Common.DbParameterCollection" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#Item(System.Int32)">
      <summary>Ottiene o imposta l'elemento in corrispondenza dell'indice specificato.</summary>
      <returns>Elemento in corrispondenza dell'indice specificato.</returns>
      <param name="index">Indice a base zero dell'elemento da ottenere o impostare.</param>
    </member>
    <member name="T:System.Data.Common.DbProviderFactory">
      <summary>Rappresenta un gruppo di metodi per la creazione di istanze dell'implementazione di un provider delle classi di origini dati.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.#ctor">
      <summary>Inizializza una nuova istanza di una classe <see cref="T:System.Data.Common.DbProviderFactory" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateCommand">
      <summary>Restituisce una nuova istanza della classe del provider che implementa la classe <see cref="T:System.Data.Common.DbCommand" />.</summary>
      <returns>Nuova istanza di <see cref="T:System.Data.Common.DbCommand" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnection">
      <summary>Restituisce una nuova istanza della classe del provider che implementa la classe <see cref="T:System.Data.Common.DbConnection" />.</summary>
      <returns>Nuova istanza di <see cref="T:System.Data.Common.DbConnection" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnectionStringBuilder">
      <summary>Restituisce una nuova istanza della classe del provider che implementa la classe <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</summary>
      <returns>Nuova istanza di <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateParameter">
      <summary>Restituisce una nuova istanza della classe del provider che implementa la classe <see cref="T:System.Data.Common.DbParameter" />.</summary>
      <returns>Nuova istanza di <see cref="T:System.Data.Common.DbParameter" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbTransaction">
      <summary>Classe base per una transazione. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.#ctor">
      <summary>Inizializza un nuovo oggetto <see cref="T:System.Data.Common.DbTransaction" />.</summary>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Commit">
      <summary>Esegue il commit della transazione di database.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.Connection">
      <summary>Specifica l'oggetto <see cref="T:System.Data.Common.DbConnection" /> associato alla transazione.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbConnection" /> associato alla transazione.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.DbConnection">
      <summary>Specifica l'oggetto <see cref="T:System.Data.Common.DbConnection" /> associato alla transazione.</summary>
      <returns>Oggetto <see cref="T:System.Data.Common.DbConnection" /> associato alla transazione.</returns>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose">
      <summary>Consente di liberare le risorse non gestite utilizzate dalla classe <see cref="T:System.Data.Common.DbTransaction" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose(System.Boolean)">
      <summary>Rilascia le risorse non gestite utilizzate da <see cref="T:System.Data.Common.DbTransaction" /> e, facoltativamente, le risorse gestite.</summary>
      <param name="disposing">true se questo metodo rilascia tutte le risorse contenute da qualsiasi oggetto gestito a cui questa classe <see cref="T:System.Data.Common.DbTransaction" /> fa riferimento.</param>
    </member>
    <member name="P:System.Data.Common.DbTransaction.IsolationLevel">
      <summary>Specifica l'oggetto <see cref="T:System.Data.IsolationLevel" /> per questa transazione.</summary>
      <returns>Oggetto <see cref="T:System.Data.IsolationLevel" /> per questa transazione.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Rollback">
      <summary>Esegue il rollback di una transazione da uno stato in sospeso.</summary>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>