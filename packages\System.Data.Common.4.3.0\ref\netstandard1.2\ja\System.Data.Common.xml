﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Common</name>
  </assembly>
  <members>
    <member name="T:System.DBNull">
      <summary>存在しない値を表します。このクラスは継承できません。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString">
      <summary>空の文字列 (<see cref="F:System.String.Empty" />) を返します。</summary>
      <returns>空の文字列 (<see cref="F:System.String.Empty" />)。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString(System.IFormatProvider)">
      <summary>指定した <see cref="T:System.IFormatProvider" /> を使用して、空の文字列を返します。</summary>
      <returns>空の文字列 (<see cref="F:System.String.Empty" />)。</returns>
      <param name="provider">文字列の書式を指定するために使用する <see cref="T:System.IFormatProvider" />。またはオペレーティング システムの現在のロケール設定から書式情報を取得するための null。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.DBNull.Value">
      <summary>
        <see cref="T:System.DBNull" /> クラスの唯一のインスタンスを表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.CommandBehavior">
      <summary>クエリの結果とそれがデータベースに与える影響を記述します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandBehavior.CloseConnection">
      <summary>このコマンドを実行した場合は、関連付けられている DataReader オブジェクトを終了すると、関連付けられている Connection オブジェクトが終了します。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.Default">
      <summary>このクエリは複数の結果セットを返すことがあります。このクエリを実行すると、データベースの状態に影響を与えることがあります。Default は <see cref="T:System.Data.CommandBehavior" /> フラグを設定しないので、ExecuteReader(CommandBehavior.Default) の呼び出しは、機能的には ExecuteReader() の呼び出しと同じです。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.KeyInfo">
      <summary>このクエリは列と主キーの情報を返します。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SchemaOnly">
      <summary>クエリは列情報だけを返します。<see cref="F:System.Data.CommandBehavior.SchemaOnly" /> を使用している場合、.NET Framework SQL Server 用データ プロバイダーは、実行するステートメントを SET FMTONLY ON で開始します。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SequentialAccess">
      <summary>DataReader が大きなバイナリ値が格納されている列を含む行を処理できるようにします。SequentialAccess を使用すると、DataReader は行全体を読み込むのではなく、ストリームとしてデータを読み込むことができます。さらに、GetBytes メソッドまたは GetChars メソッドを使用すると、読み込み動作を開始するバイト位置、および返すデータの制限付きバッファー サイズを指定できます。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleResult">
      <summary>このクエリは単一の結果セットを返します。</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleRow">
      <summary>クエリは、最初の結果セットの 1 行を返します。このクエリを実行すると、データベースの状態に影響を与えることがあります。一部の .NET Framework データ プロバイダーではこの情報を使用してコマンドのパフォーマンスを最適化することもできますが、この情報の使用は必須ではありません。<see cref="T:System.Data.OleDb.OleDbCommand" /> オブジェクトの <see cref="M:System.Data.OleDb.OleDbCommand.ExecuteReader" /> メソッドで <see cref="F:System.Data.CommandBehavior.SingleRow" /> を指定すると、OLE DB IRow インターフェイスが使用できる場合、.NET Framework OLE DB 用データ プロバイダーは、このインターフェイスを使用してバインドを実行します。それ以外の場合は、IRowset インターフェイスを使用します。単一行だけを返す SQL ステートメントを使用する場合は、<see cref="F:System.Data.CommandBehavior.SingleRow" /> を指定すると、アプリケーションのパフォーマンスも向上します。複数の結果セットを返すクエリを実行する場合は、SingleRow を指定できます。その場合、複数の結果セットの SQL クエリと単一行の両方が指定されていると、返される結果には、最初の結果セットの 1 行目のみが含まれます。クエリの他の結果セットは返されません。</summary>
    </member>
    <member name="T:System.Data.CommandType">
      <summary>コマンド文字列の解釈方法を指定します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandType.StoredProcedure">
      <summary>ストアド プロシージャの名前。</summary>
    </member>
    <member name="F:System.Data.CommandType.TableDirect">
      <summary>テーブルの名前。</summary>
    </member>
    <member name="F:System.Data.CommandType.Text">
      <summary>SQL テキスト コマンド (既定)。</summary>
    </member>
    <member name="T:System.Data.ConnectionState">
      <summary>データ ソースへの接続の現在の状態を記述します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ConnectionState.Broken">
      <summary>データ ソースへの接続が断絶しています。この状態は接続が開かれているときだけ発生します。この状態の接続は、いったん閉じてから再び開くことができる場合があります。この値は製品の将来のバージョンで使用するために予約されています。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Closed">
      <summary>接続が閉じています。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Connecting">
      <summary>接続オブジェクトがデータ ソースに接続しています。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Executing">
      <summary>接続オブジェクトがコマンドを実行しています。この値は製品の将来のバージョンで使用するために予約されています。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Fetching">
      <summary>接続オブジェクトがデータを検索しています。この値は製品の将来のバージョンで使用するために予約されています。</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Open">
      <summary>接続が開いています。</summary>
    </member>
    <member name="T:System.Data.DbType">
      <summary>.NET Framework データ プロバイダーのフィールド、プロパティ、または Parameter オブジェクトのデータ型を指定します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.DbType.AnsiString">
      <summary>1 から 8,000 文字の範囲内の非 Unicode 文字の可変長ストリーム。</summary>
    </member>
    <member name="F:System.Data.DbType.AnsiStringFixedLength">
      <summary>非 Unicode 文字の固定長ストリーム。</summary>
    </member>
    <member name="F:System.Data.DbType.Binary">
      <summary>1 から 8,000 バイトの範囲内のバイナリ データの可変長ストリーム。</summary>
    </member>
    <member name="F:System.Data.DbType.Boolean">
      <summary>true または false のブール値を表す単純型。</summary>
    </member>
    <member name="F:System.Data.DbType.Byte">
      <summary>値が 0 から 255 までの範囲内の 8 ビット符号なし整数。</summary>
    </member>
    <member name="F:System.Data.DbType.Currency">
      <summary>精度が通貨単位の 1/10,000 の、-2 63 (-922,337,203,685,477.5808) から 2 63 -1 (+922,337,203,685,477.5807) までの範囲内の通貨値。</summary>
    </member>
    <member name="F:System.Data.DbType.Date">
      <summary>日付の値を表す型。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime">
      <summary>日時の値を表す型。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime2">
      <summary>日付と時刻のデータ。日付の値の範囲は、AD 1 年 1 月 1 日から AD 9999 年 12 月 31 日です。時刻の値の範囲は、00:00:00 から 23:59:59.9999999 で、精度は 100 ナノ秒です。</summary>
    </member>
    <member name="F:System.Data.DbType.DateTimeOffset">
      <summary>タイム ゾーンに対応した日付と時刻。日付の値の範囲は、AD 1 年 1 月 1 日から AD 9999 年 12 月 31 日です。時刻の値の範囲は、00:00:00 から 23:59:59.9999999 で、精度は 100 ナノ秒です。タイム ゾーンの値の範囲は、-14:00 から +14:00 です。</summary>
    </member>
    <member name="F:System.Data.DbType.Decimal">
      <summary>1.0 × 10 -28 から概数 7.9 × 10 28 までの範囲で、有効桁数が 28 または 29 の値を表す単純型。</summary>
    </member>
    <member name="F:System.Data.DbType.Double">
      <summary>概数 5.0 × 10 -324 から 1.7 × 10 308 までの範囲で、有効桁数が 15 または 16 の値を表す浮動小数点型。</summary>
    </member>
    <member name="F:System.Data.DbType.Guid">
      <summary>グローバル一意識別子 (GUID)。</summary>
    </member>
    <member name="F:System.Data.DbType.Int16">
      <summary>-32768 から 32767 までの値を保持する符号付き 16 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.Int32">
      <summary>-2147483648 から 2147483647 までの値を保持する符号付き 32 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.Int64">
      <summary>-9223372036854775808 から 9223372036854775807 までの値を保持する符号付き 64 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.Object">
      <summary>別の DbType 値で明示的に表されていない参照型または値型を表す汎用型。</summary>
    </member>
    <member name="F:System.Data.DbType.SByte">
      <summary>-128 から 127 までの値を保持する符号付き 8 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.Single">
      <summary>概数 1.5 × 10 -45 から 3.4 × 10 38 までの範囲で、有効桁数が 7 の値を表す浮動小数点型。</summary>
    </member>
    <member name="F:System.Data.DbType.String">
      <summary>Unicode 文字列を表す型。</summary>
    </member>
    <member name="F:System.Data.DbType.StringFixedLength">
      <summary>Unicode 文字の固定長文字列。</summary>
    </member>
    <member name="F:System.Data.DbType.Time">
      <summary>SQL Server の DateTime 値を表す型。SQL Server の time 値を使用する場合は、<see cref="F:System.Data.SqlDbType.Time" /> を使用してください。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt16">
      <summary>0 から 65535 までの値を保持する符号なし 16 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt32">
      <summary>0 から 4294967295 までの値を保持する符号なし 32 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.UInt64">
      <summary>0 から 18446744073709551615 までの値を保持する符号なし 64 ビット整数を表す整数型。</summary>
    </member>
    <member name="F:System.Data.DbType.VarNumeric">
      <summary>可変長数値。</summary>
    </member>
    <member name="F:System.Data.DbType.Xml">
      <summary>解析された XML ドキュメントまたは XML フラグメントの表現。</summary>
    </member>
    <member name="T:System.Data.IsolationLevel">
      <summary>接続のトランザクション ロック動作を指定します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.IsolationLevel.Chaos">
      <summary>これより分離性の高いトランザクションからの保留中の変更に対しては上書きできません。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadCommitted">
      <summary>データが読み込まれている間、ダーティ読み込みを防ぐために共有ロックが保持されますが、トランザクションが終了する前にデータを変更できます。このため、読み込みは繰り返されません。また実際には存在しないデータを生成できます。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadUncommitted">
      <summary>ダーティ読み込みができます。つまり、共有ロックが発行されておらず、排他ロックが有効ではありません。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.RepeatableRead">
      <summary>他のユーザーがデータを更新できないようにするために、クエリで使用するすべてのデータをロックします。繰り返し不能読み込みはできませんが、実際には存在しない行を生成できます。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Serializable">
      <summary>
        <see cref="T:System.Data.DataSet" /> にレンジ ロックがかけられ、トランザクションが完了するまで、他のユーザーは行を更新したりデータセットに行を挿入できません。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Snapshot">
      <summary>あるアプリケーションで変更中のデータを他のアプリケーションから読み取ることができるように、そのデータのバージョンを保存して、ブロッキングを減らします。この場合、クエリを再実行しても、あるトランザクションで加えられた変更を、他のトランザクションで表示できません。</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Unspecified">
      <summary>指定した分離レベルとは異なる分離レベルが使用されていますが、レベルを確認できません。</summary>
    </member>
    <member name="T:System.Data.ParameterDirection">
      <summary>
        <see cref="T:System.Data.DataSet" /> に関連するクエリ内のパラメーターの型を指定します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ParameterDirection.Input">
      <summary>このパラメーターは入力パラメーターです。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.InputOutput">
      <summary>このパラメーターは入力または出力のどちらでもできます。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.Output">
      <summary>このパラメーターは出力パラメーターです。</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.ReturnValue">
      <summary>このパラメーターは、ストアド プロシージャ、組み込み関数、ユーザー定義関数などの演算からの戻り値を表します。</summary>
    </member>
    <member name="T:System.Data.StateChangeEventArgs">
      <summary>.NET Framework データ プロバイダーの状態変化イベントにデータを提供します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.StateChangeEventArgs.#ctor(System.Data.ConnectionState,System.Data.ConnectionState)">
      <summary>オブジェクトの元の状態と現在の状態が指定されている場合は、<see cref="T:System.Data.StateChangeEventArgs" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="originalState">
        <see cref="T:System.Data.ConnectionState" /> 値のいずれか。</param>
      <param name="currentState">
        <see cref="T:System.Data.ConnectionState" /> 値のいずれか。</param>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.CurrentState">
      <summary>接続の新しい状態を取得します。イベントが発生したときは、接続オブジェクトが既に新しい状態になっています。</summary>
      <returns>
        <see cref="T:System.Data.ConnectionState" /> 値のいずれか。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.OriginalState">
      <summary>接続の元の状態を取得します。</summary>
      <returns>
        <see cref="T:System.Data.ConnectionState" /> 値のいずれか。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.StateChangeEventHandler">
      <summary>
        <see cref="E:System.Data.Common.DbConnection.StateChange" /> イベントを処理するメソッドを表します。</summary>
      <param name="sender">イベントのソース。</param>
      <param name="e">イベント データを格納している <see cref="T:System.Data.StateChangeEventArgs" />。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.UpdateRowSource">
      <summary>更新する行にクエリ コマンドの結果を適用する方法を指定します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.UpdateRowSource.Both">
      <summary>出力パラメーターと最初に返された行の両方が、<see cref="T:System.Data.DataSet" /> 内の変更された行に割り当てられます。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.FirstReturnedRecord">
      <summary>最初に返された行のデータが、<see cref="T:System.Data.DataSet" /> 内の変更された行に割り当てられます。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.None">
      <summary>返されたパラメーターまたは行は無視されます。</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.OutputParameters">
      <summary>出力パラメーターが <see cref="T:System.Data.DataSet" /> 内の変更された行に割り当てられます。</summary>
    </member>
    <member name="T:System.Data.Common.DbCommand">
      <summary>データ ソースに対して実行する SQL ステートメントまたはストアド プロシージャを表します。コマンドを表すデータベース固有のクラスの基本クラスを提供します。<see cref="Overload:System.Data.Common.DbCommand.ExecuteNonQueryAsync" /></summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbCommand" /> オブジェクトのインスタンスを構築します。</summary>
    </member>
    <member name="M:System.Data.Common.DbCommand.Cancel">
      <summary>
        <see cref="T:System.Data.Common.DbCommand" /> の実行のキャンセルを試行します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandText">
      <summary>データ ソースに対して実行するテキスト コマンドを取得または設定します。</summary>
      <returns>実行するテキスト コマンド。既定値は、空の文字列 ("") です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandTimeout">
      <summary>コマンドを実行する試みを終了してエラーが生成されるまでの待機時間を取得または設定します。</summary>
      <returns>コマンドが実行されるまでの待機時間 (秒)。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandType">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.CommandText" /> プロパティの解釈方法を指示または指定します。</summary>
      <returns>
        <see cref="T:System.Data.CommandType" /> 値のいずれか。既定値は、Text です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Connection">
      <summary>この <see cref="T:System.Data.Common.DbCommand" /> で使用される <see cref="T:System.Data.Common.DbConnection" /> を取得または設定します。</summary>
      <returns>データ ソースへの接続。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateDbParameter">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの新しいインスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateParameter">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの新しいインスタンスを作成します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbConnection">
      <summary>この <see cref="T:System.Data.Common.DbCommand" /> で使用される <see cref="T:System.Data.Common.DbConnection" /> を取得または設定します。</summary>
      <returns>データ ソースへの接続。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbParameterCollection">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのコレクションを取得します。</summary>
      <returns>SQL ステートメントまたはストアド プロシージャのパラメーター。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbTransaction">
      <summary>この <see cref="T:System.Data.Common.DbCommand" /> オブジェクトを実行する <see cref="P:System.Data.Common.DbCommand.DbTransaction" /> を取得または設定します。</summary>
      <returns>.NET Framework データ プロバイダーの Command オブジェクトが実行されるトランザクション。既定値は、null 参照 (Visual Basic の場合は Nothing) です。</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DesignTimeVisible">
      <summary>カスタマイズしたインターフェイス コントロールにコマンド オブジェクトを表示する必要があるかどうかを示す値を取得または設定します。</summary>
      <returns>コマンド オブジェクトをコントロールに表示する必要がある場合は true、それ以外の場合は false。既定値は、true です。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Dispose"></member>
    <member name="M:System.Data.Common.DbCommand.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
      <summary>接続に対してコマンド テキストを実行します。</summary>
      <returns>操作を表すタスク。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> のインスタンス。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>プロバイダーは、<see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> のオーバーロードに既定以外の実装を提供するには、このメソッドを実装する必要があります。既定の実装は、<see cref="M:System.Data.Common.DbCommand.ExecuteReader" /> 同期メソッドを呼び出し、完了したタスクを返します。呼び出し元のスレッドはブロックされます。既定の実装は、既に取り消されたキャンセル トークンを渡した場合、取り消されたタスクを返します。ExecuteReader によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。このメソッドは、早期に操作をキャンセルすることを要求するために使用できるキャンセル トークンを受け取ります。実装は、この要求を無視する場合があります。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="behavior">ステートメントの実行とデータ取得に関するオプション。</param>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQuery">
      <summary>接続オブジェクトに対して SQL ステートメントを実行します。</summary>
      <returns>影響を受けた行数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync">
      <summary>接続オブジェクトに対して SQL ステートメントを実行する <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> の非同期バージョン。CancellationToken.None を使用して、<see cref="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>これは <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> の非同期バージョンです。プロバイダーは、適切な実装でオーバーライドする必要があります。オプションで、キャンセル トークンを無視できます。既定の実装は、<see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> 同期メソッドを呼び出し、完了したタスクを返します。呼び出し元のスレッドはブロックされます。既定の実装は、既に取り消されたキャンセル トークンを渡した場合、取り消されたタスクを返します。<see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。返されたタスクが完了するまで DbCommand オブジェクトの他のメソッドとプロパティを呼び出さないでください。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.Connection" /> に対して <see cref="P:System.Data.Common.DbCommand.CommandText" /> を実行し、<see cref="T:System.Data.Common.DbDataReader" /> を返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.Connection" /> に対して <see cref="P:System.Data.Common.DbCommand.CommandText" /> を実行し、<see cref="T:System.Data.CommandBehavior" /> 値の 1 つを使用して <see cref="T:System.Data.Common.DbDataReader" /> を返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> オブジェクト。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 値のいずれか。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.Connection" /> に対して <see cref="P:System.Data.Common.DbCommand.CommandText" /> を実行し、<see cref="T:System.Data.Common.DbDataReader" /> を返す <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> の非同期バージョン。CancellationToken.None を使用して、<see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.Connection" /> に対して <see cref="P:System.Data.Common.DbCommand.CommandText" /> を実行し、<see cref="T:System.Data.Common.DbDataReader" /> を返す <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> の非同期バージョン。<see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 値のいずれか。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>
        <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="behavior">
        <see cref="T:System.Data.CommandBehavior" /> 値のいずれか。</param>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>
        <see cref="P:System.Data.Common.DbCommand.Connection" /> に対して <see cref="P:System.Data.Common.DbCommand.CommandText" /> を実行し、<see cref="T:System.Data.Common.DbDataReader" /> を返す <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" /> の非同期バージョン。このメソッドは操作を取り消す通知を配信します。<see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
      <exception cref="T:System.ArgumentException">無効な <see cref="T:System.Data.CommandBehavior" /> 値。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalar">
      <summary>クエリを実行し、そのクエリが返す結果セットの最初の行にある最初の列を返します。他のすべての列および行は無視されます。</summary>
      <returns>結果セット内の最初の行の最初の列。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync">
      <summary>クエリを実行し、クエリで返された結果セットの最初の行の最初の列を返す <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> の非同期バージョン。他のすべての列および行は無視されます。CancellationToken.None を使用して、<see cref="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>これは <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> の非同期バージョンです。プロバイダーは、適切な実装でオーバーライドする必要があります。オプションで、キャンセル トークンを無視できます。既定の実装は、<see cref="M:System.Data.Common.DbCommand.ExecuteScalar" /> 同期メソッドを呼び出し、完了したタスクを返します。呼び出し元のスレッドはブロックされます。既定の実装は、既に取り消されたキャンセル トークンを渡した場合、取り消されたタスクを返します。ExecuteScalar によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。返されたタスクが完了するまで DbCommand オブジェクトの他のメソッドとプロパティを呼び出さないでください。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">キャンセル要求を監視するためのトークン。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="P:System.Data.Common.DbCommand.Parameters">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのコレクションを取得します。パラメーターの詳細については、「パラメーターおよびパラメーターのデータ型の構成」を参照してください。</summary>
      <returns>SQL ステートメントまたはストアド プロシージャのパラメーター。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Prepare">
      <summary>データ ソースに対する準備済み (コンパイル済み) のコマンドを作成します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Transaction">
      <summary>この <see cref="T:System.Data.Common.DbCommand" /> オブジェクトを実行する <see cref="T:System.Data.Common.DbTransaction" /> を取得または設定します。</summary>
      <returns>.NET Framework データ プロバイダーの Command オブジェクトが実行されるトランザクション。既定値は、null 参照 (Visual Basic の場合は Nothing) です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.UpdatedRowSource">
      <summary>
        <see cref="T:System.Data.Common.DbDataAdapter" /> の Update メソッドがコマンドの結果を使用するときにコマンドの結果を <see cref="T:System.Data.DataRow" /> に適用する方法を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.UpdateRowSource" /> 値のいずれか。コマンドが自動的に生成される場合を除き、既定値は Both です。自動的に生成される場合、既定値は None です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbConnection">
      <summary>データベースへの接続を表します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbConnection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
      <summary>データベース トランザクションを開始します。</summary>
      <returns>新しいトランザクションを表すオブジェクト。</returns>
      <param name="isolationLevel">トランザクションの分離レベルを指定します。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction">
      <summary>データベース トランザクションを開始します。</summary>
      <returns>新しいトランザクションを表すオブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>分離レベルを指定して、データベース トランザクションを開始します。</summary>
      <returns>新しいトランザクションを表すオブジェクト。</returns>
      <param name="isolationLevel">トランザクションの分離レベルを指定します。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.ChangeDatabase(System.String)">
      <summary>開いている接続の現在のデータベースを変更します。</summary>
      <param name="databaseName">使用する接続のデータベースの名前を指定します。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Close">
      <summary>データベースへの接続を閉じます。このメソッドは、開いている接続を閉じるための最も好ましいメソッドです。</summary>
      <exception cref="T:System.Data.Common.DbException">接続を開くときに、接続レベルのエラーが発生しました。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionString">
      <summary>接続を開くために使用する文字列を取得または設定します。</summary>
      <returns>初期接続を確立するために使用する接続文字列。接続文字列の正確な内容は、この接続の特定のデータ ソースに応じて異なります。既定値は、空の文字列です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionTimeout">
      <summary>試行を中断してエラーを生成するまでの、接続の確立時に待機する時間を取得します。</summary>
      <returns>接続が開かれるまでの待機時間 (秒)。既定値は、使用している特定の接続の種類により決まります。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateCommand">
      <summary>現在の接続に関連付けられている <see cref="T:System.Data.Common.DbCommand" /> オブジェクトを作成し、返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateDbCommand">
      <summary>現在の接続に関連付けられている <see cref="T:System.Data.Common.DbCommand" /> オブジェクトを作成し、返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnection.Database">
      <summary>接続が開いてから現在のデータベースの名前を取得するか、接続が開く前に接続文字列に指定されたデータベース名を取得します。</summary>
      <returns>現在のデータベース、または接続が開いてから使用するデータベースの名前。既定値は、空の文字列です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.DataSource">
      <summary>接続するデータベース サーバーの名前を取得します。</summary>
      <returns>接続するデータベース サーバーの名前。既定値は、空の文字列です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Dispose"></member>
    <member name="M:System.Data.Common.DbConnection.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbConnection.OnStateChange(System.Data.StateChangeEventArgs)">
      <summary>
        <see cref="E:System.Data.Common.DbConnection.StateChange" /> イベントを発生させます。</summary>
      <param name="stateChange">イベント データを格納している <see cref="T:System.Data.StateChangeEventArgs" />。</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.Open">
      <summary>
        <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> で指定した設定を使用して、データベース接続を開きます。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync">
      <summary>
        <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> で指定した設定を使用してデータベース接続を開く <see cref="M:System.Data.Common.DbConnection.Open" /> の非同期バージョン。このメソッドは、CancellationToken.None で仮想メソッド <see cref="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>これは <see cref="M:System.Data.Common.DbConnection.Open" /> の非同期バージョンです。プロバイダーは、適切な実装でオーバーライドする必要があります。オプションで、キャンセル トークン優先できます。既定の実装は同期 <see cref="M:System.Data.Common.DbConnection.Open" /> コールを呼び出し、完了したタスクを返します。既定の実装は、既に取り消された cancellationToken を渡した場合、取り消されたタスクを返します。Open によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。返されたタスクが完了するまで DbConnection オブジェクトの他のメソッドとプロパティを呼び出さないでください。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">取り消し命令。</param>
    </member>
    <member name="P:System.Data.Common.DbConnection.ServerVersion">
      <summary>オブジェクトが接続しているサーバーのバージョンを表す文字列を取得します。</summary>
      <returns>データベースのバージョン。返される文字列の形式は、使用している特定の接続の種類に応じて決まります。</returns>
      <exception cref="T:System.InvalidOperationException">返されたタスクが完了していない間に <see cref="P:System.Data.Common.DbConnection.ServerVersion" /> が呼び出されたため、<see cref="Overload:System.Data.Common.DbConnection.OpenAsync" /> の呼び出し後に接続が開きませんでした。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.State">
      <summary>接続の状態を説明する文字列を取得します。</summary>
      <returns>接続の状態。返される文字列の形式は、使用している特定の接続の種類に応じて決まります。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="E:System.Data.Common.DbConnection.StateChange">
      <summary>イベントの状態が変更したときに発生します。</summary>
    </member>
    <member name="T:System.Data.Common.DbConnectionStringBuilder">
      <summary>厳密に型指定された接続文字列ビルダーの基本クラスを提供します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Add(System.String,System.Object)">
      <summary>指定したキーおよび値を持つエントリを <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に追加します。</summary>
      <param name="keyword">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に追加するキー。</param>
      <param name="value">指定したキーの値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> が null 参照 (Visual Basic の場合は Nothing) です。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> は読み取り専用です。または<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> が固定サイズです。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.AppendKeyValuePair(System.Text.StringBuilder,System.String,System.String)">
      <summary>効率的かつ安全に、既存の <see cref="T:System.Text.StringBuilder" /> オブジェクトにキーと値を追加できます。</summary>
      <param name="builder">キー/値ペアを追加する対象の <see cref="T:System.Text.StringBuilder" />。</param>
      <param name="keyword">追加するキー。</param>
      <param name="value">指定したキーの値。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Clear">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> インスタンスの内容を消去します。</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> は読み取り専用です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に関連付けられる接続文字列を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内に格納されているキー/値ペアから作成された、現在の接続文字列。既定値は、空の文字列です。</returns>
      <exception cref="T:System.ArgumentException">無効な接続文字列が引数として指定されています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ContainsKey(System.String)">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に特定のキーが格納されているかどうかを判断します。</summary>
      <returns>指定したキーを持つエントリが <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に格納されている場合は true。それ以外の場合は false。</returns>
      <param name="keyword">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内で検索されるキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> が null 参照 (Visual Basic の場合は Nothing) です。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Count">
      <summary>
        <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> プロパティに格納されている、現在のキー数を取得します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> インスタンスで維持されている接続文字列に格納されているキーの数。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.EquivalentTo(System.Data.Common.DbConnectionStringBuilder)">
      <summary>この <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> オブジェクト内の接続情報と、指定したオブジェクト内の接続情報を比較します。</summary>
      <returns>2 つの <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> オブジェクトの接続情報によって同じ接続文字列が得られる場合は true。それ以外の場合は false。</returns>
      <param name="connectionStringBuilder">この <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> オブジェクトと比較される <see cref="T:System.Data.Common.DbConnectionStringBuilder" />。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Item(System.String)">
      <summary>指定したキーに関連付けられている値を取得または設定します。</summary>
      <returns>指定したキーに関連付けられている値。指定したキーが見つからない場合、そのキーを取得しようとした場合は null 参照 (Visual Basic では Nothing) が返され、そのキーを設定しようとした場合は、指定したキーを使用して新しい要素が作成されます。null (Visual Basic では Nothing) キーを渡すと、<see cref="T:System.ArgumentNullException" /> がスローされます。null 値を割り当てると、キー/値ペアが削除されます。</returns>
      <param name="keyword">取得または設定する項目のキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> が null 参照 (Visual Basic の場合は Nothing) です。</exception>
      <exception cref="T:System.NotSupportedException">このプロパティが設定されていますが、<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> が読み取り専用です。またはこのプロパティが設定されていますが、<paramref name="keyword" /> がコレクション内に存在しません。また、<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> が固定サイズです。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Keys">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内のキーを格納している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内のキーを格納している <see cref="T:System.Collections.ICollection" />。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Remove(System.String)">
      <summary>指定したキーを持つエントリを <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> インスタンスから削除します。</summary>
      <returns>接続文字列内にキーが存在し、削除された場合は true。キーが存在しなかった場合は false。</returns>
      <param name="keyword">この <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> の接続文字列から取り除く、キー/値ペアに対するキー。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> が null (Visual Basic の場合は Nothing) です。</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> が読み取り専用、または <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> が固定サイズです。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>指定されたキーが、この <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> インスタンスに存在するかどうかを示します。</summary>
      <returns>指定したキーを持つエントリが <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に格納されている場合は true。それ以外の場合は false。</returns>
      <param name="keyword">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内で検索されるキー。</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>
        <see cref="T:System.Collections.ICollection" /> の要素を <see cref="T:System.Array" /> にコピーします。<see cref="T:System.Array" /> の特定のインデックスからコピーが開始されます。</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" /> から要素がコピーされる 1 次元の <see cref="T:System.Array" />。<see cref="T:System.Array" /> には、0 から始まるインデックス番号が必要です。</param>
      <param name="index">コピーの開始位置となる、<paramref name="array" /> 内の 0 から始まるインデックス。</param>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) かどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスが同期されている (スレッド セーフである) 場合は true。それ以外の場合は false。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクトを取得します。</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" /> へのアクセスを同期するために使用できるオブジェクト。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>指定したキーおよび値を持つ要素を <see cref="T:System.Collections.IDictionary" /> オブジェクトに追加します。</summary>
      <param name="keyword">追加する要素のキーとして使用する <see cref="T:System.Object" />。</param>
      <param name="value">追加する要素の値として使用する <see cref="T:System.Object" />。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>指定したキーを持つ要素が <see cref="T:System.Collections.IDictionary" /> オブジェクトに格納されているかどうかを確認します。</summary>
      <returns>指定したキーを持つ要素を <see cref="T:System.Collections.IDictionary" /> が保持している場合は true。それ以外の場合は false。</returns>
      <param name="keyword">
        <see cref="T:System.Collections.IDictionary" /> オブジェクト内で検索されるキー。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionary" /> オブジェクトの <see cref="T:System.Collections.IDictionaryEnumerator" /> オブジェクトを返します。</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" /> オブジェクトの <see cref="T:System.Collections.IDictionaryEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Item(System.Object)">
      <summary>指定したキーを持つ要素を取得または設定します。</summary>
      <returns>指定したキーを持つ要素。</returns>
      <param name="keyword">取得または設定する要素のキー。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>指定したキーを持つ要素を <see cref="T:System.Collections.IDictionary" /> オブジェクトから削除します。</summary>
      <param name="keyword">削除する要素のキー。</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IEnumerable#GetEnumerator">
      <summary>コレクションを反復処理する列挙子を返します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ToString">
      <summary>この <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> に関連付けられている接続文字列を返します。</summary>
      <returns>現在の <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" /> プロパティ。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>指定されたキーに対応する値を <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> から取得します。</summary>
      <returns>
        <paramref name="keyword" /> が接続文字列に存在する場合は true。それ以外の場合は false。</returns>
      <param name="keyword">取得する項目のキー。</param>
      <param name="value">
        <paramref name="key" /> に対応する値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> に null 値 (Visual Basic の場合は Nothing) が含まれています。</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Values">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内の値を格納している <see cref="T:System.Collections.ICollection" /> を取得します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> 内の値を格納している <see cref="T:System.Collections.ICollection" />。</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="T:System.Data.Common.DbDataReader">
      <summary>データ ソースから行の前方向ストリームを読み取ります。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbDataReader" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Depth">
      <summary>現在の行の入れ子の深さを示す値を取得します。</summary>
      <returns>現在の行の入れ子の深さ。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose">
      <summary>
        <see cref="T:System.Data.Common.DbDataReader" /> クラスの現在のインスタンスによって使用されているすべてのリソースを解放します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Data.Common.DbDataReader" /> によって使用されているマネージ リソースを解放し、オプションでアンマネージ リソースも解放します。</summary>
      <param name="disposing">マネージ リソースとアンマネージ リソースを解放する場合は true。アンマネージ リソースだけを解放する場合は false。</param>
    </member>
    <member name="P:System.Data.Common.DbDataReader.FieldCount">
      <summary>現在の行の列数を取得します。</summary>
      <returns>現在の行の列数。</returns>
      <exception cref="T:System.NotSupportedException">SQL Server のインスタンスへの現在の接続がありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBoolean(System.Int32)">
      <summary>指定した列の値をブール値として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetByte(System.Int32)">
      <summary>指定した列の値をバイトとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>指定した列の <paramref name="dataOffset" /> で指定された位置から開始されるバイト ストリームを、バッファーの <paramref name="bufferOffset" /> で指定された開始位置に読み込みます。</summary>
      <returns>実際に読み取られたバイト数を返します。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <param name="dataOffset">読み取り操作を開始する行内のインデックス。</param>
      <param name="buffer">データのコピー先のバッファー。</param>
      <param name="bufferOffset">データのコピー先となるバッファーのインデックス。</param>
      <param name="length">読み取り対象の最大文字数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChar(System.Int32)">
      <summary>指定した列の値を単一の文字として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>指定した列の <paramref name="dataOffset" /> で指定された位置から開始される文字ストリームを、バッファーの <paramref name="bufferOffset" /> で指定された開始位置に読み込みます。</summary>
      <returns>実際に読み込まれた文字数を返します。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <param name="dataOffset">読み取り操作を開始する行内のインデックス。</param>
      <param name="buffer">データのコピー先のバッファー。</param>
      <param name="bufferOffset">データのコピー先となるバッファーのインデックス。</param>
      <param name="length">読み取り対象の最大文字数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetData(System.Int32)">
      <summary>要求された列の序数の <see cref="T:System.Data.Common.DbDataReader" /> オブジェクトを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> オブジェクト。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDataTypeName(System.Int32)">
      <summary>指定した列のデータ型の名前を取得します。</summary>
      <returns>データ型の名前を表す文字列。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDateTime(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.DateTime" /> オブジェクトとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDbDataReader(System.Int32)">
      <summary>プロバイダー固有の実装でオーバーライドできる、要求された列の序数の <see cref="T:System.Data.Common.DbDataReader" /> オブジェクトを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> オブジェクト。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDecimal(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.Decimal" /> オブジェクトとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDouble(System.Int32)">
      <summary>指定した列の値を倍精度浮動小数点数として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetEnumerator">
      <summary>データ リーダー内の行を反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" /> を返します。</summary>
      <returns>データ リーダー内の行を反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldType(System.Int32)">
      <summary>指定した列のデータ型を取得します。</summary>
      <returns>指定した列のデータ型。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValue``1(System.Int32)">
      <summary>指定された列の値を型として同期的に取得します。</summary>
      <returns>取得する列。</returns>
      <param name="ordinal">取得する列。</param>
      <typeparam name="T">指定された列の値を型として同期的に取得します。</typeparam>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.SqlClient.SqlDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしました。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> は SQL Server によって返される型を一致させたり、キャストできません。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)">
      <summary>指定された列の値を型として非同期的に取得します。</summary>
      <returns>返される値の型。</returns>
      <param name="ordinal">返される値の型。</param>
      <typeparam name="T">返される値の型。詳細については、次の「解説」を参照してください。</typeparam>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしました。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> がデータ ソースから返された型に一致しないか、キャストできません。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>指定された列の値を型として非同期的に取得します。</summary>
      <returns>返される値の型。</returns>
      <param name="ordinal">返される値の型。</param>
      <param name="cancellationToken">操作を取り消すことを示す通知を反映する取り消し命令。これは取り消しを保証しません。CancellationToken.None の設定は、このメソッドを <see cref="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)" /> と同じにします。返されたタスクを取り消し済みとしてマークする必要があります。</param>
      <typeparam name="T">返される値の型。詳細については、次の「解説」を参照してください。</typeparam>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしました。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> は、データ ソースによって返される型を一致させたり、キャストできません。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFloat(System.Int32)">
      <summary>指定した列の値を単精度浮動小数点数として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetGuid(System.Int32)">
      <summary>指定した列の値をグローバル一意識別子 (GUID) として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt16(System.Int32)">
      <summary>指定した列の値を 16 ビット符号付き整数として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt32(System.Int32)">
      <summary>指定した列の値を 32 ビット符号付き整数として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt64(System.Int32)">
      <summary>指定した列の値を 64 ビット符号付き整数として取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetName(System.Int32)">
      <summary>0 から始まる列の序数を指定して、列の名前を取得します。</summary>
      <returns>指定した列の名前。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetOrdinal(System.String)">
      <summary>列の名前を指定して、列の序数を取得します。</summary>
      <returns>インデックス番号が 0 から始まる列序数。</returns>
      <param name="name">列の名前。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定された名前は有効な列名ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>指定した列のプロバイダー固有のフィールドの型を返します。</summary>
      <returns>指定した列のデータ型を記述する <see cref="T:System.Type" /> オブジェクト。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.Object" /> のインスタンスとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>現在の行のコレクション内にあるすべてのプロバイダー固有の属性列を取得します。</summary>
      <returns>配列の <see cref="T:System.Object" /> のインスタンス数。</returns>
      <param name="values">属性列のコピー先の <see cref="T:System.Object" /> 配列。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetStream(System.Int32)">
      <summary>
        <see cref="T:System.IO.Stream" /> としてデータを取得します。</summary>
      <returns>返されたオブジェクト。</returns>
      <param name="ordinal">
        <see cref="T:System.IO.Stream" /> としてデータを取得します。</param>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしました。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
      <exception cref="T:System.InvalidCastException">返された型は、次のどの型でもありませんでした。binaryimagevarbinaryudt</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetString(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.String" /> のインスタンスとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.InvalidCastException">指定したキャストが有効ではありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)">
      <summary>
        <see cref="T:System.IO.TextReader" /> としてデータを取得します。</summary>
      <returns>返されたオブジェクト。</returns>
      <param name="ordinal">
        <see cref="T:System.IO.TextReader" /> としてデータを取得します。</param>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしました。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
      <exception cref="T:System.InvalidCastException">返された型は、次のどの型でもありませんでした。charncharntextnvarcharテキストvarchar</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValue(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.Object" /> のインスタンスとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValues(System.Object[])">
      <summary>オブジェクトの配列に現在行の列値を設定します。</summary>
      <returns>配列の <see cref="T:System.Object" /> のインスタンス数。</returns>
      <param name="values">属性列のコピー先の <see cref="T:System.Object" /> 配列。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.HasRows">
      <summary>この <see cref="T:System.Data.Common.DbDataReader" /> に 1 行以上の行が格納されているかどうかを示す値を取得します。</summary>
      <returns>1 行以上の行が <see cref="T:System.Data.Common.DbDataReader" /> に含まれている場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.IsClosed">
      <summary>
        <see cref="T:System.Data.Common.DbDataReader" /> が閉じているかどうかを示す値を取得します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbDataReader" /> が閉じている場合は true。それ以外の場合は false。</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Data.SqlClient.SqlDataReader" /> が閉じています。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)">
      <summary>列に格納されている値が存在しない値または欠損値かどうかを示す値を取得します。</summary>
      <returns>指定した列が <see cref="T:System.DBNull" /> と等価の場合は true。それ以外の場合は false。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)">
      <summary>列に格納されている値が存在しない値または欠損値かどうかを示す値を取得する <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> の非同期バージョン。</summary>
      <returns>指定した列の値が DBNull と等価である場合は true。それ以外の場合は false。</returns>
      <param name="ordinal">取得する、0 から始まる列。</param>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしています。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>列に格納されている値が存在しない値または欠損値かどうかを示す値を取得する <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" /> の非同期バージョン。必要に応じて、操作を取り消す必要があるという通知を送信します。</summary>
      <returns>指定した列の値が DBNull と等価である場合は true。それ以外の場合は false。</returns>
      <param name="ordinal">取得する、0 から始まる列。</param>
      <param name="cancellationToken">操作を取り消すことを示す通知を反映する取り消し命令。これは取り消しを保証しません。CancellationToken.None の設定は、このメソッドを <see cref="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)" /> と同じにします。返されたタスクを取り消し済みとしてマークする必要があります。</param>
      <exception cref="T:System.InvalidOperationException">データの取得中、接続は破棄されるか、または閉じられます。<see cref="T:System.Data.Common.DbDataReader" /> は、データの取得時に閉じます。読み取ることのできるデータはありません (たとえば、最初の <see cref="M:System.Data.Common.DbDataReader.Read" /> は呼び出されなかったか、false を返しました)。以前に読み取られた列をシーケンシャル モードで読み取ろうとしています。非同期操作が進行中でした。シーケンシャル モードで実行中、これはすべての Get* メソッドに適用されます。ストリームの読み取り中に呼び出すことができるためです。</exception>
      <exception cref="T:System.IndexOutOfRangeException">存在しない列を読み取ろうとしています。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.Int32)">
      <summary>指定した列の値を <see cref="T:System.Object" /> のインスタンスとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="ordinal">インデックス番号が 0 から始まる列序数。</param>
      <exception cref="T:System.IndexOutOfRangeException">渡されたインデックスが 0 から <see cref="P:System.Data.IDataRecord.FieldCount" /> の範囲にありません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.String)">
      <summary>指定した列の値を <see cref="T:System.Object" /> のインスタンスとして取得します。</summary>
      <returns>指定した列の値。</returns>
      <param name="name">列の名前。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定した名前の列が見つかりません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResult">
      <summary>バッチ ステートメントの結果を読み込むときに、リーダーを次の結果に進めます。</summary>
      <returns>次の結果セットがある場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync">
      <summary>バッチ ステートメントの結果を読み込むときに、リーダーを次の結果に進める <see cref="M:System.Data.Common.DbDataReader.NextResult" /> の非同期バージョン。CancellationToken.None を使用して、<see cref="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>これは <see cref="M:System.Data.Common.DbDataReader.NextResult" /> の非同期バージョンです。プロバイダーは、適切な実装でオーバーライドする必要があります。オプションで <paramref name="cancellationToken" /> を無視できます。既定の実装は <see cref="M:System.Data.Common.DbDataReader.NextResult" /> 同期メソッドを呼び出し、完了したタスクを返します。呼び出し元のスレッドはブロックされます。既定の実装は、既に取り消された <paramref name="cancellationToken" /> を渡した場合、取り消されたタスクを返します。<see cref="M:System.Data.Common.DbDataReader.NextResult" /> によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。返されたタスクが完了していないうちは、DbDataReader のオブジェクトの他のメソッドとプロパティを呼び出さないでください。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">取り消し命令。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Read">
      <summary>リーダーを結果セットの次のレコードに進めます。</summary>
      <returns>他の行が存在する場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync">
      <summary>結果セットの次のレコードにリーダーを進める <see cref="M:System.Data.Common.DbDataReader.Read" /> の非同期バージョン。このメソッドは、CancellationToken.None で <see cref="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)" /> を呼び出します。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>これは <see cref="M:System.Data.Common.DbDataReader.Read" /> の非同期バージョンです。プロバイダーは、適切な実装でオーバーライドする必要があります。オプションで cancellationToken を無視できます。既定の実装は、<see cref="M:System.Data.Common.DbDataReader.Read" /> 同期メソッドを呼び出し、完了したタスクを返します。呼び出し元のスレッドはブロックされます。既定の実装は、既に取り消された cancellationToken を渡した場合、取り消されたタスクを返します。Read によってスローされる例外は、返されたタスクの Exception プロパティを介して通信されます。返されたタスクが完了するまで DbDataReader オブジェクトの他のメソッドとプロパティを呼び出さないでください。</summary>
      <returns>非同期操作を表すタスク。</returns>
      <param name="cancellationToken">取り消し命令。</param>
      <exception cref="T:System.Data.Common.DbException">コマンド テキストの実行中にエラーが発生しました。</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.RecordsAffected">
      <summary>SQL ステートメントの実行によって変更、挿入、または削除された行の数を取得します。</summary>
      <returns>変更、挿入、または削除された行数。SELECT ステートメントの場合は -1、影響を受けた行がなかった場合またはステートメントが失敗した場合は 0。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.VisibleFieldCount">
      <summary>
        <see cref="T:System.Data.Common.DbDataReader" /> の非表示ではないフィールドの数を取得します。</summary>
      <returns>非表示ではないフィールドの数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbException">
      <summary>データ ソースに代わってスローされるすべての例外の基本クラス。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbException" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String)">
      <summary>
        <see cref="T:System.Data.Common.DbException" /> クラスの新しいインスタンスを、指定したエラー メッセージを使用して初期化します。</summary>
      <param name="message">この例外に表示されるメッセージ。</param>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String,System.Exception)">
      <summary>指定したエラー メッセージと、この例外の原因である内部例外への参照を使用して、<see cref="T:System.Data.Common.DbException" /> クラスの新しいインスタンスを初期化します。</summary>
      <param name="message">エラー メッセージ文字列。</param>
      <param name="innerException">内部例外参照。</param>
    </member>
    <member name="T:System.Data.Common.DbParameter">
      <summary>
        <see cref="T:System.Data.Common.DbCommand" /> のパラメーターと、オプションで <see cref="T:System.Data.DataSet" /> 列に対するマップを表します。パラメーターの詳細については、「パラメーターおよびパラメーターのデータ型の構成」を参照してください。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameter.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="P:System.Data.Common.DbParameter.DbType">
      <summary>パラメーターの <see cref="T:System.Data.DbType" /> を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.DbType" /> 値のいずれか。既定値は、<see cref="F:System.Data.DbType.String" /> です。</returns>
      <exception cref="T:System.ArgumentException">プロパティが有効な <see cref="T:System.Data.DbType" /> に設定されていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Direction">
      <summary>パラメーターが入力専用、出力専用、双方向、またはストアド プロシージャの戻り値パラメーターかどうかを示す値を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.ParameterDirection" /> 値のいずれか。既定値は、Input です。</returns>
      <exception cref="T:System.ArgumentException">プロパティが、いずれかの有効な <see cref="T:System.Data.ParameterDirection" /> 値に設定されていません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.IsNullable">
      <summary>パラメーターが null 値を受け付けるかどうかを示す値を取得または設定します。</summary>
      <returns>null 値を使用できる場合は true。それ以外の場合は false。既定値は、false です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.ParameterName">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> の名前を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> の名前です。既定値は、空の文字列 ("") です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Precision">
      <summary>[.NET Framework 4.5.1 以降のバージョンでサポートされる]<see cref="P:System.Data.Common.DbParameter.Value" /> プロパティを表すときに使用する最大桁数を取得または設定します。</summary>
      <returns>
        <see cref="P:System.Data.Common.DbParameter.Value" /> プロパティを表すための最大桁数。</returns>
    </member>
    <member name="M:System.Data.Common.DbParameter.ResetDbType">
      <summary>DbType プロパティを元の設定にリセットします。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Scale">
      <summary>[.NET Framework 4.5.1 以降のバージョンでサポートされる]<see cref="P:System.Data.Common.DbParameter.Value" /> を解決するための小数部桁数を取得または設定します。</summary>
      <returns>
        <see cref="P:System.Data.Common.DbParameter.Value" /> を解決するための小数部の桁数。</returns>
    </member>
    <member name="P:System.Data.Common.DbParameter.Size">
      <summary>列内のデータの最大サイズをバイト単位で取得または設定します。</summary>
      <returns>列内のデータのバイト単位による最大サイズ。既定値は、パラメーター値から推論されます。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumn">
      <summary>
        <see cref="T:System.Data.DataSet" /> に割り当てられ、<see cref="P:System.Data.Common.DbParameter.Value" /> の読み込みまたは戻しに使用されるソース列の名前を取得または設定します。</summary>
      <returns>
        <see cref="T:System.Data.DataSet" /> に割り当てられたソース列の名前。既定値は空の文字列です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumnNullMapping">
      <summary>ソース列が null 許容であるかどうかを示す値を設定または取得します。<see cref="T:System.Data.Common.DbCommandBuilder" /> は、これを使用することにより、null 許容列に対する Update ステートメントを正しく生成できます。</summary>
      <returns>ソース列が null 許容の場合は true。それ以外の場合は false。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Value">
      <summary>パラメーターの値を取得または設定します。</summary>
      <returns>パラメーターの値を示す <see cref="T:System.Object" />。既定値は null です。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbParameterCollection">
      <summary>
        <see cref="T:System.Data.Common.DbCommand" /> に関連するパラメーターのコレクションの基本クラス。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbParameterCollection" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Add(System.Object)">
      <summary>指定した <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを <see cref="T:System.Data.Common.DbParameterCollection" /> に追加します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの、コレクション内でのインデックス。</returns>
      <param name="value">コレクションに追加する <see cref="T:System.Data.Common.DbParameter" /> の <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.AddRange(System.Array)">
      <summary>指定した値の項目の配列を <see cref="T:System.Data.Common.DbParameterCollection" /> に追加します。</summary>
      <param name="values">コレクションに追加する <see cref="T:System.Data.Common.DbParameter" /> 型の値の配列。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Clear">
      <summary>
        <see cref="T:System.Data.Common.DbParameterCollection" /> からすべての <see cref="T:System.Data.Common.DbParameter" /> 値を削除します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.Object)">
      <summary>指定した <see cref="P:System.Data.Common.DbParameter.Value" /> の <see cref="T:System.Data.Common.DbParameter" /> がコレクションに格納されているかどうかを示します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> がコレクション内に存在する場合は true。それ以外の場合は false。</returns>
      <param name="value">コレクション内で検索される <see cref="T:System.Data.Common.DbParameter" /> の <see cref="P:System.Data.Common.DbParameter.Value" />。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.String)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> がコレクション内に存在するかどうかを示します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> がコレクション内に存在する場合は true。それ以外の場合は false。</returns>
      <param name="value">コレクション内で検索される <see cref="T:System.Data.Common.DbParameter" /> の名前。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>指定したインデックスを開始位置として、コレクションに項目の配列をコピーします。</summary>
      <param name="array">コレクションにコピーする項目の配列。</param>
      <param name="index">項目のコピー先コレクション内のインデックス。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Count">
      <summary>コレクション内の項目の数を示します。</summary>
      <returns>コレクション内の項目数。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetEnumerator">
      <summary>.NET Framework データ プロバイダーがコレクションに対して単純な反復処理を実行するのをサポートする、<see cref="M:System.Collections.IEnumerable.GetEnumerator" /> メソッドを公開します。</summary>
      <returns>コレクションを反復処理するために使用できる <see cref="T:System.Collections.IEnumerator" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.Int32)">
      <summary>コレクション内の指定したインデックス位置の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを返します。</summary>
      <returns>コレクション内の指定したインデックス位置の <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</returns>
      <param name="index">コレクション内の <see cref="T:System.Data.Common.DbParameter" /> のインデックス。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.String)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを返します。</summary>
      <returns>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</returns>
      <param name="parameterName">コレクション内の <see cref="T:System.Data.Common.DbParameter" /> の名前。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.Object)">
      <summary>指定した <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのインデックスを返します。</summary>
      <returns>指定した <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのインデックス。</returns>
      <param name="value">コレクション内の <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.String)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのインデックスを返します。</summary>
      <returns>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトのインデックス。</returns>
      <param name="parameterName">コレクション内の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの名前。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Insert(System.Int32,System.Object)">
      <summary>このコレクション内の指定したインデックス位置に、指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの指定したインデックスを挿入します。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを挿入するインデックス位置。</param>
      <param name="value">コレクションに挿入する <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Data.Common.DbParameter" /> を取得および設定します。</summary>
      <returns>指定されたインデックスにある <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="index">パラメーターの、0 から始まるインデックス。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定したインデックスが存在しません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.String)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> を取得および設定します。</summary>
      <returns>指定した名前を持つ <see cref="T:System.Data.Common.DbParameter" />。</returns>
      <param name="parameterName">パラメーターの名前。</param>
      <exception cref="T:System.IndexOutOfRangeException">指定したインデックスが存在しません。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Remove(System.Object)">
      <summary>指定された <see cref="T:System.Data.Common.DbParameter" /> オブジェクトをコレクションから削除します。</summary>
      <param name="value">削除する <see cref="T:System.Data.Common.DbParameter" /> オブジェクト。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.Int32)">
      <summary>指定したインデックス位置にある <see cref="T:System.Data.Common.DbParameter" /> オブジェクトをコレクションから削除します。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトが存在する位置を示すインデックス。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.String)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトをコレクションから削除します。</summary>
      <param name="parameterName">削除する <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの名前。</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
      <summary>指定したインデックス位置にある <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを新しい値に設定します。</summary>
      <param name="index">
        <see cref="T:System.Data.Common.DbParameter" /> オブジェクトが存在する位置を示すインデックス。</param>
      <param name="value">新しい <see cref="T:System.Data.Common.DbParameter" /> 値。</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
      <summary>指定した名前の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトを新しい値に設定します。</summary>
      <param name="parameterName">コレクション内の <see cref="T:System.Data.Common.DbParameter" /> オブジェクトの名前。</param>
      <param name="value">新しい <see cref="T:System.Data.Common.DbParameter" /> 値。</param>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.SyncRoot">
      <summary>コレクションへのアクセスを同期するために使用される <see cref="T:System.Object" /> を示します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameterCollection" /> へのアクセスを同期するために使用される <see cref="T:System.Object" />。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#Item(System.Int32)">
      <summary>指定したインデックスにある要素を取得または設定します。</summary>
      <returns>指定したインデックスにある要素。</returns>
      <param name="index">取得または設定する要素の、0 から始まるインデックス番号。</param>
    </member>
    <member name="T:System.Data.Common.DbProviderFactory">
      <summary>プロバイダーのデータ ソース クラスの実装のインスタンスを作成するためのメソッドのセットを表します。</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.#ctor">
      <summary>
        <see cref="T:System.Data.Common.DbProviderFactory" /> クラスの新しいインスタンスを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateCommand">
      <summary>
        <see cref="T:System.Data.Common.DbCommand" /> クラスを実装しているプロバイダーのクラスの新しいインスタンスを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbCommand" /> の新しいインスタンス。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnection">
      <summary>
        <see cref="T:System.Data.Common.DbConnection" /> クラスを実装しているプロバイダーのクラスの新しいインスタンスを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnection" /> の新しいインスタンス。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnectionStringBuilder">
      <summary>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> クラスを実装しているプロバイダーのクラスの新しいインスタンスを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> の新しいインスタンス。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateParameter">
      <summary>
        <see cref="T:System.Data.Common.DbParameter" /> クラスを実装しているプロバイダーのクラスの新しいインスタンスを返します。</summary>
      <returns>
        <see cref="T:System.Data.Common.DbParameter" /> の新しいインスタンス。</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbTransaction">
      <summary>トランザクションの基本クラス。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.#ctor">
      <summary>新しい <see cref="T:System.Data.Common.DbTransaction" /> オブジェクトを初期化します。</summary>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Commit">
      <summary>データベース トランザクションをコミットします。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.Connection">
      <summary>トランザクションに関連付けられている <see cref="T:System.Data.Common.DbConnection" /> オブジェクトを指定します。</summary>
      <returns>トランザクションに関連付けられる <see cref="T:System.Data.Common.DbConnection" /> オブジェクト。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.DbConnection">
      <summary>トランザクションに関連付けられている <see cref="T:System.Data.Common.DbConnection" /> オブジェクトを指定します。</summary>
      <returns>トランザクションに関連付けられる <see cref="T:System.Data.Common.DbConnection" /> オブジェクト。</returns>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose">
      <summary>
        <see cref="T:System.Data.Common.DbTransaction" /> によって使用されているアンマネージ リソースを解放します。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.Data.Common.DbTransaction" /> によって使用されているアンマネージ リソースを解放し、オプションでマネージ リソースも解放します。</summary>
      <param name="disposing">true の場合、このメソッドは、この <see cref="T:System.Data.Common.DbTransaction" /> から参照されるすべてのマネージ オブジェクトが保持しているすべてのリソースを解放します。</param>
    </member>
    <member name="P:System.Data.Common.DbTransaction.IsolationLevel">
      <summary>このトランザクションの <see cref="T:System.Data.IsolationLevel" /> を指定します。</summary>
      <returns>トランザクションの <see cref="T:System.Data.IsolationLevel" />。</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Rollback">
      <summary>保留中の状態からトランザクションをロールバックします。</summary>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>