﻿
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title>液化气订单查询</title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="start_time" id="start_time" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="end_time" id="end_time" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/yhq_lpg_jydd?start_time=" + $('#start_time').val();
            $.getJSON("/api/values/yhq_lpg_jydd?start_time=" + $('#start_time').val() + "&end_time=" + $('#end_time').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>ID</td><td>片区</td><td>站点</td><td>单号</td><td>生成时间</td><td>客户类型</td><td>客户ID</td><td>应收金额</td><td>支付方式</td><td>支付订单号</td><td>付款交易号</td><td>记账标志</td><td>客户名字</td><td>客户手机号</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.id_ + '</td><td>' + item.AREA_ + '</td><td>' + item.dept_
                            + '</td><td>' + item.no_ + '</td><td>' + item.time_ + '</td><td>' + item.cust_type_ + '</td><td>'
                            + item.cust_id_ + '</td><td>' + item.AMOUNT_ + '</td><td>' + item.PAY_TYPE_
                            + '</td><td>'
                            + item.PAY_NO_
                            + '</td><td>' + item.TRADE_NO_ + '</td><td>' + item.FLAG_ + '</td><td>' + item.BUY_NAME_ + '</td><td>'
                            + item.PHONE_   + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/fyhq_lpg_jydd_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>



