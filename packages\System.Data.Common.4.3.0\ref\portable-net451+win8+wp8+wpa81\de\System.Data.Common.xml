﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Data.Common</name>
  </assembly>
  <members>
    <member name="T:System.DBNull">
      <summary>Stellt einen nicht vorhandenen Wert dar.Diese Klasse kann nicht vererbt werden.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString">
      <summary>Gibt eine leere Zeichenfolge zurück (<see cref="F:System.String.Empty" />).</summary>
      <returns>Eine leere Zeichenfolge (<see cref="F:System.String.Empty" />).</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.DBNull.ToString(System.IFormatProvider)">
      <summary>Gibt eine leere Zeichenfolge unter Verwendung des angegebenen <see cref="T:System.IFormatProvider" /> zurück.</summary>
      <returns>Eine leere Zeichenfolge (<see cref="F:System.String.Empty" />).</returns>
      <param name="provider">Der <see cref="T:System.IFormatProvider" />, der zum Formatieren des Rückgabewerts verwendet werden soll.– oder – null, wenn die Formatierungsinformationen dem aktuellen Gebietsschema des Betriebssystems entnommen werden sollen. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.DBNull.Value">
      <summary>Stellt die einzige Instanz der <see cref="T:System.DBNull" />-Klasse dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.CommandBehavior">
      <summary>Stellt eine Beschreibung der Ergebnisse der Abfrage sowie ihrer Auswirkungen auf die Datenbank bereit.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandBehavior.CloseConnection">
      <summary>Beim Ausführen des Befehls wird das zugeordnete Connection-Objekt geschlossen, wenn das zugeordnete DataReader-Objekt geschlossen wird.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.Default">
      <summary>Die Abfrage kann mehrere Resultsets zurückgeben.Die Ausführung der Abfrage kann sich auf den Zustand der Datenbank auswirken.Default legt keine <see cref="T:System.Data.CommandBehavior" />-Flags fest, daher entspricht die Funktion eines Aufrufs von ExecuteReader(CommandBehavior.Default) dem von ExecuteReader().</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.KeyInfo">
      <summary>Die Abfrage gibt Informationen über Spalten und Primärschlüssel zurück. </summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SchemaOnly">
      <summary>Die Abfrage gibt nur Spalteninformationen zurück.Bei Verwendung von <see cref="F:System.Data.CommandBehavior.SchemaOnly" /> stellt der .NET Framework-Datenanbieter für SQL Server der auszuführenden Anweisung SET FMTONLY ON voran.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SequentialAccess">
      <summary>Ermöglicht dem DataReader das Behandeln von Zeilen, die Spalten mit großen Binärwerten enthalten.Statt des Ladens der gesamten Zeile ermöglicht SequentialAccess dem DataReader das Laden der Daten als Stream.Anschließend können Sie mit der GetBytes-Methode oder der GetChars-Methode eine Byteposition für den Beginn des Lesevorgangs sowie eine eingeschränkte Puffergröße für die zurückgegebenen Daten angeben.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleResult">
      <summary>Die Abfrage gibt ein einziges Resultset zurück.</summary>
    </member>
    <member name="F:System.Data.CommandBehavior.SingleRow">
      <summary>Von der Abfrage wird erwartet, dass eine einzige Zeile des ersten Resultsets zurückgegeben wird.Die Ausführung der Abfrage kann sich auf den Zustand der Datenbank auswirken.Einige .NET Framework-Datenprovider können mit diesen Informationen die Leistung des Befehls optimieren (dies ist jedoch nicht unbedingt erforderlich).Wenn Sie <see cref="F:System.Data.CommandBehavior.SingleRow" /> mit der <see cref="M:System.Data.OleDb.OleDbCommand.ExecuteReader" />-Methode des <see cref="T:System.Data.OleDb.OleDbCommand" />-Objekts angeben, führt der .NET Framework-Datenanbieter für OLE DB eine Bindung mithilfe der OLE DB IRow-Schnittstelle durch, sofern diese verfügbar ist.Andernfalls wird die IRowset-Schnittstelle verwendet.Wenn die SQL-Anweisung nur eine einzige Zeile zurückgeben soll, kann durch Angeben von <see cref="F:System.Data.CommandBehavior.SingleRow" /> auch die Leistung der Anwendung verbessert werden.SingleRow kann auch beim Ausführen von Abfragen angegeben werden, die mehrere Resultsets zurückgeben können.  In einem solchen Fall, in dem sowohl eine SQL-Abfrage für mehrere Resultsets als auch eine einzige Zeile angegeben werden, enthält das zurückgegebene Ergebnis nur die erste Zeile des ersten Resultsets.Die übrigen Resultsets der Abfrage werden nicht zurückgegeben.</summary>
    </member>
    <member name="T:System.Data.CommandType">
      <summary>Gibt an, wie eine Befehlszeichenfolge interpretiert wird.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.CommandType.StoredProcedure">
      <summary>Der Name einer gespeicherten Prozedur.</summary>
    </member>
    <member name="F:System.Data.CommandType.TableDirect">
      <summary>Name der Tabelle</summary>
    </member>
    <member name="F:System.Data.CommandType.Text">
      <summary>Ein SQL-Textbefehl. (Standardeinstellung.) </summary>
    </member>
    <member name="T:System.Data.ConnectionState">
      <summary>Beschreibt den aktuellen Zustand der Verbindung mit einer Datenquelle.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ConnectionState.Broken">
      <summary>Die Verbindung mit der Datenquelle ist unterbrochen.Dies kann nur nach dem Öffnen der Verbindung auftreten.Eine Verbindung in diesem Zustand kann geschlossen und anschließend erneut geöffnet werden. (Dieser Wert ist für zukünftige Versionen des Produkts reserviert.)</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Closed">
      <summary>Die Verbindung ist geschlossen.</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Connecting">
      <summary>Das Verbindungsobjekt stellt eine Verbindung mit der Datenquelle her.</summary>
    </member>
    <member name="F:System.Data.ConnectionState.Executing">
      <summary>Das Verbindungsobjekt führt einen Befehl aus. (Dieser Wert ist für zukünftige Versionen des Produkts reserviert.) </summary>
    </member>
    <member name="F:System.Data.ConnectionState.Fetching">
      <summary>Das Verbindungsobjekt ruft Daten ab. (Dieser Wert ist für zukünftige Versionen des Produkts reserviert.) </summary>
    </member>
    <member name="F:System.Data.ConnectionState.Open">
      <summary>Die Verbindung ist geöffnet.</summary>
    </member>
    <member name="T:System.Data.DbType">
      <summary>Gibt den Datentyp eines Felds, einer Eigenschaft oder eines Parameter-Objekts eines .NET Framework-Datenproviders an.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.DbType.AnsiString">
      <summary>Ein Stream variabler Länge von Nicht-Unicode-Zeichen in einem Bereich zwischen 1 und 8000 Zeichen.</summary>
    </member>
    <member name="F:System.Data.DbType.AnsiStringFixedLength">
      <summary>Ein Stream fester Länge mit Nicht-Unicode-Zeichen.</summary>
    </member>
    <member name="F:System.Data.DbType.Binary">
      <summary>Ein Stream variabler Länge mit Binärdaten in einem Bereich zwischen 1 und 8000 Bytes.</summary>
    </member>
    <member name="F:System.Data.DbType.Boolean">
      <summary>Ein einfacher Typ zur Darstellung der booleschen Werte true und false.</summary>
    </member>
    <member name="F:System.Data.DbType.Byte">
      <summary>Eine 8-Bit-Ganzzahl ohne Vorzeichen, deren Wert im Bereich von 0 bis 255 liegt.</summary>
    </member>
    <member name="F:System.Data.DbType.Currency">
      <summary>Ein Währungswert im Bereich von -263 (bzw. -922.337.203.685.477,5808) bis 263-1 (bzw. +922.337.203.685.477,5807) mit einer Genauigkeit von einem Zehntausendstel einer Währungseinheit.</summary>
    </member>
    <member name="F:System.Data.DbType.Date">
      <summary>Ein Typ, der einen Datumswert darstellt.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime">
      <summary>Ein Typ zur Darstellung des Werts für Datum und Uhrzeit.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTime2">
      <summary>Datums- und Uhrzeitdaten.Der Bereich für Datumswerte liegt zwischen dem 1. Januar 1 n. Chr. und dem 31. Dezember 9999 n. Chr.Der Bereich für den Zeitwert liegt zwischen 00:00:00 und 23:59:59.9999999 mit einer Genauigkeit von 100 Nanosekunden.</summary>
    </member>
    <member name="F:System.Data.DbType.DateTimeOffset">
      <summary>Datums- und Zeitdaten mit Unterstützung von Zeitzonen.Der Bereich für Datumswerte liegt zwischen dem 1. Januar 1 n. Chr. und dem 31. Dezember 9999 n. Chr.Der Bereich für den Zeitwert liegt zwischen 00:00:00 und 23:59:59.9999999 mit einer Genauigkeit von 100 Nanosekunden.Der Wertbereich für Zeitzonen liegt zwischen -14: 00 und +14: 00.</summary>
    </member>
    <member name="F:System.Data.DbType.Decimal">
      <summary>Ein einfacher Typ zur Darstellung der Werte im Bereich von 1,0 x 10-28 bis ungefähr 7,9 x 1028 mit 28-29 signifikanten Ziffern.</summary>
    </member>
    <member name="F:System.Data.DbType.Double">
      <summary>Ein Gleitkommatyp zur Darstellung von Werten im Bereich von ungefähr 5,0 x 10-324 bis 1,7 x 10308 mit einer Genauigkeit von 15-16 Stellen.</summary>
    </member>
    <member name="F:System.Data.DbType.Guid">
      <summary>Eine GUID (Globally Unique Identifier, globaler eindeutiger Bezeichner).</summary>
    </member>
    <member name="F:System.Data.DbType.Int16">
      <summary>Ein ganzzahliger Typ zur Darstellung von 16-Bit-Ganzzahlen mit Vorzeichen und mit Werten zwischen -32768 und 32767.</summary>
    </member>
    <member name="F:System.Data.DbType.Int32">
      <summary>Ein ganzzahliger Typ zur Darstellung von 32-Bit-Ganzzahlen mit Vorzeichen und mit Werten zwischen -2.147.483.648 und 2.147.483.647.</summary>
    </member>
    <member name="F:System.Data.DbType.Int64">
      <summary>Ein ganzzahliger Typ zur Darstellung von 64-Bit-Ganzzahlen mit Vorzeichen und mit Werten zwischen -9.223.372.036.854.775.808 und 9.223.372.036.854.775.807.</summary>
    </member>
    <member name="F:System.Data.DbType.Object">
      <summary>Ein allgemeiner Typ, der jeden Verweis bzw. Werttyp darstellt, der nicht explizit durch einen anderen DbType-Wert dargestellt wird.</summary>
    </member>
    <member name="F:System.Data.DbType.SByte">
      <summary>Ein ganzzahliger Typ zur Darstellung von 8-Bit-Ganzzahlen mit Vorzeichen und mit Werten zwischen -128 und 127.</summary>
    </member>
    <member name="F:System.Data.DbType.Single">
      <summary>Ein Gleitkommatyp zur Darstellung von Werten im Bereich von ungefähr 1,5 x 10-45 bis 3,4 x 1038 mit einer Genauigkeit von 7 Stellen.</summary>
    </member>
    <member name="F:System.Data.DbType.String">
      <summary>Ein Typ, der Zeichenfolgen mit Unicode-Zeichen darstellt.</summary>
    </member>
    <member name="F:System.Data.DbType.StringFixedLength">
      <summary>Eine Zeichenfolge mit fester Länge mit Unicode-Zeichen.</summary>
    </member>
    <member name="F:System.Data.DbType.Time">
      <summary>Ein Typ, der einen SQL Server-DateTime-Wert darstellt.Wenn Sie einen SQL Server-time-Wert verwenden möchten, verwenden Sie <see cref="F:System.Data.SqlDbType.Time" />.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt16">
      <summary>Ein ganzzahliger Typ zur Darstellung vorzeichenloser 16-Bit-Ganzzahlen mit Werten zwischen 0 und 65535.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt32">
      <summary>Ein ganzzahliger Typ zur Darstellung vorzeichenloser 32-Bit-Ganzzahlen mit Werten zwischen 0 und 4.294.967.295.</summary>
    </member>
    <member name="F:System.Data.DbType.UInt64">
      <summary>Ein ganzzahliger Typ zur Darstellung vorzeichenloser 64-Bit-Ganzzahlen mit Werten zwischen 0 und 18.446.744.073.709.551.615.</summary>
    </member>
    <member name="F:System.Data.DbType.VarNumeric">
      <summary>Ein numerischer Wert mit variabler Länge.</summary>
    </member>
    <member name="F:System.Data.DbType.Xml">
      <summary>Eine analysierte Darstellung eines XML-Dokuments oder eines XML-Fragments.</summary>
    </member>
    <member name="T:System.Data.IsolationLevel">
      <summary>Gibt das Sperrverhalten für Transaktionen für die Verbindung an.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.IsolationLevel.Chaos">
      <summary>Die ausstehenden Änderungen von höher isolierten Transaktionen können nicht überschrieben werden.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadCommitted">
      <summary>Beim Lesen der Daten werden gemeinsame Sperren verwendet, um das Lesen geänderter Daten zu verhindern. Die Daten können jedoch vor dem Ende der Transaktion geändert werden, was zu nicht wiederholbaren Lesevorgängen oder Phantomdaten führen kann.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.ReadUncommitted">
      <summary>Ein Lesen geänderter Daten ist möglich. Das heißt, dass keine gemeinsamen Sperren ausgegeben und keine exklusiven Sperren berücksichtigt werden.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.RepeatableRead">
      <summary>Die Sperren gelten für alle in einer Abfrage verwendeten Daten, damit die Daten nicht durch andere Benutzer aktualisiert werden können.Nicht wiederholbare Lesevorgänge werden dadurch verhindert, es sind jedoch weiterhin Phantomzeilen möglich.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Serializable">
      <summary>Eine Bereichssperre wird für die <see cref="T:System.Data.DataSet" />-Klasse festgelegt. Dadurch wird verhindert, dass andere Benutzer vor dem Abschluss der Transaktion Zeilen in das Dataset einfügen oder darin aktualisieren.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Snapshot">
      <summary>Reduziert das Blockieren durch Speichern einer Version von Daten, die von einer Anwendung gelesen werden können, während sie von einer anderen Anwendung geändert werden.Gibt an, dass Sie von einer Transaktion aus keine Änderungen sehen können, die in anderen Transaktionen vorgenommen wurden, auch wenn Sie diese erneut abfragen.</summary>
    </member>
    <member name="F:System.Data.IsolationLevel.Unspecified">
      <summary>Es wird nicht der angegebene Isolationsgrad verwendet, der Grad kann jedoch nicht bestimmt werden.</summary>
    </member>
    <member name="T:System.Data.ParameterDirection">
      <summary>Gibt den Typ eines Parameters in einer Abfrage relativ zum <see cref="T:System.Data.DataSet" /> an.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.ParameterDirection.Input">
      <summary>Der Parameter ist ein Eingabeparameter.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.InputOutput">
      <summary>Der Parameter unterstützt sowohl Eingabe als auch Ausgabe.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.Output">
      <summary>Der Parameter ist ein Ausgabeparameter.</summary>
    </member>
    <member name="F:System.Data.ParameterDirection.ReturnValue">
      <summary>Der Parameter stellt einen Rückgabewert aus einer Operation dar, z. B. aus einer gespeicherten Prozedur, einer integrierten Funktion oder einer benutzerdefinierten Funktion.</summary>
    </member>
    <member name="T:System.Data.StateChangeEventArgs">
      <summary>Stellt Daten für das Zustandsänderungsereignis eines .NET Framework-Datenproviders bereit.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.StateChangeEventArgs.#ctor(System.Data.ConnectionState,System.Data.ConnectionState)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.StateChangeEventArgs" />-Klasse, wenn der ursprüngliche Zustand und der aktuelle Zustand des Objekts angegeben sind.</summary>
      <param name="originalState">Einer der <see cref="T:System.Data.ConnectionState" />-Werte. </param>
      <param name="currentState">Einer der <see cref="T:System.Data.ConnectionState" />-Werte. </param>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.CurrentState">
      <summary>Ruft den neuen Zustand der Verbindung ab.Das Verbindungsobjekt weist beim Auslösen des Ereignisses bereits den neuen Zustand auf.</summary>
      <returns>Einer der <see cref="T:System.Data.ConnectionState" />-Werte.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.StateChangeEventArgs.OriginalState">
      <summary>Ruft den ursprünglichen Zustand der Verbindung ab.</summary>
      <returns>Einer der <see cref="T:System.Data.ConnectionState" />-Werte.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.StateChangeEventHandler">
      <summary>Stellt die Methode für die Behandlung des <see cref="E:System.Data.Common.DbConnection.StateChange" />-Ereignisses dar.</summary>
      <param name="sender">Die Quelle des Ereignisses. </param>
      <param name="e">Die <see cref="T:System.Data.StateChangeEventArgs" />-Klasse, die die Ereignisdaten enthält. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.UpdateRowSource">
      <summary>Gibt an, wie Ergebnisse von Abfragebefehlen auf die aktualisierte Zeile angewendet werden.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="F:System.Data.UpdateRowSource.Both">
      <summary>Sowohl die Ausgabeparameter als auch die erste zurückgegebene Zeile werden der geänderten Zeile im <see cref="T:System.Data.DataSet" /> zugeordnet.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.FirstReturnedRecord">
      <summary>Die Daten in der ersten zurückgegebenen Zeile werden der geänderten Zeile im <see cref="T:System.Data.DataSet" /> zugeordnet.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.None">
      <summary>Alle zurückgegebenen Parameter oder Zeilen werden ignoriert.</summary>
    </member>
    <member name="F:System.Data.UpdateRowSource.OutputParameters">
      <summary>Ausgabeparameter werden der geänderten Zeile im <see cref="T:System.Data.DataSet" /> zugeordnet.</summary>
    </member>
    <member name="T:System.Data.Common.DbCommand">
      <summary>Stellt eine SQL-Anweisung oder eine gespeicherte Prozedur dar, die für eine Datenquelle ausgeführt werden soll.Stellt eine Basisklasse für datenbankspezifische Klassen bereit, die Befehle darstellen.<see cref="Overload:System.Data.Common.DbCommand.ExecuteNonQueryAsync" /></summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.#ctor">
      <summary>Erstellt eine Instanz des <see cref="T:System.Data.Common.DbCommand" />-Objekts.</summary>
    </member>
    <member name="M:System.Data.Common.DbCommand.Cancel">
      <summary>Versucht die Ausführung eines <see cref="T:System.Data.Common.DbCommand" /> abzubrechen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandText">
      <summary>Ruft den Textbefehl ab, der für die Datenquelle ausgeführt werden soll, oder legt diesen fest.</summary>
      <returns>Der auszuführende Textbefehl.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandTimeout">
      <summary>Ruft die Zeit ab, die gewartet werden soll, bis der Versuch einer Befehlsausführung beendet und ein Fehler generiert wird, oder legt diese fest.</summary>
      <returns>Die Zeitdauer in Sekunden, die auf das Ausführen des Befehls gewartet werden soll.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.CommandType">
      <summary>Gibt an, wie die <see cref="P:System.Data.Common.DbCommand.CommandText" />-Eigenschaft interpretiert wird.</summary>
      <returns>Einer der <see cref="T:System.Data.CommandType" />-Werte.Der Standardwert ist Text.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Connection">
      <summary>Ruft die von diesem <see cref="T:System.Data.Common.DbCommand" /> verwendete <see cref="T:System.Data.Common.DbConnection" /> ab oder legt diese fest.</summary>
      <returns>Die Verbindung mit der Datenquelle.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateDbParameter">
      <summary>Erstellt eine neue Instanz eines <see cref="T:System.Data.Common.DbParameter" />-Objekts.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbParameter" />-Objekt.</returns>
    </member>
    <member name="M:System.Data.Common.DbCommand.CreateParameter">
      <summary>Erstellt eine neue Instanz eines <see cref="T:System.Data.Common.DbParameter" />-Objekts.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbParameter" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbConnection">
      <summary>Ruft die von diesem <see cref="T:System.Data.Common.DbCommand" /> verwendete <see cref="T:System.Data.Common.DbConnection" /> ab oder legt diese fest.</summary>
      <returns>Die Verbindung mit der Datenquelle.</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbParameterCollection">
      <summary>Ruft die Auflistung von <see cref="T:System.Data.Common.DbParameter" />-Objekten ab.</summary>
      <returns>Die Parameter der SQL-Anweisung oder der gespeicherten Prozedur.</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DbTransaction">
      <summary>Ruft die <see cref="P:System.Data.Common.DbCommand.DbTransaction" /> ab, in der dieses <see cref="T:System.Data.Common.DbCommand" />-Objekt ausgeführt wird, oder legt diese fest.</summary>
      <returns>Die Transaktion, in der ein Command-Objekt eines .NET Framework-Datenanbieters ausgeführt wird.Der Standardwert ist ein NULL-Verweis (Nothing in Visual Basic).</returns>
    </member>
    <member name="P:System.Data.Common.DbCommand.DesignTimeVisible">
      <summary>Ruft einen Wert ab, der angibt, ob das Befehlsobjekt in einem benutzerdefinierten Schnittstellensteuerelement sichtbar sein soll, oder legt diesen fest.</summary>
      <returns>true, wenn das Befehlsobjekt in einem Steuerelement sichtbar sein soll, andernfalls false.Die Standardeinstellung ist true.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Dispose"></member>
    <member name="M:System.Data.Common.DbCommand.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReader(System.Data.CommandBehavior)">
      <summary>Führt den Befehlstext für die Verbindung aus.</summary>
      <returns>Eine Aufgabe, die den Vorgang darstellt.</returns>
      <param name="behavior">Eine Instanz von <see cref="T:System.Data.CommandBehavior" />.</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>Anbieter sollten diese Methode implementieren, um eine nicht standardmäßige Implementierung für <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />-Überladungen bereitzustellen.Die Standardimplementierung ruft die synchrone <see cref="M:System.Data.Common.DbCommand.ExecuteReader" />-Methode auf und gibt eine abgeschlossene Aufgabe zurück. Der aufrufende Thread wird blockiert.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen Abbruchtoken erhalten hat.Die Ausnahmen, die von ExecuteReader ausgelöst werden, werden über die zurückgegebene Task Exception-Eigenschaft übermittelt.Diese Methode nimmt ein Abbruchtoken an, das verwendet werden kann, um ein frühes Abbrechen des Vorgangs anzufordern.Implementierungen können diese Anforderung ignorieren.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="behavior">Optionen zur Ausführung der Anweisung und zum Datenabruf.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQuery">
      <summary>Führt eine SQL-Anweisung für ein ein Verbindungsobjekt aus.</summary>
      <returns>Die Anzahl der betroffenen Zeilen.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />, die eine SQL-Anweisung für ein Verbindungsobjekt ausführt.Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteNonQueryAsync(System.Threading.CancellationToken)">
      <summary>Dies ist die asynchrone Version von <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />.Anbieter sollten mit einer entsprechenden Implementierung überschreiben.Das Abbruchtoken kann optional ignoriert werden.Die Standardimplementierung ruft die synchrone <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" />-Methode auf und gibt eine abgeschlossene Aufgabe zurück. Der aufrufende Thread wird blockiert.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen Abbruchtoken erhalten hat.  Die Ausnahmen, die von <see cref="M:System.Data.Common.DbCommand.ExecuteNonQuery" /> ausgelöst werden, werden auf die zurückgegebene Task Exception-Eigenschaft übermittelt.Rufen Sie keine anderen Methoden und Eigenschaften des DbCommand-Objekts auf, bis die zurückgegebene Aufgabe abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader">
      <summary>Führt <see cref="P:System.Data.Common.DbCommand.CommandText" /> für die <see cref="P:System.Data.Common.DbCommand.Connection" /> aus und gibt einen <see cref="T:System.Data.Common.DbDataReader" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReader(System.Data.CommandBehavior)">
      <summary>Führt <see cref="P:System.Data.Common.DbCommand.CommandText" /> für die <see cref="P:System.Data.Common.DbCommand.Connection" /> aus und gibt einen <see cref="T:System.Data.Common.DbDataReader" /> mit einem der <see cref="T:System.Data.CommandBehavior" />-Werte zurück. </summary>
      <returns>Ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt.</returns>
      <param name="behavior">Einer der <see cref="T:System.Data.CommandBehavior" />-Werte.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync">
      <summary>Eine asynchrone Version von <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, die <see cref="P:System.Data.Common.DbCommand.CommandText" /> gegen <see cref="P:System.Data.Common.DbCommand.Connection" /> ausführt und <see cref="T:System.Data.Common.DbDataReader" /> zurückgibt.Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior)">
      <summary>Eine asynchrone Version von <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, die <see cref="P:System.Data.Common.DbCommand.CommandText" /> gegen <see cref="P:System.Data.Common.DbCommand.Connection" /> ausführt und <see cref="T:System.Data.Common.DbDataReader" /> zurückgibt.Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="behavior">Einer der <see cref="T:System.Data.CommandBehavior" />-Werte.</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)">
      <summary>Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="behavior">Einer der <see cref="T:System.Data.CommandBehavior" />-Werte.</param>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteReaderAsync(System.Threading.CancellationToken)">
      <summary>Eine asynchrone Version von <see cref="Overload:System.Data.Common.DbCommand.ExecuteReader" />, die <see cref="P:System.Data.Common.DbCommand.CommandText" /> gegen <see cref="P:System.Data.Common.DbCommand.Connection" /> ausführt und <see cref="T:System.Data.Common.DbDataReader" /> zurückgibt.Diese Methode gibt eine Benachrichtigung darüber weiter, dass Vorgänge abgebrochen werden sollen.Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteDbDataReaderAsync(System.Data.CommandBehavior,System.Threading.CancellationToken)" /> auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
      <exception cref="T:System.ArgumentException">Ein ungültiger <see cref="T:System.Data.CommandBehavior" />-Wert.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalar">
      <summary>Führt die Abfrage aus und gibt die erste Spalte der ersten Zeile im Resultset zurück, das durch die Abfrage zurückgegeben wird.Alle anderen Spalten und Zeilen werden ignoriert.</summary>
      <returns>Die erste Spalte der ersten Zeile im Resultset.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" />, die die Abfrage ausführt und die erste Spalte der ersten Zeile im Resultset zurückgibt.Alle anderen Spalten und Zeilen werden ignoriert.Ruft <see cref="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbCommand.ExecuteScalarAsync(System.Threading.CancellationToken)">
      <summary>Dies ist die asynchrone Version von <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" />.Anbieter sollten mit einer entsprechenden Implementierung überschreiben.Das Abbruchtoken kann optional ignoriert werden.Die Standardimplementierung ruft die synchrone <see cref="M:System.Data.Common.DbCommand.ExecuteScalar" />-Methode auf und gibt eine abgeschlossene Aufgabe zurück. Der aufrufende Thread wird blockiert.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen Abbruchtoken erhalten hat.Die Ausnahmen, die von ExecuteReader ausgelöst werden, werden über die zurückgegebene Task Exception-Eigenschaft übermittelt.Rufen Sie keine anderen Methoden und Eigenschaften des DbCommand-Objekts auf, bis die zurückgegebene Aufgabe abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Das Token zum überwachen von Abbruchanforderungen .</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="P:System.Data.Common.DbCommand.Parameters">
      <summary>Ruft die Auflistung von <see cref="T:System.Data.Common.DbParameter" />-Objekten ab.Weitere Informationen zu Parametern finden Sie unter Konfigurieren von Parametern und Parameterdatentypen.</summary>
      <returns>Die Parameter der SQL-Anweisung oder der gespeicherten Prozedur.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbCommand.Prepare">
      <summary>Erstellt eine vorbereitete (oder kompilierte) Version des Befehls für die Datenquelle.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.Transaction">
      <summary>Ruft die <see cref="T:System.Data.Common.DbTransaction" /> ab, in der dieses <see cref="T:System.Data.Common.DbCommand" />-Objekt ausgeführt wird, oder legt diese fest.</summary>
      <returns>Die Transaktion, in der ein Command-Objekt eines .NET Framework-Datenanbieters ausgeführt wird.Der Standardwert ist ein NULL-Verweis (Nothing in Visual Basic).</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbCommand.UpdatedRowSource">
      <summary>Ruft ab oder legt fest, wie die Ergebnisse von Befehlen auf <see cref="T:System.Data.DataRow" /> angewendet werden, wenn diese von der Update-Methode eines <see cref="T:System.Data.Common.DbDataAdapter" /> verwendet wird.</summary>
      <returns>Einer der <see cref="T:System.Data.UpdateRowSource" />-Werte.Der Standardwert ist Both, sofern der Befehl nicht automatisch generiert wurde.In diesem Fall ist der Standardwert None.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbConnection">
      <summary>Stellt eine Verbindung zu einer Datenbank dar. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbConnection" />-Klasse.</summary>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginDbTransaction(System.Data.IsolationLevel)">
      <summary>Startet eine Datenbanktransaktion.</summary>
      <returns>Ein Objekt, das die neue Transaktion darstellt.</returns>
      <param name="isolationLevel">Ruft die Isolationsstufe für die Transaktion ab.</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction">
      <summary>Startet eine Datenbanktransaktion.</summary>
      <returns>Ein Objekt, das die neue Transaktion darstellt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.BeginTransaction(System.Data.IsolationLevel)">
      <summary>Startet eine Datenbanktransaktion mit dem angegebenen Isolationsgrad.</summary>
      <returns>Ein Objekt, das die neue Transaktion darstellt.</returns>
      <param name="isolationLevel">Ruft die Isolationsstufe für die Transaktion ab.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.ChangeDatabase(System.String)">
      <summary>Wechselt die aktuelle Datenbank für eine geöffnete Verbindung.</summary>
      <param name="databaseName">Gibt den Namen der Datenbank für die zu verwendende Verbindung an.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Close">
      <summary>Schließt die Verbindung mit der Datenbank.Dies ist die bevorzugte Methode zum Schließen offener Verbindungen.</summary>
      <exception cref="T:System.Data.Common.DbException">Der Fehler auf Verbindungsebene, der beim Öffnen der Verbindung aufgetreten ist. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionString">
      <summary>Ruft die Zeichenfolge ab, mit der die Verbindung geöffnet wird, oder legt diese fest.</summary>
      <returns>Die Verbindungszeichenfolge, mit der die ursprüngliche Verbindung hergestellt wird.Der genaue Inhalt der Verbindungszeichenfolge hängt von der bestimmten Datenquelle für diese Verbindung ab.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.ConnectionTimeout">
      <summary>Ruft die Zeit ab, die beim Verbindungsaufbau gewartet werden soll, bis der Versuch beendet und ein Fehler generiert wird.</summary>
      <returns>Die Zeitdauer in Sekunden, die auf das Öffnen einer Verbindung gewartet werden soll.Der Standardwert wird vom bestimmten Verbindungstyp bestimmt, den Sie verwenden.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateCommand">
      <summary>Erstellt ein <see cref="T:System.Data.Common.DbCommand" />-Objekt, das der aktuellen Verbindung zugeordnet ist, und gibt es zurück.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbCommand" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.CreateDbCommand">
      <summary>Erstellt ein <see cref="T:System.Data.Common.DbCommand" />-Objekt, das der aktuellen Verbindung zugeordnet ist, und gibt es zurück.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbCommand" />-Objekt.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnection.Database">
      <summary>Ruft den Namen der aktuellen Datenbank ab, nachdem eine Verbindung geöffnet wurde, oder ruft den in der Verbindungszeichenfolge angegebenen Datenbanknamen ab, bevor die Verbindung geöffnet wird.</summary>
      <returns>Der Name der aktuellen Datenbank oder der nach dem Öffnen einer Verbindung zu verwendenden Datenbank.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.DataSource">
      <summary>Ruft den Namen des Datenbankservers ab, mit dem eine Verbindung hergestellt werden soll.</summary>
      <returns>Der Name des Datenbankervers, mit dem eine Verbindung hergestellt werden soll.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.Dispose"></member>
    <member name="M:System.Data.Common.DbConnection.Dispose(System.Boolean)"></member>
    <member name="M:System.Data.Common.DbConnection.OnStateChange(System.Data.StateChangeEventArgs)">
      <summary>Löst das <see cref="E:System.Data.Common.DbConnection.StateChange" />-Ereignis aus.</summary>
      <param name="stateChange">Ein <see cref="T:System.Data.StateChangeEventArgs" />, das die Ereignisdaten enthält.</param>
    </member>
    <member name="M:System.Data.Common.DbConnection.Open">
      <summary>Öffnet eine Datenbankverbindung mit den durch <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> angegebenen Einstellungen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbConnection.Open" />, die eine Datenbankverbindung mit den Einstellungen öffnet, die durch <see cref="P:System.Data.Common.DbConnection.ConnectionString" /> angegeben werden.Diese Methode ruft die virtuelle Methode <see cref="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnection.OpenAsync(System.Threading.CancellationToken)">
      <summary>Dies ist die asynchrone Version von <see cref="M:System.Data.Common.DbConnection.Open" />.Anbieter sollten mit einer entsprechenden Implementierung überschreiben.Das Abbruchtoken kann optional berücksichtigt werden.Die Standardimplementierung ruft den asynchronen <see cref="M:System.Data.Common.DbConnection.Open" />-Aufruf auf und gibt eine abgeschlossene Aufgabe zurück.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen cancellationToken erhalten hat.Die Ausnahmen, die von Open ausgelöst werden, werden über die zurückgegebene Task Exception-Eigenschaft übermittelt.Rufen Sie keine anderen Methoden und Eigenschaften des DbConnection-Objekts auf, bis die zurückgegebene Aufgabe abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Die Abbruchanweisung.</param>
    </member>
    <member name="P:System.Data.Common.DbConnection.ServerVersion">
      <summary>Ruft eine Zeichenfolge ab, die die Version des Servers darstellt, mit der das Objekt verbunden ist.</summary>
      <returns>Die Version der Datenbank.Das Format der zurückgegebenen Zeichenfolge hängt vom bestimmten Verbindungstyp ab, den Sie verwenden.</returns>
      <exception cref="T:System.InvalidOperationException">
        <see cref="P:System.Data.Common.DbConnection.ServerVersion" /> wurde aufgerufen, als die zurückgegebene Aufgabe noch nicht abgeschlossen war und die Verbindung nach einem Aufruf von <see cref="Overload:System.Data.Common.DbConnection.OpenAsync" /> nicht geöffnet war.</exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbConnection.State">
      <summary>Ruft eine Zeichenfolge ab, die den Zustand der Verbindung beschreibt.</summary>
      <returns>Der Zustand der Verbindung.Das Format der zurückgegebenen Zeichenfolge hängt vom bestimmten Verbindungstyp ab, den Sie verwenden.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="E:System.Data.Common.DbConnection.StateChange">
      <summary>Tritt ein, wenn der Zustand des Ereignisses geändert wird.</summary>
    </member>
    <member name="T:System.Data.Common.DbConnectionStringBuilder">
      <summary>Stellt eine Basisklasse für stark typisierte Verbindungszeichenfolgen-Generatoren bereit.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Klasse.</summary>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Add(System.String,System.Object)">
      <summary>Fügt dem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> einen Eintrag mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="keyword">Der dem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> hinzuzufügende Schlüssel.</param>
      <param name="value">Der Wert für den angegebenen Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> ist ein NULL-Verweis (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> ist schreibgeschützt. - oder -<see cref="T:System.Data.Common.DbConnectionStringBuilder" /> hat eine feste Größe.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.AppendKeyValuePair(System.Text.StringBuilder,System.String,System.String)">
      <summary>Bietet eine effiziente und sichere Möglichkeit, einem vorhandenen <see cref="T:System.Text.StringBuilder" />-Objekt einen Schlüssel und einen Wert anzufügen.</summary>
      <param name="builder">Der <see cref="T:System.Text.StringBuilder" />, dem das Schlüssel-Wert-Paar hinzugefügt werden soll.</param>
      <param name="keyword">Der hinzuzufügende Schlüssel.</param>
      <param name="value">Der Wert für den angegebenen Schlüssel.</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Clear">
      <summary>Löscht den Inhalt der <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Instanz.</summary>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> ist schreibgeschützt.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString">
      <summary>Ruft die Verbindungszeichenfolge ab, die dem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> zugeordnet ist, oder legt diese fest.</summary>
      <returns>Die aktuelle Verbindungszeichenfolge, die aus den Schlüssel-Wert-Paaren in <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> erstellt wurde.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <exception cref="T:System.ArgumentException">Es wurde ein ungültiges Argument für eine Verbindungszeichenfolge bereitgestellt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ContainsKey(System.String)">
      <summary>Bestimmt, ob der <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> einen bestimmten Schlüssel enthält.</summary>
      <returns>true, wenn <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> einen Eintrag mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="keyword">Der im <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> ist ein NULL-Verweis (Nothing in Visual Basic).</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Count">
      <summary>Ruft die aktuelle Anzahl von Schlüsseln ab, die innerhalb der <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" />-Eigenschaft enthalten sind.</summary>
      <returns>Die Anzahl von Schlüsseln, die innerhalb der von der <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Instanz verwalteten Verbindungszeichenfolge enthalten sind.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.EquivalentTo(System.Data.Common.DbConnectionStringBuilder)">
      <summary>Vergleicht die Verbindungsinformationen in diesem <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Objekt mit den Verbindungsinformationen im angegebenen Objekt.</summary>
      <returns>true, wenn die Verbindungsinformationen in beiden <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Objekten eine gleichwertige Verbindungszeichenfolge ergeben, andernfalls false.</returns>
      <param name="connectionStringBuilder">Der <see cref="T:System.Data.Common.DbConnectionStringBuilder" />, der mit diesem <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Objekt verglichen werden soll.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Item(System.String)">
      <summary>Ruft den Wert ab, der dem angegebenen Schlüssel zugeordnet ist, oder legt diesen fest.</summary>
      <returns>Der dem angegebenen Schlüssel zugeordnete Wert.Wenn der angegebene Schlüssel nicht gefunden werden kann, wird beim Abrufen dieses Schlüssels ein NULL-Verweis (Nothing in Visual Basic) zurückgegeben. Beim Festlegen wird ein neues Element mit dem angegebenen Schlüssel erstellt.Die Übergabe eines NULL-Schlüssels (Nothing in Visual Basic) löst eine <see cref="T:System.ArgumentNullException" /> aus.Die Zuweisung eines NULL-Werts entfernt das Schlüssel-Wert-Paar.</returns>
      <param name="keyword">Der Schlüssel des abzurufenden oder festzulegenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> ist ein NULL-Verweis (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Die Eigenschaft wird festgelegt, und der <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> ist schreibgeschützt. - oder -Die Eigenschaft wird festgelegt, <paramref name="keyword" /> ist in der Auflistung nicht vorhanden, und <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> hat eine feste Größe.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Keys">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Schlüssel im <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Schlüssel im <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> enthält.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.Remove(System.String)">
      <summary>Entfernt den Eintrag mit dem angegebenen Schlüssel aus der <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Instanz.</summary>
      <returns>true, wenn der Schlüssel in der Verbindungszeichenfolge vorhanden war und entfernt wurde, false, wenn der Schlüssel nicht vorhanden war.</returns>
      <param name="keyword">Der Schlüssel des Schlüssel-Wert-Paares, das aus der Verbindungszeichenfolge in diesem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> entfernt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> ist NULL (Nothing in Visual Basic).</exception>
      <exception cref="T:System.NotSupportedException">Der <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> ist schreibgeschützt, oder der <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> weist eine feste Größe auf.</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ShouldSerialize(System.String)">
      <summary>Gibt an, ob der angegebene Schlüssel in dieser <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Instanz vorhanden ist.</summary>
      <returns>true, wenn <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> einen Eintrag mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="keyword">Der im <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> zu suchende Schlüssel.</param>
      <filterpriority>2</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Fügt dem <see cref="T:System.Collections.IDictionary" />-Objekt ein Element mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="keyword">Das <see cref="T:System.Object" />, das als Schlüssel für das hinzuzufügende Element verwendet werden soll.</param>
      <param name="value">Das <see cref="T:System.Object" />, das als Wert für das hinzuzufügende Element verwendet werden soll.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.IDictionary" />-Objekt ein Element mit dem angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn <see cref="T:System.Collections.IDictionary" /> ein Element mit dem Schlüssel enthält, andernfalls false.</returns>
      <param name="keyword">Der im <see cref="T:System.Collections.IDictionary" />-Objekt zu suchende Schlüssel.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#GetEnumerator">
      <summary>Gibt ein <see cref="T:System.Collections.IDictionaryEnumerator" />-Objekt für das <see cref="T:System.Collections.IDictionary" />-Objekt zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.IDictionaryEnumerator" />-Objekt für das <see cref="T:System.Collections.IDictionary" />-Objekt.</returns>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ruft das Element mit dem angegebenen Schlüssel ab oder legt dieses fest.</summary>
      <returns>Das Element mit dem angegebenen Schlüssel.</returns>
      <param name="keyword">Der Schlüssel des abzurufenden oder zu festzulegenden Elements.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.IDictionary" />-Objekt.</summary>
      <param name="keyword">Der Schlüssel des zu entfernenden Elements.</param>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />-Objekt, das zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.ToString">
      <summary>Gibt die diesem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> zugeordnete Verbindungszeichenfolge zurück.</summary>
      <returns>Die aktuelle <see cref="P:System.Data.Common.DbConnectionStringBuilder.ConnectionString" />-Eigenschaft.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbConnectionStringBuilder.TryGetValue(System.String,System.Object@)">
      <summary>Ruft einen Wert ab, der dem von diesem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> bereitgestellten Schlüssel entspricht.</summary>
      <returns>true, wenn <paramref name="keyword" /> in der Verbindungszeichenfolge gefunden wurde, andernfalls false.</returns>
      <param name="keyword">Der Schlüssel des abzurufenden Elements.</param>
      <param name="value">Der Wert, der dem <paramref name="key" /> entspricht.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyword" /> enthält einen NULL-Wert (Nothing in Visual Basic).</exception>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="P:System.Data.Common.DbConnectionStringBuilder.Values">
      <summary>Ruft ein <see cref="T:System.Collections.ICollection" /> ab, das die Werte im <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Werte aus dem <see cref="T:System.Data.Common.DbConnectionStringBuilder" /> enthält.</returns>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="T:System.Data.Common.DbDataReader">
      <summary>Liest einen Vorwärtsstream von Zeilen aus einer Datenquelle.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbDataReader" />-Klasse.</summary>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Depth">
      <summary>Ruft einen Wert ab, der die Tiefe der Schachtelung für die aktuelle Zeile angibt.</summary>
      <returns>Die Tiefe der Schachtelung für die aktuelle Zeile.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.Data.Common.DbDataReader" />-Klasse verwendeten Ressourcen frei.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Dispose(System.Boolean)">
      <summary>Gibt die vom <see cref="T:System.Data.Common.DbDataReader" /> verwendeten verwalteten Ressourcen und optional auch die nicht verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um verwaltete und nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.Data.Common.DbDataReader.FieldCount">
      <summary>Ruft die Anzahl der Spalten in der aktuellen Zeile ab.</summary>
      <returns>Die Anzahl der Spalten in der aktuellen Zeile.</returns>
      <exception cref="T:System.NotSupportedException">Es ist keine aktuelle Verbindung zu einer Instanz von SQL Server vorhanden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBoolean(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als booleschen Wert ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetByte(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als ein Byte ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetBytes(System.Int32,System.Int64,System.Byte[],System.Int32,System.Int32)">
      <summary>Liest einen Bytestream aus der angegebenen Spalte beginnend an der durch <paramref name="dataOffset" /> angegebenen Position in den Puffer beginnend an der durch <paramref name="bufferOffset" /> angegebenen Position.</summary>
      <returns>Die tatsächlich gelesene Anzahl von Bytes.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <param name="dataOffset">Der Index in der Zeile, an dem der Lesevorgang beginnen soll.</param>
      <param name="buffer">Der Puffer, in den die Daten kopiert werden sollen.</param>
      <param name="bufferOffset">Der Index im Puffer, an den die Daten kopiert werden.</param>
      <param name="length">Die maximale Anzahl der zu lesenden Zeichen.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChar(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als ein einzelnes Zeichen ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetChars(System.Int32,System.Int64,System.Char[],System.Int32,System.Int32)">
      <summary>Liest einen Zeichenstream aus der angegebenen Spalte beginnend an der durch <paramref name="dataOffset" /> angegebenen Position in den Puffer beginnend an der durch <paramref name="bufferOffset" /> angegebenen Position.</summary>
      <returns>Die tatsächlich gelesene Anzahl von Zeichen.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <param name="dataOffset">Der Index in der Zeile, an dem der Lesevorgang beginnen soll.</param>
      <param name="buffer">Der Puffer, in den die Daten kopiert werden sollen.</param>
      <param name="bufferOffset">Der Index im Puffer, an den die Daten kopiert werden.</param>
      <param name="length">Die maximale Anzahl der zu lesenden Zeichen.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetData(System.Int32)">
      <summary>Gibt ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt für die angeforderte Spaltenordnungszahl zurück.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>1</filterpriority>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" PathDiscovery="*AllFiles*" />
      </PermissionSet>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDataTypeName(System.Int32)">
      <summary>Ruft den Namen des Datentyps der angegebenen Spalte ab.</summary>
      <returns>Eine Zeichenfolge, die den Namen des Datentyps darstellt.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDateTime(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als <see cref="T:System.DateTime" />-Objekt ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDbDataReader(System.Int32)">
      <summary>Gibt ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt für die angeforderte Spaltenordnungszahl zurück, das mit einer anbieterspezifischen Implementierung überschrieben werden kann.</summary>
      <returns>Ein <see cref="T:System.Data.Common.DbDataReader" />-Objekt.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDecimal(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als <see cref="T:System.Decimal" />-Objekt ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetDouble(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Gleitkommazahl mit doppelter Genauigkeit ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetEnumerator">
      <summary>Gibt einen <see cref="T:System.Collections.IEnumerator" /> zurück, mit dem die Zeilen im Datenreader durchlaufen werden können.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem die Zeilen im Datenreader durchlaufen werden können.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldType(System.Int32)">
      <summary>Ruft den Datentyp der angegebenen Spalte ab.</summary>
      <returns>Der Datentyp der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValue``1(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte asynchron als ein Typ ab.</summary>
      <returns>Die Spalte die abgerufen werden soll.</returns>
      <param name="ordinal">Die Spalte die abgerufen werden soll.</param>
      <typeparam name="T">Ruft den Wert der angegebenen Spalte asynchron als ein Typ ab.</typeparam>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.SqlClient.SqlDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.SqlClient.SqlDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wurde versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> entspricht nicht dem Typ, der von SQL Server zurückgegeben wird, oder kann nicht konvertiert werden.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte asynchron als ein Typ ab.</summary>
      <returns>Der Typ des zurückzugebenden Werts.</returns>
      <param name="ordinal">Der Typ des zurückzugebenden Werts.</param>
      <typeparam name="T">Der Typ des zurückzugebenden Werts.Weitere Informationen finden Sie im Abschnitt Hinweise.</typeparam>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wurde versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> entspricht nicht dem Typ, der von der Datenquelle zurückgegeben wird, oder kann nicht konvertiert werden.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32,System.Threading.CancellationToken)">
      <summary>Ruft den Wert der angegebenen Spalte asynchron als ein Typ ab.</summary>
      <returns>Der Typ des zurückzugebenden Werts.</returns>
      <param name="ordinal">Der Typ des zurückzugebenden Werts.</param>
      <param name="cancellationToken">Die Abbruch-Anweisung, die eine Benachrichtigung verteilt, dass Vorgänge abgebrochen werden sollen.Dies garantiert keinen Abbruch.Die Einstellung CancellationToken.None macht diese Methode äquivalent zu <see cref="M:System.Data.Common.DbDataReader.GetFieldValueAsync``1(System.Int32)" />.Die zurückgegebene Aufgabe muss als abgebrochen gekennzeichnet werden.</param>
      <typeparam name="T">Der Typ des zurückzugebenden Werts.Weitere Informationen finden Sie im Abschnitt Hinweise.</typeparam>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wurde versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
      <exception cref="T:System.InvalidCastException">
        <paramref name="T" /> entspricht nicht dem Typ, der von der Datenquelle zurückgegeben wird, oder kann nicht konvertiert werden.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetFloat(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Gleitkommazahl mit einfacher Genauigkeit ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetGuid(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als global eindeutigen Bezeichner (Globally Unique Identifier, GUID) ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt16(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als 16-Bit-Ganzzahl mit Vorzeichen ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt32(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als 32-Bit-Ganzzahl mit Vorzeichen ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetInt64(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als 64-Bit-Ganzzahl mit Vorzeichen ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetName(System.Int32)">
      <summary>Ruft den Namen der Spalte anhand der nullbasierten Spaltenordnungszahl ab.</summary>
      <returns>Der Name der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetOrdinal(System.String)">
      <summary>Ruft bei angegebenem Spaltennamen die Ordnungszahl der Spalte ab.</summary>
      <returns>Die nullbasierte Ordnungszahl der Spalte.</returns>
      <param name="name">Der Name der Spalte.</param>
      <exception cref="T:System.IndexOutOfRangeException">Der angegebene Name ist kein gültiger Spaltenname.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificFieldType(System.Int32)">
      <summary>Gibt den anbieterspezifischen Feldtyp der angegebenen Spalte zurück.</summary>
      <returns>Das <see cref="T:System.Type" />-Objekt, das den Datentyp der angegebenen Spalte beschreibt.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValue(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Instanz von <see cref="T:System.Object" /> ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetProviderSpecificValues(System.Object[])">
      <summary>Ruft alle anbieterspezifischen Attributspalten in der Auflistung der aktuellen Zeile ab.</summary>
      <returns>Die Anzahl der Instanzen von <see cref="T:System.Object" /> im Array.</returns>
      <param name="values">Ein Array vom Typ <see cref="T:System.Object" />, in das die Attributspalten kopiert werden sollen.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetStream(System.Int32)">
      <summary>Ruft Daten als <see cref="T:System.IO.Stream" /> ab.</summary>
      <returns>Das zurückgegebene Objekt.</returns>
      <param name="ordinal">Ruft Daten als <see cref="T:System.IO.Stream" /> ab.</param>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wurde versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
      <exception cref="T:System.InvalidCastException">Der zurückgegebene Typ war keiner der folgenden Typen:BinaryimageVarBinaryudt</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetString(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Instanz von <see cref="T:System.String" /> ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.InvalidCastException">Die angegebene Umwandlung ist ungültig. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetTextReader(System.Int32)">
      <summary>Ruft Daten als <see cref="T:System.IO.TextReader" /> ab.</summary>
      <returns>Das zurückgegebene Objekt.</returns>
      <param name="ordinal">Ruft Daten als <see cref="T:System.IO.TextReader" /> ab.</param>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wurde versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
      <exception cref="T:System.InvalidCastException">Der zurückgegebene Typ war keiner der folgenden Typen:charNCharNTextNVarChartextVarChar</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValue(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Instanz von <see cref="T:System.Object" /> ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.GetValues(System.Object[])">
      <summary>Füllt ein Array von Objekten mit den Spaltenwerten der aktuellen Zeile auf.</summary>
      <returns>Die Anzahl der Instanzen von <see cref="T:System.Object" /> im Array.</returns>
      <param name="values">Ein Array vom Typ <see cref="T:System.Object" />, in das die Attributspalten kopiert werden sollen.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.HasRows">
      <summary>Ruft einen Wert ab, der angibt, ob dieser <see cref="T:System.Data.Common.DbDataReader" /> eine oder mehrere Zeilen enthält.</summary>
      <returns>true, wenn <see cref="T:System.Data.Common.DbDataReader" /> eine oder mehrere Zeilen enthält, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.IsClosed">
      <summary>Ruft einen Wert ab, der angibt, ob der <see cref="T:System.Data.Common.DbDataReader" /> geschlossen ist.</summary>
      <returns>true, wenn die <see cref="T:System.Data.Common.DbDataReader" />-Klasse geschlossen ist, andernfalls false.</returns>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.Data.SqlClient.SqlDataReader" /> ist geschlossen. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)">
      <summary>Ruft einen Wert ab, der angibt, ob die Spalte nicht vorhandene oder fehlende Werte enthält.</summary>
      <returns>true, wenn die angegebene Spalte <see cref="T:System.DBNull" /> entspricht, andernfalls false.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" />, die einen Wert abruft, der angibt, ob die Spalte nicht vorhandene oder fehlende Werte enthält.</summary>
      <returns>true, wenn der angegebene Spaltenwert DBNull entspricht, andernfalls false.</returns>
      <param name="ordinal">Die nullbasierte abzurufende Spalte.</param>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wird versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32,System.Threading.CancellationToken)">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.IsDBNull(System.Int32)" />, die einen Wert abruft, der angibt, ob die Spalte nicht vorhandene oder fehlende Werte enthält.Sendet optional eine Benachrichtigung, dass Vorgänge abgebrochen werden sollen.</summary>
      <returns>true, wenn der angegebene Spaltenwert DBNull entspricht, andernfalls false.</returns>
      <param name="ordinal">Die nullbasierte abzurufende Spalte.</param>
      <param name="cancellationToken">Die Abbruch-Anweisung, die eine Benachrichtigung verteilt, dass Vorgänge abgebrochen werden sollen.Dies garantiert keinen Abbruch.Die Einstellung CancellationToken.None macht diese Methoden äquivalent zu <see cref="M:System.Data.Common.DbDataReader.IsDBNullAsync(System.Int32)" />.Die zurückgegebene Aufgabe muss als abgebrochen gekennzeichnet werden.</param>
      <exception cref="T:System.InvalidOperationException">Die Verbindung wird während des Datenabrufs getrennt oder geschlossen.Der <see cref="T:System.Data.Common.DbDataReader" /> wird während des Datenabrufs geschlossen.Es gibt keine Daten, die gelesen werden können (der erste Aufruf von <see cref="M:System.Data.Common.DbDataReader.Read" /> hat z. B. nicht stattgefunden oder "false" zurückgegeben).Es wird versucht, eine zuvor gelesene Spalte im sequenziellen Modus zu lesen.Es gab einen aktiven asynchronen Vorgang.Dies gilt für alle Get*-Methoden bei der Ausführung im sequenziellen Modus, da sie aufgerufen werden können, während des Lesens eines Datenstroms.</exception>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde versucht, eine Spalte zu lesen, die nicht vorhanden ist.</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.Int32)">
      <summary>Ruft den Wert der angegebenen Spalte als Instanz von <see cref="T:System.Object" /> ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="ordinal">Die nullbasierte Ordnungszahl der Spalte.</param>
      <exception cref="T:System.IndexOutOfRangeException">Der übergebene Index lag außerhalb des Bereichs von 0 (null) bis <see cref="P:System.Data.IDataRecord.FieldCount" />. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.Item(System.String)">
      <summary>Ruft den Wert der angegebenen Spalte als Instanz von <see cref="T:System.Object" /> ab.</summary>
      <returns>Der Wert der angegebenen Spalte.</returns>
      <param name="name">Der Name der Spalte.</param>
      <exception cref="T:System.IndexOutOfRangeException">Es wurde keine Spalte mit dem angegebenen Namen gefunden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResult">
      <summary>Setzt den Reader beim Lesen der Ergebnisse von Batchanweisungen auf das nächste Ergebnis.</summary>
      <returns>true, wenn weitere Resultsets vorhanden sind, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.NextResult" />, die den Reader beim Lesen der Ergebnisse von Batchanweisungen auf das nächste Ergebnis setzt.Ruft <see cref="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.NextResultAsync(System.Threading.CancellationToken)">
      <summary>Dies ist die asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.NextResult" />.Anbieter sollten mit einer entsprechenden Implementierung überschreiben.Das <paramref name="cancellationToken" /> kann optional ignoriert werden.Die Standardimplementierung ruft die synchrone <see cref="M:System.Data.Common.DbDataReader.NextResult" />-Methode auf und gibt eine abgeschlossene Aufgabe zurück. Der aufrufende Thread wird blockiert.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen <paramref name="cancellationToken" /> erhalten wird.Die Ausnahmen, die von <see cref="M:System.Data.Common.DbDataReader.NextResult" /> ausgelöst werden, werden über die zurückgegebene Task Exception-Eigenschaft übermittelt.Andere Methoden und Eigenschaften des DbDataReader-Objekts sollten nicht aufgerufen werden, während die zurückgegebene Aufgabe noch nicht abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Die Abbruchanweisung.</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.Read">
      <summary>Setzt den Reader auf den nächsten Datensatz in einem Resultset.</summary>
      <returns>true, wenn weitere Zeilen vorhanden sind, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync">
      <summary>Eine asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.Read" />, die den Reader auf den nächsten Datensatz in einem Resultset erhöht.Diese Methode ruft <see cref="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)" /> mit CancellationToken.None auf.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="M:System.Data.Common.DbDataReader.ReadAsync(System.Threading.CancellationToken)">
      <summary>Dies ist die asynchrone Version von <see cref="M:System.Data.Common.DbDataReader.Read" />.  Anbieter sollten mit einer entsprechenden Implementierung überschreiben.Das Abbruchtoken kann optional ignoriert werden.Die Standardimplementierung ruft die synchrone <see cref="M:System.Data.Common.DbDataReader.Read" />-Methode auf und gibt eine abgeschlossene Aufgabe zurück. Der aufrufende Thread wird blockiert.Die Standardimplementierung gibt eine abgebrochene Aufgabe zurück, wenn sie einen bereits abgebrochenen cancellationToken erhalten hat.  Die Ausnahmen, die von Read ausgelöst werden, werden über die zurückgegebene Task Exception-Eigenschaft übermittelt.Rufen Sie keine anderen Methoden und Eigenschaften des DbDataReader-Objekts auf, bis die zurückgegebene Aufgabe abgeschlossen ist.</summary>
      <returns>Eine Aufgabe, die den asynchronen Vorgang darstellt.</returns>
      <param name="cancellationToken">Die Abbruchanweisung.</param>
      <exception cref="T:System.Data.Common.DbException">Ein Fehler, der beim Ausführen des Befehlstextes aufgetreten ist.</exception>
    </member>
    <member name="P:System.Data.Common.DbDataReader.RecordsAffected">
      <summary>Ruft die Anzahl der durch die Ausführung der SQL-Anweisung geänderten, eingefügten oder gelöschten Zeilen ab. </summary>
      <returns>Die Anzahl der geänderten, eingefügten oder gelöschten Zeilen. -1 für SELECT-Anweisungen, 0, wenn keine Zeilen betroffen sind oder die Anweisung fehlgeschlagen ist.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbDataReader.VisibleFieldCount">
      <summary>Ruft die Anzahl der nicht ausgeblendeten Felder im <see cref="T:System.Data.Common.DbDataReader" /> ab.</summary>
      <returns>Die Anzahl der nicht ausgeblendeten Felder.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbException">
      <summary>Die Basisklasse für alle von der Datenquelle ausgelösten Ausnahmen.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbException" />-Klasse.</summary>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbException" />-Klasse mit der angegebenen Fehlermeldung.</summary>
      <param name="message">Die für diese Ausnahme anzuzeigende Meldung.</param>
    </member>
    <member name="M:System.Data.Common.DbException.#ctor(System.String,System.Exception)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbException" />-Klasse mit der angegebenen Fehlermeldung und einem Verweis auf die innere Ausnahme, die diese Ausnahme ausgelöst hat.</summary>
      <param name="message">Die Zeichenfolge der Fehlermeldung.</param>
      <param name="innerException">Der Verweis auf die innere Ausnahme.</param>
    </member>
    <member name="T:System.Data.Common.DbParameter">
      <summary>Stellt einen Parameter für einen <see cref="T:System.Data.Common.DbCommand" /> sowie optional dessen Zuordnung zu einer <see cref="T:System.Data.DataSet" />-Spalte dar.Weitere Informationen zu Parametern finden Sie unter Konfigurieren von Parametern und Parameterdatentypen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameter.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbParameter" />-Klasse.</summary>
    </member>
    <member name="P:System.Data.Common.DbParameter.DbType">
      <summary>Ruft die <see cref="T:System.Data.DbType" />-Enumeration des Parameters ab oder legt diese fest.</summary>
      <returns>Einer der <see cref="T:System.Data.DbType" />-Werte.Die Standardeinstellung ist <see cref="F:System.Data.DbType.String" />.</returns>
      <exception cref="T:System.ArgumentException">Die Eigenschaft ist nicht auf einen gültigen <see cref="T:System.Data.DbType" /> festgelegt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Direction">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob der Parameter nur zur Eingabe, nur zur Ausgabe oder bidirektional verwendet wird oder ob es sich um einen Parameter für den Rückgabewert für gespeicherte Prozeduren handelt.</summary>
      <returns>Einer der <see cref="T:System.Data.ParameterDirection" />-Werte.Die Standardeinstellung ist Input.</returns>
      <exception cref="T:System.ArgumentException">Die Eigenschaft ist nicht auf einen der gültigen <see cref="T:System.Data.ParameterDirection" />-Werte festgelegt.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.IsNullable">
      <summary>Ruft einen Wert ab, der angibt, ob der Parameter NULL-Werte akzeptiert, oder legt diesen fest.</summary>
      <returns>true, wenn NULL-Werte akzeptiert werden, andernfalls false.Die Standardeinstellung ist false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.ParameterName">
      <summary>Ruft den Namen des <see cref="T:System.Data.Common.DbParameter" /> ab oder legt diesen fest.</summary>
      <returns>Der Name der <see cref="T:System.Data.Common.DbParameter" />-Klasse.Der Standardwert ist eine leere Zeichenfolge ("").</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Precision">
      <summary>[Unterstützt in .NET Framework 4.5.1 und höheren Versionen] Ruft die maximale Anzahl von Ziffern ab, die zur Darstellung der <see cref="P:System.Data.Common.DbParameter.Value" />-Eigenschaft verwendet werden kann, oder legt diese fest.</summary>
      <returns>Die maximale Anzahl von Ziffern für die Darstellung der <see cref="P:System.Data.Common.DbParameter.Value" />-Eigenschaft.</returns>
    </member>
    <member name="M:System.Data.Common.DbParameter.ResetDbType">
      <summary>Setzt die DbType-Eigenschaft auf ihre ursprünglichen Einstellungen zurück.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Scale">
      <summary>[Unterstützt in .NET Framework 4.5.1 und höheren Versionen] Ruft die Anzahl der Dezimalstellen ab, in die der <see cref="P:System.Data.Common.DbParameter.Value" /> aufgelöst wird, oder legt diese fest.</summary>
      <returns>Die Anzahl der Dezimalstellen, in die der <see cref="P:System.Data.Common.DbParameter.Value" /> aufgelöst wird.</returns>
    </member>
    <member name="P:System.Data.Common.DbParameter.Size">
      <summary>Ruft die maximale Größe der Daten innerhalb der Spalte in Bytes ab oder legt diese fest.</summary>
      <returns>Die maximale Größe der Daten in der Spalte in Bytes.Der Standardwert wird aus dem Parameterwert hergeleitet.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumn">
      <summary>Ruft den Namen der Quellspalte ab, die dem <see cref="T:System.Data.DataSet" /> zugeordnet ist und zum Laden oder Zurückgeben des <see cref="P:System.Data.Common.DbParameter.Value" /> verwendet wird, oder legt diesen fest.</summary>
      <returns>Der Name der Quellspalte, die dem <see cref="T:System.Data.DataSet" /> zugeordnet ist.Der Standard ist eine leere Zeichenfolge.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.SourceColumnNullMapping">
      <summary>Ruft einen Wert ab oder legt einen Wert fest, der angibt, ob die Quellspalte NULL-Werte zulässt.So kann <see cref="T:System.Data.Common.DbCommandBuilder" /> ordnungsgemäß Update-Anweisungen für Spalten generieren, die NULL-Werte zulassen.</summary>
      <returns>true, wenn die Quellspalte NULL-Werte zulässt, andernfalls false.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameter.Value">
      <summary>Ruft den Wert des Parameters ab oder legt diesen fest.</summary>
      <returns>Ein <see cref="T:System.Object" />, das den Wert des Parameters darstellt.Der Standardwert ist NULL.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbParameterCollection">
      <summary>Die Basisklasse für eine Auflistung von Parametern für einen <see cref="T:System.Data.Common.DbCommand" />. </summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Data.Common.DbParameterCollection" />-Klasse.</summary>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Add(System.Object)">
      <summary>Fügt der <see cref="T:System.Data.Common.DbParameterCollection" /> das angegebene <see cref="T:System.Data.Common.DbParameter" />-Objekt hinzu.</summary>
      <returns>Der Index des <see cref="T:System.Data.Common.DbParameter" />-Objekts in der Auflistung.</returns>
      <param name="value">Der <see cref="P:System.Data.Common.DbParameter.Value" /> des <see cref="T:System.Data.Common.DbParameter" />, der der Auflistung hinzugefügt werden soll.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.AddRange(System.Array)">
      <summary>Fügt in der <see cref="T:System.Data.Common.DbParameterCollection" /> ein Array von Elementen mit den angegebenen Werten hinzu.</summary>
      <param name="values">Ein Array von Werten vom Typ <see cref="T:System.Data.Common.DbParameter" />, die der Auflistung hinzugefügt werden sollen.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Clear">
      <summary>Entfernt alle <see cref="T:System.Data.Common.DbParameter" />-Werte aus der <see cref="T:System.Data.Common.DbParameterCollection" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.Object)">
      <summary>Gibt an, ob ein <see cref="T:System.Data.Common.DbParameter" /> mit dem angegebenen <see cref="P:System.Data.Common.DbParameter.Value" /> in der Auflistung enthalten ist.</summary>
      <returns>true, wenn sich der <see cref="T:System.Data.Common.DbParameter" /> in der Auflistung befindet, andernfalls false.</returns>
      <param name="value">Der <see cref="P:System.Data.Common.DbParameter.Value" /> des <see cref="T:System.Data.Common.DbParameter" />, der in der Auflistung gesucht werden soll.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Contains(System.String)">
      <summary>Gibt an, ob ein <see cref="T:System.Data.Common.DbParameter" /> mit dem angegebenen Namen in der Auflistung vorhanden ist.</summary>
      <returns>true, wenn sich der <see cref="T:System.Data.Common.DbParameter" /> in der Auflistung befindet, andernfalls false.</returns>
      <param name="value">Der Name des <see cref="T:System.Data.Common.DbParameter" />, der in der Auflistung gesucht werden soll.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.CopyTo(System.Array,System.Int32)">
      <summary>Kopiert ein Array von Elementen beginnend am angegebenen Index in die Auflistung.</summary>
      <param name="array">Das Array von Elementen, das in die Auflistung kopiert werden soll.</param>
      <param name="index">Der Index in der Auflistung, an den die Elemente kopiert werden sollen.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Count">
      <summary>Gibt die Anzahl der Elemente in der Auflistung an.</summary>
      <returns>Die Anzahl der Elemente in der Auflistung.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetEnumerator">
      <summary>Macht die <see cref="M:System.Collections.IEnumerable.GetEnumerator" />-Methode verfügbar, die eine einfache Iteration über eine Auflistung eines .NET Framework-Datenanbieters unterstützt.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem eine Auflistung durchlaufen werden kann.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.Int32)">
      <summary>Gibt das <see cref="T:System.Data.Common.DbParameter" />-Objekt am angegebenen Index in der Auflistung zurück.</summary>
      <returns>Das <see cref="T:System.Data.Common.DbParameter" />-Objekt am angegebenen Index in der Auflistung.</returns>
      <param name="index">Der Index des <see cref="T:System.Data.Common.DbParameter" /> in der Auflistung.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.GetParameter(System.String)">
      <summary>Gibt das <see cref="T:System.Data.Common.DbParameter" />-Objekt mit dem angegebenen Namen zurück.</summary>
      <returns>Das <see cref="T:System.Data.Common.DbParameter" />-Objekt mit dem angegebenen Namen.</returns>
      <param name="parameterName">Der Name des <see cref="T:System.Data.Common.DbParameter" /> in der Auflistung.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.Object)">
      <summary>Gibt den Index des angegebenen <see cref="T:System.Data.Common.DbParameter" />-Objekts zurück.</summary>
      <returns>Der Index des angegebenen <see cref="T:System.Data.Common.DbParameter" />-Objekts.</returns>
      <param name="value">Das <see cref="T:System.Data.Common.DbParameter" />-Objekt in der Auflistung.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.IndexOf(System.String)">
      <summary>Gibt den Index des <see cref="T:System.Data.Common.DbParameter" />-Objekts mit dem angegebenen Namen zurück.</summary>
      <returns>Der Index des <see cref="T:System.Data.Common.DbParameter" />-Objekts mit dem angegebenen Namen.</returns>
      <param name="parameterName">Der Name des <see cref="T:System.Data.Common.DbParameter" />-Objekts in der Auflistung.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Insert(System.Int32,System.Object)">
      <summary>Fügt das angegebene <see cref="T:System.Data.Common.DbParameter" />-Objekt mit dem angegebenen Namen am angegebenen Index in die Auflistung ein.</summary>
      <param name="index">Der Index, an dem das <see cref="T:System.Data.Common.DbParameter" />-Objekt eingefügt werden soll.</param>
      <param name="value">Das in die Auflistung einzufügende <see cref="T:System.Data.Common.DbParameter" />-Objekt.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.Int32)">
      <summary>Ruft den <see cref="T:System.Data.Common.DbParameter" /> am angegebenen Index ab oder legt diesen fest.</summary>
      <returns>Der <see cref="T:System.Data.Common.DbParameter" /> am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des Parameters.</param>
      <exception cref="T:System.IndexOutOfRangeException">Der angegebene Index ist nicht vorhanden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.Item(System.String)">
      <summary>Ruft den <see cref="T:System.Data.Common.DbParameter" /> mit dem angegebenen Namen ab oder legt diesen fest.</summary>
      <returns>Der <see cref="T:System.Data.Common.DbParameter" /> mit dem angegebenen Namen.</returns>
      <param name="parameterName">Der Name des Parameters.</param>
      <exception cref="T:System.IndexOutOfRangeException">Der angegebene Index ist nicht vorhanden. </exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.Remove(System.Object)">
      <summary>Entfernt das angegebene <see cref="T:System.Data.Common.DbParameter" />-Objekt aus der Auflistung.</summary>
      <param name="value">Das zu entfernende <see cref="T:System.Data.Common.DbParameter" />-Objekt.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.Int32)">
      <summary>Entfernt das <see cref="T:System.Data.Common.DbParameter" />-Objekt am angegebenen Index aus der Auflistung.</summary>
      <param name="index">Der Index des <see cref="T:System.Data.Common.DbParameter" />-Objekts.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.RemoveAt(System.String)">
      <summary>Entfernt das <see cref="T:System.Data.Common.DbParameter" />-Objekt mit dem angegebenen Namen aus der Auflistung.</summary>
      <param name="parameterName">Der Name des zu entfernenden <see cref="T:System.Data.Common.DbParameter" />-Objekts.</param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.Int32,System.Data.Common.DbParameter)">
      <summary>Legt das <see cref="T:System.Data.Common.DbParameter" />-Objekt am angegebenen Index auf einen neuen Wert fest. </summary>
      <param name="index">Der Index des <see cref="T:System.Data.Common.DbParameter" />-Objekts.</param>
      <param name="value">Der neue <see cref="T:System.Data.Common.DbParameter" />-Wert.</param>
    </member>
    <member name="M:System.Data.Common.DbParameterCollection.SetParameter(System.String,System.Data.Common.DbParameter)">
      <summary>Legt das <see cref="T:System.Data.Common.DbParameter" />-Objekt mit dem angegebenen Namen auf einen neuen Wert fest.</summary>
      <param name="parameterName">Der Name des <see cref="T:System.Data.Common.DbParameter" />-Objekts in der Auflistung.</param>
      <param name="value">Der neue <see cref="T:System.Data.Common.DbParameter" />-Wert.</param>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.SyncRoot">
      <summary>Gibt das <see cref="T:System.Object" /> an, das zum Synchronisieren des Zugriffs auf die Auflistung verwendet wird.</summary>
      <returns>Ein <see cref="T:System.Object" />, das zum Synchronisieren des Zugriffs auf die <see cref="T:System.Data.Common.DbParameterCollection" /> verwendet wird.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#ICollection#IsSynchronized"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsFixedSize"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#IsReadOnly"></member>
    <member name="P:System.Data.Common.DbParameterCollection.System#Collections#IList#Item(System.Int32)">
      <summary>Ruft das Element am angegebenen Index ab oder legt dieses fest.</summary>
      <returns>Das Element am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des Elements, das abgerufen oder festgelegt werden soll.</param>
    </member>
    <member name="T:System.Data.Common.DbProviderFactory">
      <summary>Stellt einen Satz von Methoden für das Erstellen von Instanzen der Implementierung eines Anbieters der Datenquellenklassen dar.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.#ctor">
      <summary>Initialisiert eine neue Instanz einer <see cref="T:System.Data.Common.DbProviderFactory" />-Klasse.</summary>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateCommand">
      <summary>Gibt eine neue Instanz der Klasse des Anbieters zurück, die die <see cref="T:System.Data.Common.DbCommand" />-Klasse implementiert.</summary>
      <returns>Eine neue Instanz von <see cref="T:System.Data.Common.DbCommand" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnection">
      <summary>Gibt eine neue Instanz der Klasse des Anbieters zurück, die die <see cref="T:System.Data.Common.DbConnection" />-Klasse implementiert.</summary>
      <returns>Eine neue Instanz von <see cref="T:System.Data.Common.DbConnection" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateConnectionStringBuilder">
      <summary>Gibt eine neue Instanz der Klasse des Anbieters zurück, die die <see cref="T:System.Data.Common.DbConnectionStringBuilder" />-Klasse implementiert.</summary>
      <returns>Eine neue Instanz von <see cref="T:System.Data.Common.DbConnectionStringBuilder" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbProviderFactory.CreateParameter">
      <summary>Gibt eine neue Instanz der Klasse des Anbieters zurück, die die <see cref="T:System.Data.Common.DbParameter" />-Klasse implementiert.</summary>
      <returns>Eine neue Instanz von <see cref="T:System.Data.Common.DbParameter" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Data.Common.DbTransaction">
      <summary>Die Basisklasse für eine Transaktion. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.#ctor">
      <summary>Initialisiert ein neues <see cref="T:System.Data.Common.DbTransaction" />-Objekt.</summary>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Commit">
      <summary>Führt einen Commit der Datenbanktransaktion aus.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.Connection">
      <summary>Gibt das <see cref="T:System.Data.Common.DbConnection" />-Objekt an, das der Transaktion zugeordnet ist.</summary>
      <returns>Das der Transaktion zugeordnete <see cref="T:System.Data.Common.DbConnection" />-Objekt.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="P:System.Data.Common.DbTransaction.DbConnection">
      <summary>Gibt das <see cref="T:System.Data.Common.DbConnection" />-Objekt an, das der Transaktion zugeordnet ist.</summary>
      <returns>Das der Transaktion zugeordnete <see cref="T:System.Data.Common.DbConnection" />-Objekt.</returns>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose">
      <summary>Gibt die von <see cref="T:System.Data.Common.DbTransaction" /> verwendeten, nicht verwalteten Ressourcen frei.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.Data.Common.DbTransaction" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">Wenn true, gibt diese Methode alle Ressourcen frei, die von verwalteten Objekten verwendet werden, auf die diese <see cref="T:System.Data.Common.DbTransaction" /> verweist.</param>
    </member>
    <member name="P:System.Data.Common.DbTransaction.IsolationLevel">
      <summary>Gibt die <see cref="T:System.Data.IsolationLevel" />-Enumeration für diese Transaktion an.</summary>
      <returns>Die <see cref="T:System.Data.IsolationLevel" />-Enumeration für diese Transaktion.</returns>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Data.Common.DbTransaction.Rollback">
      <summary>Führt einen Rollback einer noch ausstehenden Transaktion aus.</summary>
      <filterpriority>1</filterpriority>
    </member>
  </members>
</doc>