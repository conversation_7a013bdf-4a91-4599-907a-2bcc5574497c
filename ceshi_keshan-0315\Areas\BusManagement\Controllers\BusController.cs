using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.Areas.BusManagement.App_Start;
using ceshi_keshan_0315.Areas.BusManagement.Models;
using ceshi_keshan_0315.Areas.BusManagement.ViewModels;
using ceshi_keshan_0315.Common;
using NPOI.XSSF.UserModel;
using System.IO;

namespace ceshi_keshan_0315.Areas.BusManagement.Controllers
{
    /// <summary>
    /// 公车管理控制器
    /// 新增模块：处理公车信息的增删改查和导出功能
    /// </summary>
    public class BusController : Controller
    {
        // 临时注释掉 FreeSql，避免数据库连接问题
        // private readonly FreeSql.IFreeSql _freeSql = BusFreeSqlConfig.Instance;

        /// <summary>
        /// 公车列表页面
        /// </summary>
        /// <param name="plateNumber">车牌号筛选</param>
        /// <param name="status">状态筛选</param>
        /// <param name="purchaseDate">购买日期筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <returns></returns>
        // public ActionResult Index(string plateNumber = "", BusStatus? status = null, 
        //     DateTime? purchaseDate = null, int pageIndex = 1)
        // {
        //     ViewBag.Title = "公车信息管理";
            
        //     try
        //     {
        //         var query = _freeSql.Select<Bus>()
        //             .Where(b => !b.IsDeleted);

        //         // 条件筛选
        //         if (!string.IsNullOrEmpty(plateNumber))
        //         {
        //             query = query.Where(b => b.PlateNumber.Contains(plateNumber));
        //         }

        //         if (status.HasValue)
        //         {
        //             query = query.Where(b => b.Status == status.Value);
        //         }

        //         if (purchaseDate.HasValue)
        //         {
        //             var date = purchaseDate.Value.Date;
        //             query = query.Where(b => b.PurchaseDate >= date && b.PurchaseDate < date.AddDays(1));
        //         }

        //         // 分页查询
        //         const int pageSize = 20;
        //         var totalCount = query.Count();
        //         var buses = query
        //             .OrderByDescending(b => b.CreateTime)
        //             .Page(pageIndex, pageSize)
        //             .ToList();

        //         // 转换为视图模型
        //         var busVMs = buses.Select(b => new BusVM
        //         {
        //             Id = b.Id,
        //             PlateNumber = b.PlateNumber,
        //             Model = b.Model,
        //             Brand = b.Brand,
        //             Status = b.Status,
        //             StatusText = GetStatusText(b.Status),
        //             SeatCount = b.SeatCount,
        //             PurchaseDate = b.PurchaseDate,
        //             Remarks = b.Remarks
        //         }).ToList();

        //         // 分页信息
        //         ViewBag.TotalCount = totalCount;
        //         ViewBag.PageIndex = pageIndex;
        //         ViewBag.PageSize = pageSize;
        //         ViewBag.TotalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        //         ViewBag.HasPreviousPage = pageIndex > 1;
        //         ViewBag.HasNextPage = pageIndex < ViewBag.TotalPages;

        //         // 筛选条件回传
        //         ViewBag.PlateNumber = plateNumber;
        //         ViewBag.Status = status;
        //         ViewBag.PurchaseDate = purchaseDate?.ToString("yyyy-MM-dd");

        //         return View(busVMs);
        //     }
        //     catch (Exception ex)
        //     {
        //         ViewBag.ErrorMessage = "查询公车信息时发生错误：" + ex.Message;
        //         return View(new System.Collections.Generic.List<BusVM>());
        //     }
        // }


        public ActionResult Index()
        {
            ViewBag.Title = "Home Page";

            return View();
        }

        /// <summary>
        /// 公车详情页面
        /// </summary>
        /// <param name="id">公车ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                var bus = _freeSql.Select<Bus>()
                    .Where(b => b.Id == id && !b.IsDeleted)
                    .First();

                if (bus == null)
                {
                    return HttpNotFound("未找到指定的公车信息");
                }

                var busVM = new BusVM
                {
                    Id = bus.Id,
                    PlateNumber = bus.PlateNumber,
                    Model = bus.Model,
                    Brand = bus.Brand,
                    Status = bus.Status,
                    StatusText = GetStatusText(bus.Status),
                    SeatCount = bus.SeatCount,
                    PurchaseDate = bus.PurchaseDate,
                    Remarks = bus.Remarks
                };

                return View(busVM);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取公车详情时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 导出公车信息到Excel
        /// </summary>
        /// <returns></returns>
        public ActionResult ExportToExcel()
        {
            try
            {
                var buses = _freeSql.Select<Bus>()
                    .Where(b => !b.IsDeleted)
                    .OrderBy(b => b.PlateNumber)
                    .ToList();

                var workbook = new XSSFWorkbook();
                var sheet = workbook.CreateSheet("公车清单");

                // 创建标题行
                var headerRow = sheet.CreateRow(0);
                var headers = new[] { "车牌号", "车型", "品牌", "状态", "座位数", "购买日期", "备注" };
                for (int i = 0; i < headers.Length; i++)
                {
                    headerRow.CreateCell(i).SetCellValue(headers[i]);
                }

                // 填充数据
                for (int i = 0; i < buses.Count; i++)
                {
                    var row = sheet.CreateRow(i + 1);
                    var bus = buses[i];
                    
                    row.CreateCell(0).SetCellValue(bus.PlateNumber);
                    row.CreateCell(1).SetCellValue(bus.Model ?? "");
                    row.CreateCell(2).SetCellValue(bus.Brand ?? "");
                    row.CreateCell(3).SetCellValue(GetStatusText(bus.Status));
                    row.CreateCell(4).SetCellValue(bus.SeatCount);
                    row.CreateCell(5).SetCellValue(bus.PurchaseDate.ToString("yyyy-MM-dd"));
                    row.CreateCell(6).SetCellValue(bus.Remarks ?? "");
                }

                // 自动调整列宽
                for (int i = 0; i < headers.Length; i++)
                {
                    sheet.AutoSizeColumn(i);
                }

                // 返回文件流
                var stream = new MemoryStream();
                workbook.Write(stream);
                var fileName = $"公车清单_{DateTime.Now:yyyyMMdd}.xlsx";
                
                return File(stream.ToArray(), "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                TempData["ErrorMessage"] = "导出Excel时发生错误：" + ex.Message;
                return RedirectToAction("Index");
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(BusStatus status)
        {
            return status switch
            {
                BusStatus.Available => "可用",
                BusStatus.InUse => "使用中",
                BusStatus.Maintenance => "维修中",
                BusStatus.Retired => "已报废",
                _ => "未知"
            };
        }
    }
}
