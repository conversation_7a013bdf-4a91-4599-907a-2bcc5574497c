﻿
@{
    ViewBag.Title = "ls_<PERSON><PERSON>han_ribaobiao_huizong";
}

<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title>市县公司日报表汇总查询</title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_sqsrbb" id="workday_sqsrbb" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_eqsrbb" id="workday_eqsrbb" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
               
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/ls_quansheng_ribaobiao_huizong?workday_sqsrbb=" + $('#workday_sqsrbb').val();
            $.getJSON("/api/values/ls_quansheng_ribaobiao_huizong?workday_sqsrbb=" + $('#workday_sqsrbb').val() + "&workday_eqsrbb=" + $('#workday_eqsrbb').val() , function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>全省</td><td>92</td><td>95</td><td>98</td><td>0#</td><td>燃料油</td><td>CNG</td><td>LNG</td><td>去年全省</td><td>去年92</td><td>去年95</td><td>去年98</td><td>去年0#</td><td>去年燃料油</td><td>去年CNG</td><td>去年LNG</tr>';
                    $.each(jsonobj, function (i, item) {
                        htmltable += '<tr><td>' + item.quansheng +  '</td><td>' + item.SalesVola
                            + '</td><td>' + item.SalesVolb + '</td><td>' + item.SalesVolc + '</td><td>' + item.SalesVold + '</td><td>'
                            + item.rly + '</td><td>' + item.CNG + '</td><td>' + item.LNG +
                            '</td><td>' + item.qn_quansheng + '</td><td>' + item.qn_SalesVola
                            + '</td><td>' + item.qn_SalesVolb + '</td><td>' + item.qn_SalesVolc + '</td><td>' + item.qn_SalesVold + '</td><td>'
                            + item.qn_rly + '</td><td>' + item.qn_CNG + '</td><td>' + item.qn_LNG +
                            '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/ls_quansheng_ribaobiao_huizong_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>



