@model ceshi_keshan_0315.ViewModels.BusApplicationVM
@{
    ViewBag.Title = "申请详情";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">申请编号：#@Model.Id</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            <!-- 申请信息 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">申请信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="dl-horizontal">
                                <dt>申请人：</dt>
                                <dd>@Model.ApplicantName</dd>
                                <dt>申请部门：</dt>
                                <dd>@Model.Department</dd>
                                <dt>联系电话：</dt>
                                <dd>@Model.Phone</dd>
                                <dt>用车目的：</dt>
                                <dd>@Model.Purpose</dd>
                                <dt>目的地：</dt>
                                <dd>@Model.Destination</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="dl-horizontal">
                                <dt>开始时间：</dt>
                                <dd>@Model.StartTime.ToString("yyyy-MM-dd HH:mm")</dd>
                                <dt>结束时间：</dt>
                                <dd>@Model.EndTime.ToString("yyyy-MM-dd HH:mm")</dd>
                                <dt>用车时长：</dt>
                                <dd>@Model.Duration.ToString("F1") 小时</dd>
                                <dt>预计人数：</dt>
                                <dd>@Model.EstimatedPeople 人</dd>
                                <dt>申请时间：</dt>
                                <dd>@Model.CreateTime.ToString("yyyy-MM-dd HH:mm")</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 车辆信息 -->
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4 class="panel-title">车辆信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="dl-horizontal">
                                <dt>车牌号：</dt>
                                <dd><strong>@Model.PlateNumber</strong></dd>
                                <dt>车型：</dt>
                                <dd>@Model.Model</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 审批信息 -->
            <div class="panel panel-warning">
                <div class="panel-heading">
                    <h4 class="panel-title">审批信息</h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="dl-horizontal">
                                <dt>申请状态：</dt>
                                <dd>
                                    <span class="label label-info">@Model.StatusText</span>
                                </dd>
                                @if (!string.IsNullOrEmpty(Model.ReviewerName))
                                {
                                    <dt>审批人：</dt>
                                    <dd>@Model.ReviewerName</dd>
                                }
                                @if (Model.ReviewTime.HasValue)
                                {
                                    <dt>审批时间：</dt>
                                    <dd>@Model.ReviewTime.Value.ToString("yyyy-MM-dd HH:mm")</dd>
                                }
                            </dl>
                        </div>
                        <div class="col-md-6">
                            @if (!string.IsNullOrEmpty(Model.ReviewComments))
                            {
                                <dl class="dl-horizontal">
                                    <dt>审批意见：</dt>
                                    <dd>@Model.ReviewComments</dd>
                                </dl>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="panel panel-default">
                <div class="panel-body text-center">
                    <a href="/BusApplication/Index" class="btn btn-default">
                        <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                    </a>
                    @if (Model.CanEdit)
                    {
                        <a href="/BusApplication/Edit/@Model.Id" class="btn btn-primary">
                            <i class="glyphicon glyphicon-edit"></i> 编辑申请
                        </a>
                    }
                    @if (Model.Status == ceshi_keshan_0315.Models.ApplicationStatus.Pending)
                    {
                        <button type="button" class="btn btn-success" onclick="reviewApplication(@Model.Id, 'approve')">
                            <i class="glyphicon glyphicon-ok"></i> 批准
                        </button>
                        <button type="button" class="btn btn-danger" onclick="reviewApplication(@Model.Id, 'reject')">
                            <i class="glyphicon glyphicon-remove"></i> 拒绝
                        </button>
                    }
                    <a href="/Bus/Index" class="btn btn-info">
                        <i class="glyphicon glyphicon-list"></i> 公车列表
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 审批表单 -->
<form id="reviewForm" method="post" action="/BusApplication/ReviewAction" style="display: none;">
    <input type="hidden" name="id" value="@Model.Id" />
    <input type="hidden" name="action" id="reviewAction" />
    <input type="hidden" name="comments" id="reviewComments" />
</form>

<script>
function reviewApplication(id, action) {
    var actionText = action === 'approve' ? '批准' : '拒绝';
    var comments = prompt('请输入审批意见（可选）：');

    if (comments !== null) {
        document.getElementById('reviewAction').value = action;
        document.getElementById('reviewComments').value = comments || '';
        document.getElementById('reviewForm').submit();
    }
}
</script>