using System;
using System.Web.Mvc;

namespace ceshi_keshan_0315.Controllers
{
    /// <summary>
    /// 测试控制器
    /// 新增模块：用于测试路由和基本功能
    /// </summary>
    public class TestController : Controller
    {
        /// <summary>
        /// 测试页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Index()
        {
            ViewBag.Title = "路由测试页面";
            ViewBag.CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            ViewBag.ControllerName = this.GetType().FullName;
            ViewBag.ActionName = this.ControllerContext.RouteData.Values["action"];
            
            return View();
        }

        /// <summary>
        /// 简单的 JSON 测试
        /// </summary>
        /// <returns></returns>
        public JsonResult TestJson()
        {
            var result = new
            {
                Success = true,
                Message = "路由工作正常！",
                CurrentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                ControllerName = this.GetType().FullName
            };

            return Json(result, JsonRequestBehavior.AllowGet);
        }

        /// <summary>
        /// 简单的文本测试
        /// </summary>
        /// <returns></returns>
        public ActionResult TestText()
        {
            return Content("✅ 路由测试成功！当前时间：" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
        }
    }
}
