using System;
using System.Linq;
using System.Web.Mvc;
using ceshi_keshan_0315.Areas.BusManagement.App_Start;
using ceshi_keshan_0315.Areas.BusManagement.Models;
using ceshi_keshan_0315.Areas.BusManagement.ViewModels;

namespace ceshi_keshan_0315.Areas.BusManagement.Controllers
{
    /// <summary>
    /// 公车申请控制器
    /// 新增模块：处理公车申请的创建、查看和管理功能
    /// </summary>
    public class ApplicationController : Controller
    {
        private readonly FreeSql.IFreeSql _freeSql = BusFreeSqlConfig.Instance;

        /// <summary>
        /// 申请列表页面
        /// </summary>
        /// <param name="query">查询条件</param>
        /// <returns></returns>
        // public ActionResult Index(BusApplicationQueryVM query)
        // {
        //     ViewBag.Title = "公车申请管理";
            
        //     try
        //     {
        //         var dbQuery = _freeSql.Select<BusApplication, Bus>()
        //             .LeftJoin((a, b) => a.BusId == b.Id)
        //             .Where((a, b) => !a.Is<PERSON>eleted);

        //         // 条件筛选
        //         if (!string.IsNullOrEmpty(query.PlateNumber))
        //         {
        //             dbQuery = dbQuery.Where((a, b) => b.PlateNumber.Contains(query.PlateNumber));
        //         }

        //         if (!string.IsNullOrEmpty(query.ApplicantName))
        //         {
        //             dbQuery = dbQuery.Where((a, b) => a.ApplicantName.Contains(query.ApplicantName));
        //         }

        //         if (!string.IsNullOrEmpty(query.Department))
        //         {
        //             dbQuery = dbQuery.Where((a, b) => a.Department.Contains(query.Department));
        //         }

        //         if (query.Status.HasValue)
        //         {
        //             dbQuery = dbQuery.Where((a, b) => a.Status == query.Status.Value);
        //         }

        //         if (query.StartDate.HasValue)
        //         {
        //             dbQuery = dbQuery.Where((a, b) => a.StartTime >= query.StartDate.Value);
        //         }

        //         if (query.EndDate.HasValue)
        //         {
        //             var endDate = query.EndDate.Value.AddDays(1);
        //             dbQuery = dbQuery.Where((a, b) => a.StartTime < endDate);
        //         }

        //         // 分页查询
        //         query.TotalCount = (int)dbQuery.Count();
        //         var applications = dbQuery
        //             .OrderByDescending((a, b) => a.CreateTime)
        //             .Page(query.PageIndex, query.PageSize)
        //             .ToList((a, b) => new BusApplicationVM
        //             {
        //                 Id = a.Id,
        //                 PlateNumber = b.PlateNumber,
        //                 Model = b.Model,
        //                 ApplicantName = a.ApplicantName,
        //                 Department = a.Department,
        //                 Phone = a.Phone,
        //                 Purpose = a.Purpose,
        //                 Destination = a.Destination,
        //                 StartTime = a.StartTime,
        //                 EndTime = a.EndTime,
        //                 EstimatedPeople = a.EstimatedPeople,
        //                 Status = a.Status,
        //                 StatusText = GetStatusText(a.Status),
        //                 ReviewerName = a.ReviewerName,
        //                 ReviewTime = a.ReviewTime,
        //                 ReviewComments = a.ReviewComments,
        //                 CreateTime = a.CreateTime
        //             });

        //         ViewBag.Query = query;
        //         return View(applications);
        //     }
        //     catch (Exception ex)
        //     {
        //         ViewBag.ErrorMessage = "查询申请记录时发生错误：" + ex.Message;
        //         return View(new System.Collections.Generic.List<BusApplicationVM>());
        //     }
        // }

        public ActionResult Index()
        {
            ViewBag.Title = "Home Page";

            return View();
        }

        /// <summary>
        /// 创建申请页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Create()
        {
            ViewBag.Title = "创建公车申请";

            try
            {
                // 获取可用公车列表
                var availableBuses = _freeSql.Select<Bus>()
                    .Where(b => b.Status == BusStatus.Available && !b.IsDeleted)
                    .OrderBy(b => b.PlateNumber)
                    .ToList();

                ViewBag.AvailableBuses = availableBuses.Select(b => new SelectListItem
                {
                    Value = b.Id.ToString(),
                    Text = $"{b.PlateNumber} ({b.Model}, {b.SeatCount}座)"
                }).ToList();

                return View();
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "加载创建页面时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 申请详情页面
        /// </summary>
        /// <param name="id">申请ID</param>
        /// <returns></returns>
        public ActionResult Details(int id)
        {
            try
            {
                var application = _freeSql.Select<BusApplication, Bus>()
                    .LeftJoin((a, b) => a.BusId == b.Id)
                    .Where((a, b) => a.Id == id && !a.IsDeleted)
                    .ToOne((a, b) => new BusApplicationVM
                    {
                        Id = a.Id,
                        PlateNumber = b.PlateNumber,
                        Model = b.Model,
                        ApplicantName = a.ApplicantName,
                        Department = a.Department,
                        Phone = a.Phone,
                        Purpose = a.Purpose,
                        Destination = a.Destination,
                        StartTime = a.StartTime,
                        EndTime = a.EndTime,
                        EstimatedPeople = a.EstimatedPeople,
                        Status = a.Status,
                        StatusText = GetStatusText(a.Status),
                        ReviewerName = a.ReviewerName,
                        ReviewTime = a.ReviewTime,
                        ReviewComments = a.ReviewComments,
                        CreateTime = a.CreateTime
                    });

                if (application == null)
                {
                    return HttpNotFound("未找到指定的申请记录");
                }

                return View(application);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "获取申请详情时发生错误：" + ex.Message;
                return View();
            }
        }

        /// <summary>
        /// 审批页面
        /// </summary>
        /// <returns></returns>
        public ActionResult Review()
        {
            ViewBag.Title = "申请审批";
            
            try
            {
                // 获取待审批的申请
                var pendingApplications = _freeSql.Select<BusApplication, Bus>()
                    .LeftJoin((a, b) => a.BusId == b.Id)
                    .Where((a, b) => a.Status == ApplicationStatus.Pending && !a.IsDeleted)
                    .OrderBy((a, b) => a.CreateTime)
                    .ToList((a, b) => new BusApplicationVM
                    {
                        Id = a.Id,
                        PlateNumber = b.PlateNumber,
                        Model = b.Model,
                        ApplicantName = a.ApplicantName,
                        Department = a.Department,
                        Purpose = a.Purpose,
                        StartTime = a.StartTime,
                        EndTime = a.EndTime,
                        EstimatedPeople = a.EstimatedPeople,
                        CreateTime = a.CreateTime
                    });

                ViewBag.PendingCount = pendingApplications.Count;
                return View(pendingApplications);
            }
            catch (Exception ex)
            {
                ViewBag.ErrorMessage = "加载审批页面时发生错误：" + ex.Message;
                return View(new System.Collections.Generic.List<BusApplicationVM>());
            }
        }

        /// <summary>
        /// 获取状态文本
        /// </summary>
        /// <param name="status">状态枚举</param>
        /// <returns></returns>
        private string GetStatusText(ApplicationStatus status)
        {
            return status switch
            {
                ApplicationStatus.Pending => "待审批",
                ApplicationStatus.Approved => "已批准",
                ApplicationStatus.Rejected => "已拒绝",
                ApplicationStatus.Completed => "已完成",
                ApplicationStatus.Cancelled => "已取消",
                _ => "未知"
            };
        }
    }
}
