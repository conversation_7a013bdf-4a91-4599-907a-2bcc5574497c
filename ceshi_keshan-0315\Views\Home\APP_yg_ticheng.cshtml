﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            position: relative; /* 添加这一行 */
        }

        h1 {
            color: #333;
        }

        table {
            border-collapse: collapse;
        }

        th, td {
            border: 1px solid black;
            padding: 8px;
        }

        input[type="submit"] {
            background-color: #4CAF50;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        input[type="button"] {
            background-color: #4CAF50;
            color: white;
            padding: 8px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .submit-button {
            text-align: center;
        }
    </style>
</head>
<body>
    <h1>员工提成统计表</h1>
    <div>
        <form method="post" id="form1">

            <td>查询月份</td>
            <td><input type="month" name="bill_date_month" id="bill_date_month"></td>

            <td>员工名字</td>
            <td><input type="text" name="emp_name" id="emp_name"></td>

            <td>员工手机号</td>
            <td><input type="text" name="emp_phone" id="emp_phone"></td>

            <td>油站名称</td>
            <td><input type="text" name="store_ou_name" id="store_ou_name"></td>

            <td>油站编码</td>
            <td><input type="text" name="standard_code" id="standard_code"></td>

            <td colspan="2" class="submit-button">

                <input id="Button1" type="button" value="提交">
                <input id="btn" type="submit" value="导出" />

        </form>

        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>


    </div>
</body>
</html>


<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/APP_yg_ticheng?bill_date_month=" + $('#bill_date_month').val() + "&emp_name=" + $('#emp_name').val() + "&emp_phone=" + $('#emp_phone').val() + "&store_ou_name=" + $('#store_ou_name').val() + "&standard_code=" + $('#standard_code').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>ID</td><td>员工名字</td><td>员工ID</td><td>员工手机号</td><td>组织机构</td><td>油站名称</td><td>油站编码</td><td>日期</td><td>员工自提订单奖励数</td><td>员工配送订单奖励数</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.id + '</td><td>' + item.emp_name + '</td><td>' + item.emp_id + '</td><td>' + item.emp_phone
                                + '</td><td>' + item.store_ou_code + '</td><td>' + item.store_ou_name + '</td><td>' + item.standard_code
                                + '</td><td>' + item.bill_date_month + '</td><td>' + item.emp_self_lifting_rewards_num
                                + '</td><td>' + item.emp_delivery_rewards_num + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/APP_yg_ticheng_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
        

    });
</script>

