﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Antlr" version="3.4.1.9004" targetFramework="net452" />
  <package id="bootstrap" version="3.0.0" targetFramework="net452" />
  <package id="EntityFramework" version="6.1.3" targetFramework="net452" />
  <package id="EntityFramework.zh-Hans" version="6.1.3" targetFramework="net452" />
  <package id="FreeSql" version="3.2.807-preview20231215" targetFramework="net472" />
  <package id="FreeSql.Provider.MySql" version="3.2.807-preview20231215" targetFramework="net472" />
  <package id="Google.Protobuf" version="3.21.9" targetFramework="net472" />
  <package id="jQuery" version="1.10.2" targetFramework="net452" />
  <package id="jQuery.Validation" version="1.11.1" targetFramework="net452" />
  <package id="K4os.Compression.LZ4" version="1.3.5" targetFramework="net472" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.5" targetFramework="net472" />
  <package id="K4os.Hash.xxHash" version="1.0.8" targetFramework="net472" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Core.zh-Hans" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.EntityFramework" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.EntityFramework.zh-Hans" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Owin.zh-Hans" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Mvc" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Mvc.zh-Hans" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Razor.zh-Hans" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Web.Optimization" version="1.1.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.Web.Optimization.zh-Hans" version="1.1.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client.zh-Hans" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core.zh-Hans" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.HelpPage" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Owin.zh-Hans" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost.zh-Hans" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebPages.zh-Hans" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="1.0.0" targetFramework="net452" />
  <package id="Microsoft.jQuery.Unobtrusive.Validation" version="3.2.3" targetFramework="net452" />
  <package id="Microsoft.Net.Compilers" version="1.0.0" targetFramework="net452" developmentDependency="true" />
  <package id="Microsoft.Owin" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Host.SystemWeb.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Cookies" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Facebook" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Facebook.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Google" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Google.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.MicrosoftAccount.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Twitter" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Twitter.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Owin.zh-Hans" version="3.0.1" targetFramework="net452" />
  <package id="Microsoft.Web.Infrastructure" version="1.0.0.0" targetFramework="net452" />
  <package id="Modernizr" version="2.6.2" targetFramework="net452" />
  <package id="MySql.Data" version="8.0.33" targetFramework="net472" requireReinstallation="true" />
  <package id="Newtonsoft.Json" version="6.0.4" targetFramework="net452" />
  <package id="NPOI" version="2.5.1" targetFramework="net452" />
  <package id="Owin" version="1.0" targetFramework="net452" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net472" />
  <package id="Respond" version="1.2.0" targetFramework="net452" />
  <package id="SharpZipLib" version="1.2.0" targetFramework="net452" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Data.Common" version="4.3.0" targetFramework="net472" />
  <package id="System.Data.OracleClient" version="1.0.8" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="5.0.2" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="WebGrease" version="1.5.2" targetFramework="net452" />
</packages>