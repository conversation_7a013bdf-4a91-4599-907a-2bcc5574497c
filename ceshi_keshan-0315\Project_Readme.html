﻿<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="utf-8" />
    <title>你的 ASP.NET 应用程序</title>
    <style>
        body {
            background: #fff;
            color: #505050;
            font: 14px 'Segoe UI', tahoma, arial, helvetica, sans-serif;
            margin: 20px;
            padding: 0;
        }

        #header {
            background: #efefef;
            padding: 0;
        }

        h1 {
            font-size: 48px;
            font-weight: normal;
            margin: 0;
            padding: 0 30px;
            line-height: 150px;
        }

        p {
            font-size: 20px;
            color: #fff;
            background: #969696;
            padding: 0 30px;
            line-height: 50px;
        }

        #main {
            padding: 5px 30px;
        }

        .section {
            width: 21.7%;
            float: left;
            margin: 0 0 0 4%;
        }

            .section h2 {
                font-size: 13px;
                text-transform: uppercase;
                margin: 0;
                border-bottom: 1px solid silver;
                padding-bottom: 12px;
                margin-bottom: 8px;
            }

            .section.first {
                margin-left: 0;
            }

                .section.first h2 {
                    font-size: 24px;
                    text-transform: none;
                    margin-bottom: 25px;
                    border: none;
                }

                .section.first li {
                    border-top: 1px solid silver;
                    padding: 8px 0;
                }

            .section.last {
                margin-right: 0;
            }

        ul {
            list-style: none;
            padding: 0;
            margin: 0;
            line-height: 20px;
        }

        li {
            padding: 4px 0;
        }

        a {
            color: #267cb2;
            text-decoration: none;
        }

            a:hover {
                text-decoration: underline;
            }
    </style>
</head>
<body>

    <div id="header">
        <h1>你的 ASP.NET 应用程序</h1>
        <p>恭喜! 你已创建了一个项目</p>
    </div>

    <div id="main">
        <div class="section first">
            <h2>此应用程序包含:</h2>
            <ul>
                <li>用于记录你的 Web API 的<a href="http://go.microsoft.com/fwlink/?LinkID=615543">帮助页</a></li>
                <li>使用 <a href="http://go.microsoft.com/fwlink/?LinkID=615519">Bootstrap</a> 进行主题定位</li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320957">身份验证</a>，如果选择此项，则将显示如何注册和登录</li>
                <li>使用 <a href="http://go.microsoft.com/fwlink/?LinkID=320958">NuGet</a> 管理的 ASP.NET 功能</li>
            </ul>
        </div>

        <div class="section">
            <h2>自定义应用</h2>
            <ul>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320959">使用 ASP.NET Web API 的 HTTP 服务入门</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320960">更改站点的主题</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320961">使用 NuGet 添加更多库</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320962">配置身份验证</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=320963">从模型搭建 ASP.NET Web API 的基架</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615545">保护 Web API</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615544">在不同设备上访问你的 Web API</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615546">为测试和调试启用跟踪</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615530">使用 ASP.NET SignalR 添加实时 Web</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615531">使用 Scaffolding 添加组件</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615533">共享项目</a></li>
            </ul>
        </div>

        <div class="section">
            <h2>部署</h2>
            <ul>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615534">确保你的应用已为生产做好准备</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615535">Microsoft Azure</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615536">托管提供商</a></li>
            </ul>
        </div>

        <div class="section last">
            <h2>获取帮助</h2>
            <ul>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615537">获取帮助</a></li>
                <li><a href="http://go.microsoft.com/fwlink/?LinkID=615538">获取更多模板</a></li>
            </ul>
        </div>
    </div>
</body>
</html>
