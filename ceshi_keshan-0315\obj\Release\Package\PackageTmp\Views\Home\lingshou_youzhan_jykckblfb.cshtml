﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_s1" id="workday_s1" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_d1" id="workday_d1" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            
            $.getJSON("/api/values/lingshou_youzhan_jykckblfb?workday_s1=" + $('#workday_s1').val() + "&workday_d1=" + $('#workday_d1').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                      if (data.length > 0)
                      {
                          var jsonobj = $.parseJSON(data);//转化data的格式
                          var htmltable = "";
                          htmltable += '<tr><td>油站名称</td><td>持卡消费量</td><td>总销量</td><td>持卡比例</td><td>柴油IC卡</td><td>柴油总量</td><td>柴油持卡比例</td><td>汽油IC卡</td><td>汽油总量</td><td>汽油持卡比例</td><td>92号汽油IC卡</td><td>92号汽油总量</td><td>92号汽油持卡比例</td><td>95号汽油IC卡</td><td>95号汽油总量</td><td>95号汽油持卡比例</td><td>98号汽油IC卡</td><td>98号汽油总量</td><td>98号汽油持卡比例</tr>';
                          $.each(jsonobj, function (i, item) {
                              htmltable += '<tr><td>' + item.ShortName + '</td><td>' + item.ckxfl + '</td><td>' + item.zxl + '</td><td>' + item.ckbl + '%</td><td>' + item.cyick + '</td><td>' + item.cyzl + '</td><td>' + item.cyckbl + '%</td><td>' + item.qyick + '</td><td>' + item.qyzl + '</td><td>' + item.qyckbl + '%</td><td>' + item.qyick92 + '</td><td>' + item.qyzl92 + '</td><td>' + item.qyckbl92 + '%</td><td>' + item.qyick95 + '</td><td>' + item.qyzl95 + '</td><td>' + item.qyckbl95 + '%</td><td>' + item.qyick98 + '</td><td>' + item.qyzl98 + '</td><td>' + item.qyckbl98 + '%</tr>';
                          });  //循环each   json数组     <tr>行  <td>列
                          $('#shuju_1').html(htmltable);
                      } else
                      { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/lingshou_youzhan_jykckblfb_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
