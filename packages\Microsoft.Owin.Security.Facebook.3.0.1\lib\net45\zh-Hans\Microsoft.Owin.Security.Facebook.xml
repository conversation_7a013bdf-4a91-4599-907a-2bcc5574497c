﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Owin.Security.Facebook</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext">
      <summary>在质询导致重定向到 Facebook 中间件中的授权终结点时传递上下文</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions,Microsoft.Owin.Security.AuthenticationProperties,System.String)">
      <summary>创建新的上下文对象。</summary>
      <param name="context">OWIN 请求上下文</param>
      <param name="options">Facebook 中间件选项</param>
      <param name="properties">质询的身份验证属性</param>
      <param name="redirectUri">初始重定向 URI</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.Properties">
      <summary>获取质询的身份验证属性</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext.RedirectUri">
      <summary>获取用于重定向操作的 URI。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext">
      <summary>包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,Newtonsoft.Json.Linq.JObject,System.String,System.String)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext" /></summary>
      <param name="context">OWIN 环境</param>
      <param name="user">JSON 序列化的用户</param>
      <param name="accessToken">Facebook 访问令牌</param>
      <param name="expires">距过期的秒数</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.AccessToken">
      <summary>获取 Facebook 访问令牌</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Email">
      <summary>获取 Facebook 电子邮件</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.ExpiresIn">
      <summary>获取 Facebook 访问令牌过期时间</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Id">
      <summary>获取 Facebook 用户 ID</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Identity">
      <summary>获取用于表示用户的 <see cref="T:System.Security.Claims.ClaimsIdentity" /></summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Link">
      <summary>获取或设置指向 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext" /> 的链接。</summary>
      <returns>指向 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext" /> 的链接。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Name">
      <summary>获取用户的姓名</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.Properties">
      <summary>获取或设置常见身份验证属性的属性包</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.User">
      <summary>获取 JSON 序列化的用户</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext.UserName">
      <summary>获取 Facebook 用户名</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware">
      <summary>使用 Facebook 对用户进行身份验证的 OWIN 中间件</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware" /></summary>
      <param name="next">OWIN 管道中要调用的下一个中间件</param>
      <param name="app">OWIN 应用程序</param>
      <param name="options">中间件的配置选项</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware.CreateHandler">
      <summary>提供用于处理身份验证相关请求的 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler" /> 对象。</summary>
      <returns>配置了提供给构造函数的 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions" /> 的 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler" />。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions">
      <summary>
        <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware" /> 的配置选项</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.#ctor">
      <summary>初始化新的 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions" /></summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.AppId">
      <summary>获取或设置 Facebook 分配的 appId</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.AppSecret">
      <summary>获取或设置 Facebook 分配的应用程序机密</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelCertificateValidator">
      <summary>获取或设置固定证书验证程序，用于验证在属于 Facebook 的返回通道通信中使用的终结点。</summary>
      <returns>固定证书验证程序。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelHttpHandler">
      <summary>用于与 Facebook 通信的 HttpMessageHandler。除非值可以向下转换为 WebRequestHandler，否则不能在设置 BackchannelCertificateValidator 的同时设置此项。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.BackchannelTimeout">
      <summary>获取或设置与 Facebook 进行的返回通道通信的超时值（以毫秒为单位）。</summary>
      <returns>返回通道超时值（以毫秒为单位）。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.CallbackPath">
      <summary>应用程序的基路径内将返回用户代理的请求路径。此请求到达时，中间件将处理此请求。默认值为“/signin-facebook”。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Caption">
      <summary>获取或设置用户可以在登录用户界面上显示的文本。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Provider">
      <summary>获取或设置用于处理身份验证事件的 <see cref="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider" />。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.Scope">
      <summary>要请求的权限列表。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.SendAppSecretProof">
      <summary>获取或设置在调用 Facebook API 时是否应生成并发送 appsecret_proof。默认情况下已启用此项。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.SignInAsAuthenticationType">
      <summary>获取或设置将负责实际颁发用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的其他身份验证中间件的名称。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions.StateDataFormat">
      <summary>获取或设置用于保护由中间件处理的数据的类型。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider">
      <summary>默认 <see cref="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider" /> 实现。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider" /></summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext)">
      <summary>在质询导致重定向到 Facebook 中间件中的授权终结点时调用</summary>
      <param name="context">包含质询的重定向 URI 和 <see cref="T:Microsoft.Owin.Security.AuthenticationProperties" /></param>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext)">
      <summary>每当 Facebook 成功对用户进行身份验证时调用</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="context">包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnApplyRedirect">
      <summary>获取或设置调用 ApplyRedirect 方法时调用的委托。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnAuthenticated">
      <summary>获取或设置调用 Authenticated 方法时调用的函数。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.OnReturnEndpoint">
      <summary>获取或设置调用 ReturnEndpoint 方法时调用的函数。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext)">
      <summary>在 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 保存到本地 Cookie 中且浏览器重定向到最初请求的 URL 之前调用。</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext">
      <summary>向中间件提供程序提供上下文信息。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)">
      <param name="context">OWIN 环境</param>
      <param name="ticket">身份验证票证</param>
    </member>
    <member name="T:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider">
      <summary>指定回调方法，<see cref="T:Microsoft.Owin.Security.Facebook.FacebookAuthenticationMiddleware" /> 调用这些方法使开发人员可以控制身份验证过程。/&amp;amp;gt;</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Facebook.FacebookApplyRedirectContext)">
      <summary>在质询导致重定向到 Facebook 中间件中的授权终结点时调用</summary>
      <param name="context">包含质询的重定向 URI 和 <see cref="T:Microsoft.Owin.Security.AuthenticationProperties" /></param>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Facebook.FacebookAuthenticatedContext)">
      <summary>每当 Facebook 成功对用户进行身份验证时调用</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="context">包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Facebook.IFacebookAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Facebook.FacebookReturnEndpointContext)">
      <summary>在 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 保存到本地 Cookie 中且浏览器重定向到最初请求的 URL 之前调用。</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:Owin.FacebookAuthenticationExtensions">
      <summary>提供 Facebook 身份验证的扩展方法。</summary>
    </member>
    <member name="M:Owin.FacebookAuthenticationExtensions.UseFacebookAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Facebook.FacebookAuthenticationOptions)">
      <summary>生成 OWIN 应用程序的 Facebook 身份验证。</summary>
      <returns>生成身份验证的 <see cref="T:Owin.IAppBuilder" />。</returns>
      <param name="app">应用程序生成器。</param>
      <param name="options">Facebook 身份验证选项。</param>
    </member>
    <member name="M:Owin.FacebookAuthenticationExtensions.UseFacebookAuthentication(Owin.IAppBuilder,System.String,System.String)">
      <summary>生成 OWIN 应用程序的 Facebook 身份验证。</summary>
      <returns>生成身份验证的 <see cref="T:Owin.IAppBuilder" />。</returns>
      <param name="app">应用程序生成器。</param>
      <param name="appId">应用程序 ID。</param>
      <param name="appSecret">应用程序密码。</param>
    </member>
  </members>
</doc>