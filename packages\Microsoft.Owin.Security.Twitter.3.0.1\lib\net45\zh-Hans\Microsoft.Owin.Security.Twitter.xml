﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Owin.Security.Twitter</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider">
      <summary>指定回调方法，<see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware" /> 调用这些方法使开发人员可以控制身份验证过程。/&amp;amp;gt;</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext)">
      <summary>在质询导致重定向到 Twitter 中间件中的授权终结点时调用</summary>
      <param name="context">包含质询的重定向 URI 和 <see cref="T:Microsoft.Owin.Security.AuthenticationProperties" /></param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext)">
      <summary>当 Twitter 成功对用户进行身份验证时调用</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="context">包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext)">
      <summary>在 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 保存到本地 Cookie 中且浏览器重定向到最初请求的 URL 之前调用。</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext">
      <summary>在质询导致重定向到 Twitter 中间件中的授权终结点时传递上下文</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions,Microsoft.Owin.Security.AuthenticationProperties,System.String)">
      <summary>创建新的上下文对象。</summary>
      <param name="context">OWIN 请求上下文</param>
      <param name="options">Facebook 中间件选项</param>
      <param name="properties">质询的身份验证属性</param>
      <param name="redirectUri">初始重定向 URI</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.Properties">
      <summary>获取质询的身份验证属性</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext.RedirectUri">
      <summary>获取用于重定向操作的 URI。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext">
      <summary>包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.#ctor(Microsoft.Owin.IOwinContext,System.String,System.String,System.String,System.String)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext" /></summary>
      <param name="context">OWIN 环境</param>
      <param name="userId">Twitter 用户 ID</param>
      <param name="screenName">Twitter 屏幕名称</param>
      <param name="accessToken">Twitter 访问令牌</param>
      <param name="accessTokenSecret">Twitter 访问令牌机密</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.AccessToken">
      <summary>获取 Twitter 访问令牌</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.AccessTokenSecret">
      <summary>获取 Twitter 访问令牌机密</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.Identity">
      <summary>获取用于表示用户的 <see cref="T:System.Security.Claims.ClaimsIdentity" /></summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.Properties">
      <summary>获取或设置常见身份验证属性的属性包</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.ScreenName">
      <summary>获取 Twitter 屏幕名称</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext.UserId">
      <summary>获取 Twitter 用户 ID</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware">
      <summary>使用 Twitter 对用户进行身份验证的 OWIN 中间件</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware.#ctor(Microsoft.Owin.OwinMiddleware,Owin.IAppBuilder,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions)">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware" /></summary>
      <param name="next">OWIN 管道中要调用的下一个中间件</param>
      <param name="app">OWIN 应用程序</param>
      <param name="options">中间件的配置选项</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware.CreateHandler">
      <summary>提供用于处理身份验证相关请求的 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler" /> 对象。</summary>
      <returns>配置了提供给构造函数的 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions" /> 的 <see cref="T:Microsoft.Owin.Security.Infrastructure.AuthenticationHandler" />。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions">
      <summary>Twitter 身份验证中间件的选项。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions" /> 类的新实例。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelCertificateValidator">
      <summary>获取或设置要用于验证在属于 Twitter 的返回通道通信中使用的终结点的固定证书验证程序。</summary>
      <returns>固定证书验证程序。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelHttpHandler">
      <summary>用于与 Twitter 通信的 HttpMessageHandler。除非值可以向下转换为 WebRequestHandler，否则不能在设置 BackchannelCertificateValidator 的同时设置此项。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.BackchannelTimeout">
      <summary>获取或设置与 Twitter 进行的返回通道通信的超时值（以毫秒为单位）。</summary>
      <returns>返回通道超时值。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.CallbackPath">
      <summary>应用程序的基路径内将返回用户代理的请求路径。此请求到达时，中间件将处理此请求。默认值为“/signin-twitter”。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.Caption">
      <summary>获取或设置用户可以在登录用户界面上显示的文本。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.ConsumerKey">
      <summary>获取或设置用于与 Twitter 通信的使用者密钥。</summary>
      <returns>用于与 Twitter 通信的使用者密钥。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.ConsumerSecret">
      <summary>获取或设置用于对发送到 Twitter 的请求签名的使用者机密。</summary>
      <returns>用于对发送到 Twitter 的请求签名的使用者机密。</returns>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.Provider">
      <summary>获取或设置用于处理身份验证事件的 <see cref="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider" />。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.SignInAsAuthenticationType">
      <summary>获取或设置将负责实际颁发用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的其他身份验证中间件的名称。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions.StateDataFormat">
      <summary>获取或设置用于保护由中间件处理的数据的类型。</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider">
      <summary>默认 <see cref="T:Microsoft.Owin.Security.Twitter.ITwitterAuthenticationProvider" /> 实现。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.#ctor">
      <summary>初始化 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider" /></summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.ApplyRedirect(Microsoft.Owin.Security.Twitter.TwitterApplyRedirectContext)">
      <summary>在质询导致重定向到 Twitter 中间件中的授权终结点时调用</summary>
      <param name="context">包含质询的重定向 URI 和 <see cref="T:Microsoft.Owin.Security.AuthenticationProperties" /></param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.Authenticated(Microsoft.Owin.Security.Twitter.TwitterAuthenticatedContext)">
      <summary>当 Twitter 成功对用户进行身份验证时调用</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
      <param name="context">包含登录会话及用户 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 的相关信息。</param>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnApplyRedirect">
      <summary>获取或设置调用 ApplyRedirect 方法时调用的委托。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnAuthenticated">
      <summary>获取或设置调用 Authenticated 方法时调用的函数。</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.OnReturnEndpoint">
      <summary>获取或设置调用 ReturnEndpoint 方法时调用的函数。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterAuthenticationProvider.ReturnEndpoint(Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext)">
      <summary>在 <see cref="T:System.Security.Claims.ClaimsIdentity" /> 保存到本地 Cookie 中且浏览器重定向到最初请求的 URL 之前调用。</summary>
      <returns>表示已完成的操作的 <see cref="T:System.Threading.Tasks.Task" />。</returns>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext">
      <summary>向中间件提供程序提供上下文信息。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext.#ctor(Microsoft.Owin.IOwinContext,Microsoft.Owin.Security.AuthenticationTicket)">
      <summary>初始化新的 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterReturnEndpointContext" />。</summary>
      <param name="context">OWIN 环境</param>
      <param name="ticket">身份验证票证</param>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.Messages.AccessToken">
      <summary>Twitter 访问令牌</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.AccessToken.#ctor"></member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.AccessToken.ScreenName">
      <summary>获取或设置 Twitter 屏幕名称</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.AccessToken.UserId">
      <summary>获取或设置 Twitter 用户 ID</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.Messages.RequestToken">
      <summary>Twitter 请求令牌</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestToken.#ctor"></member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.CallbackConfirmed"></member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.Properties">
      <summary>获取或设置常见身份验证属性的属性包</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.Token">
      <summary>获取或设置 Twitter 令牌</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.RequestToken.TokenSecret">
      <summary>获取或设置 Twitter 令牌机密</summary>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer">
      <summary>序列化和反序列化 Twitter 请求令牌和访问令牌，以使它们可供其他应用程序组件使用。</summary>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.#ctor"></member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Deserialize(System.Byte[])">
      <summary>反序列化请求令牌</summary>
      <returns>Twitter 请求令牌</returns>
      <param name="data">包含已序列化令牌的字节数组</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Read(System.IO.BinaryReader)">
      <summary>从一系列字节中读取 Twitter 请求令牌。由 <see cref="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Deserialize(System.Byte[])" /> 方法使用。</summary>
      <returns>令牌</returns>
      <param name="reader">读取令牌字节时要使用的读取器</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Serialize(Microsoft.Owin.Security.Twitter.Messages.RequestToken)">
      <summary>序列化请求令牌</summary>
      <returns>包含已序列化令牌的字节数组</returns>
      <param name="model">要序列化的令牌</param>
    </member>
    <member name="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Write(System.IO.BinaryWriter,Microsoft.Owin.Security.Twitter.Messages.RequestToken)">
      <summary>将 Twitter 请求令牌写入为一系列字节。由 <see cref="M:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer.Serialize(Microsoft.Owin.Security.Twitter.Messages.RequestToken)" /> 方法使用。</summary>
      <param name="writer">写入令牌时要使用的编写器</param>
      <param name="token">要写入的令牌</param>
    </member>
    <member name="T:Microsoft.Owin.Security.Twitter.Messages.Serializers">
      <summary>提供对请求令牌序列化程序的访问</summary>
    </member>
    <member name="P:Microsoft.Owin.Security.Twitter.Messages.Serializers.RequestToken">
      <summary>获取或设置静态可用的序列化程序对象。默认情况下，此属性的值将为 <see cref="T:Microsoft.Owin.Security.Twitter.Messages.RequestTokenSerializer" />。</summary>
    </member>
    <member name="T:Owin.TwitterAuthenticationExtensions">
      <summary>使用 <see cref="T:Microsoft.Owin.Security.Twitter.TwitterAuthenticationMiddleware" /> 的扩展方法</summary>
    </member>
    <member name="M:Owin.TwitterAuthenticationExtensions.UseTwitterAuthentication(Owin.IAppBuilder,Microsoft.Owin.Security.Twitter.TwitterAuthenticationOptions)">
      <summary>对使用 Twitter 的用户进行身份验证</summary>
      <returns>已更新的 <see cref="T:Owin.IAppBuilder" /></returns>
      <param name="app">传递给配置方法的 <see cref="T:Owin.IAppBuilder" /></param>
      <param name="options">中间件配置选项</param>
    </member>
    <member name="M:Owin.TwitterAuthenticationExtensions.UseTwitterAuthentication(Owin.IAppBuilder,System.String,System.String)">
      <summary>对使用 Twitter 的用户进行身份验证</summary>
      <returns>已更新的 <see cref="T:Owin.IAppBuilder" /></returns>
      <param name="app">传递给配置方法的 <see cref="T:Owin.IAppBuilder" /></param>
      <param name="consumerKey">Twitter 颁发的使用者密钥</param>
      <param name="consumerSecret">Twitter 颁发的使用者机密</param>
    </member>
  </members>
</doc>