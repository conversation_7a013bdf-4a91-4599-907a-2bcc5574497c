@{
    ViewBag.Title = ViewBag.Title ?? "路由测试页面";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container">
    <div class="page-header">
        <h1 class="text-success">✅ 路由测试成功！</h1>
        <p class="text-muted">如果您看到这个页面，说明 MVC 路由配置正常工作。</p>
    </div>

    <div class="panel panel-success">
        <div class="panel-heading">
            <h3 class="panel-title">🎯 路由信息</h3>
        </div>
        <div class="panel-body">
            <table class="table table-bordered">
                <tr>
                    <td><strong>控制器</strong></td>
                    <td><code>@ViewBag.ControllerName</code></td>
                </tr>
                <tr>
                    <td><strong>动作</strong></td>
                    <td><code>@ViewBag.ActionName</code></td>
                </tr>
                <tr>
                    <td><strong>当前时间</strong></td>
                    <td><code>@ViewBag.CurrentTime</code></td>
                </tr>
                <tr>
                    <td><strong>访问路径</strong></td>
                    <td><code>/Test/Index</code></td>
                </tr>
            </table>
        </div>
    </div>

    <div class="panel panel-info">
        <div class="panel-heading">
            <h3 class="panel-title">🔗 测试链接</h3>
        </div>
        <div class="panel-body">
            <div class="row">
                <div class="col-md-6">
                    <h4>基础测试</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="/Test/Index">测试首页</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/Test/TestJson">JSON 测试</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/Test/TestText">文本测试</a>
                        </li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h4>公车管理测试</h4>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <a href="/Bus/Index">公车列表页面</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/BusApplication/Create">创建申请页面</a>
                        </li>
                        <li class="list-group-item">
                            <a href="/home/<USER>">返回原系统</a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="panel panel-warning">
        <div class="panel-heading">
            <h3 class="panel-title">📊 AJAX 测试</h3>
        </div>
        <div class="panel-body">
            <button id="testAjax" class="btn btn-primary">测试 AJAX 调用</button>
            <div id="ajaxResult" style="margin-top: 10px; display: none;">
                <pre id="ajaxContent"></pre>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            $('#testAjax').click(function() {
                $.get('/Test/TestJson', function(data) {
                    $('#ajaxContent').text(JSON.stringify(data, null, 2));
                    $('#ajaxResult').show();
                }).fail(function() {
                    $('#ajaxContent').text('AJAX 调用失败');
                    $('#ajaxResult').show();
                });
            });
        });
    </script>
}
