
@{
    ViewBag.Title = "创建公车申请";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewBag.Title - 公车管理系统</title>

    <!-- Bootstrap CSS -->
    <link href="~/Content/bootstrap.min.css" rel="stylesheet" />
    <link href="~/Content/Site.css" rel="stylesheet" />

    <!-- 自定义样式 -->
    <style>
        body {
            background-color: #f5f5f5;
            font-family: "Microsoft YaHei", Arial, sans-serif;
        }
        .main-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 0;
            margin-bottom: 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .main-header h1 {
            margin: 0;
            font-weight: 300;
        }
        .breadcrumb-custom {
            background: white;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .panel {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: none;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .control-label {
            font-weight: 600;
        }
        .help-block {
            color: #666;
            font-size: 12px;
        }
        .text-danger {
            color: #d9534f;
        }
        .footer-custom {
            margin-top: 50px;
            padding: 20px 0;
            background: #fff;
            border-top: 1px solid #e7e7e7;
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>


    <!-- 页面头部 -->
    <div class="main-header">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h1><i class="glyphicon glyphicon-plus"></i> @ViewBag.Title</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">请填写完整的用车申请信息，提交后将进入审批流程</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="container">
        <div class="breadcrumb-custom">
            <ol class="breadcrumb" style="margin: 0; background: none;">
                <li><a href="/home/<USER>"><i class="glyphicon glyphicon-home"></i> 首页</a></li>
                <li><a href="/Bus/Index">公车管理</a></li>
                <li><a href="/BusApplication/Index">申请管理</a></li>
                <li class="active">创建申请</li>
            </ol>
        </div>
    </div>

    <div class="container">
        <div class="row">
            <div class="col-md-10 col-md-offset-1">

            <!-- 消息提示 -->
            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    <strong><i class="glyphicon glyphicon-exclamation-sign"></i> 错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success alert-dismissible">
                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                    <strong><i class="glyphicon glyphicon-ok-sign"></i> 成功：</strong> @TempData["SuccessMessage"]
                </div>
            }

            <!-- 申请表单 -->
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="glyphicon glyphicon-edit"></i> 申请信息
                    </h4>
                </div>
                <div class="panel-body">
                    <form method="post" action="/BusApplication/Create" class="form-horizontal" id="createForm">

                        <!-- 申请人信息 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 申请人姓名
                            </label>
                            <div class="col-sm-9">
                                <input type="text" name="ApplicantName" class="form-control"
                                       placeholder="请输入申请人姓名" required maxlength="50" />
                                <span class="help-block">请输入真实姓名</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 申请部门
                            </label>
                            <div class="col-sm-9">
                                <input type="text" name="Department" class="form-control"
                                       placeholder="请输入申请部门" required maxlength="100" />
                                <span class="help-block">请输入完整的部门名称</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 联系电话
                            </label>
                            <div class="col-sm-9">
                                <input type="tel" name="Phone" class="form-control"
                                       placeholder="请输入联系电话" required pattern="[0-9]{11}" />
                                <span class="help-block">请输入11位手机号码</span>
                            </div>
                        </div>

                        <!-- 车辆选择 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 选择车辆
                            </label>
                            <div class="col-sm-9">
                                <select name="BusId" class="form-control" required id="busSelect">
                                    <option value="">请选择车辆</option>
                                    <option value="1" data-seats="5">粤A12345 (丰田凯美瑞, 5座)</option>
                                    <option value="2" data-seats="5">粤A67890 (本田雅阁, 5座)</option>
                                    <option value="3" data-seats="5">粤A11111 (大众帕萨特, 5座)</option>
                                </select>
                                <span class="help-block">只显示当前可用的车辆</span>
                            </div>
                        </div>

                        <!-- 用车详情 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 用车目的
                            </label>
                            <div class="col-sm-9">
                                <textarea name="Purpose" class="form-control" rows="3"
                                          placeholder="请详细说明用车目的和行程安排" required maxlength="500"></textarea>
                                <span class="help-block">请详细描述用车原因和具体安排</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 目的地
                            </label>
                            <div class="col-sm-9">
                                <input type="text" name="Destination" class="form-control"
                                       placeholder="请输入具体目的地地址" required maxlength="200" />
                                <span class="help-block">请输入详细的目的地地址</span>
                            </div>
                        </div>

                        <!-- 时间安排 -->
                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 开始时间
                            </label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="StartTime" class="form-control"
                                       required id="startTime" />
                                <span class="help-block">请选择用车开始时间</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 结束时间
                            </label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="EndTime" class="form-control"
                                       required id="endTime" />
                                <span class="help-block">请选择用车结束时间</span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">
                                <span class="text-danger">*</span> 预计人数
                            </label>
                            <div class="col-sm-9">
                                <input type="number" name="EstimatedPeople" class="form-control"
                                       min="1" max="50" value="1" required id="peopleCount" />
                                <span class="help-block" id="seatWarning">请根据车辆座位数合理填写</span>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="glyphicon glyphicon-ok"></i> 提交申请
                                </button>
                                <a href="/BusApplication/Index" class="btn btn-default btn-lg">
                                    <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                                </a>
                                <a href="/Bus/Index" class="btn btn-info btn-lg">
                                    <i class="glyphicon glyphicon-list"></i> 公车列表
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 使用说明 -->
            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <i class="glyphicon glyphicon-info-sign"></i> 使用说明
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5><i class="glyphicon glyphicon-check text-success"></i> 申请须知</h5>
                            <ul class="list-unstyled">
                                <li><i class="glyphicon glyphicon-ok text-success"></i> 请如实填写申请信息，确保联系方式准确</li>
                                <li><i class="glyphicon glyphicon-ok text-success"></i> 用车时间不能早于当前时间</li>
                                <li><i class="glyphicon glyphicon-ok text-success"></i> 结束时间必须晚于开始时间</li>
                                <li><i class="glyphicon glyphicon-ok text-success"></i> 预计人数不能超过车辆座位数</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5><i class="glyphicon glyphicon-time text-info"></i> 审批流程</h5>
                            <ul class="list-unstyled">
                                <li><i class="glyphicon glyphicon-arrow-right text-info"></i> 申请提交后将进入审批流程</li>
                                <li><i class="glyphicon glyphicon-arrow-right text-info"></i> 审批结果将通过系统通知</li>
                                <li><i class="glyphicon glyphicon-arrow-right text-info"></i> 可在申请列表中查看审批状态</li>
                                <li><i class="glyphicon glyphicon-arrow-right text-info"></i> 如有疑问请联系管理员</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function () {
        // 设置最小时间为当前时间
        var now = new Date();
        var minDateTime = now.toISOString().slice(0, 16);
        $('#startTime, #endTime').attr('min', minDateTime);

        // 车辆选择变化时检查座位数
        $('#busSelect').change(function () {
            var selectedOption = $(this).find('option:selected');
            var seats = selectedOption.data('seats');
            var peopleInput = $('#peopleCount');
            var warning = $('#seatWarning');

            if (seats) {
                peopleInput.attr('max', seats);
                warning.text('该车辆最多可乘坐 ' + seats + ' 人');

                // 如果当前人数超过座位数，自动调整
                if (parseInt(peopleInput.val()) > seats) {
                    peopleInput.val(seats);
                }
            } else {
                peopleInput.attr('max', 50);
                warning.text('请根据车辆座位数合理填写');
            }
        });

        // 开始时间变化时，自动设置结束时间最小值
        $('#startTime').change(function () {
            var startTime = $(this).val();
            if (startTime) {
                $('#endTime').attr('min', startTime);

                // 如果结束时间早于开始时间，清空结束时间
                var endTime = $('#endTime').val();
                if (endTime && endTime <= startTime) {
                    $('#endTime').val('');
                }
            }
        });

        // 表单提交验证
        $('#createForm').submit(function (e) {
            var startTime = new Date($('#startTime').val());
            var endTime = new Date($('#endTime').val());
            var now = new Date();

            // 验证开始时间不能早于当前时间
            if (startTime <= now) {
                alert('开始时间不能早于当前时间！');
                e.preventDefault();
                return false;
            }

            // 验证结束时间必须晚于开始时间
            if (endTime <= startTime) {
                alert('结束时间必须晚于开始时间！');
                e.preventDefault();
                return false;
            }

            // 验证人数不超过座位数
            var selectedOption = $('#busSelect').find('option:selected');
            var seats = selectedOption.data('seats');
            var people = parseInt($('#peopleCount').val());

            if (seats && people > seats) {
                alert('预计人数不能超过车辆座位数（' + seats + '座）！');
                e.preventDefault();
                return false;
            }

            // 确认提交
            return confirm('确认提交申请吗？提交后将进入审批流程。');
        });

        // 自动隐藏提示消息
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
    });
</script>

    <!-- 页面底部 -->
    <div class="footer-custom">
        <div class="container">
            <p>&copy; @DateTime.Now.Year 公车管理系统 - 让出行更便捷</p>
        </div>
    </div>
</body>
</html>
