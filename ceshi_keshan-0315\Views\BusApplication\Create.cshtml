

@{
    ViewBag.Title = "创建公车申请";
    Layout = "~/Views/Shared/_Layout.cshtml";
    Response.ContentType = "text/html; charset=utf-8"; // 显式指定编码
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">填写用车申请信息，提交后等待审批</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">申请信息</h4>
                </div>
                <div class="panel-body">
                    <form method="post" action="/BusApplication/Create" class="form-horizontal">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请人姓名</label>
                            <div class="col-sm-9">
                                <input type="text" name="ApplicantName" class="form-control" placeholder="请输入申请人姓名" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">申请部门</label>
                            <div class="col-sm-9">
                                <input type="text" name="Department" class="form-control" placeholder="请输入申请部门" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">联系电话</label>
                            <div class="col-sm-9">
                                <input type="text" name="Phone" class="form-control" placeholder="请输入联系电话" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">选择车辆</label>
                            <div class="col-sm-9">
                                <select name="BusId" class="form-control">
                                    <option value="">请选择车辆</option>
                                    <option value="1">粤A12345 (丰田凯美瑞, 5座)</option>
                                    <option value="2">粤A67890 (本田雅阁, 5座)</option>
                                    <option value="3">粤A11111 (大众帕萨特, 5座)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">用车目的</label>
                            <div class="col-sm-9">
                                <textarea name="Purpose" class="form-control" rows="3" placeholder="请详细说明用车目的"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">目的地</label>
                            <div class="col-sm-9">
                                <input type="text" name="Destination" class="form-control" placeholder="请输入目的地" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">开始时间</label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="StartTime" class="form-control" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">结束时间</label>
                            <div class="col-sm-9">
                                <input type="datetime-local" name="EndTime" class="form-control" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">预计人数</label>
                            <div class="col-sm-9">
                                <input type="number" name="EstimatedPeople" class="form-control" min="1" max="50" value="1" />
                                <p class="help-block">请根据车辆座位数合理填写</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-primary">
                                    <i class="glyphicon glyphicon-ok"></i> 提交申请
                                </button>
                                <a href="/BusApplication/Index" class="btn btn-default">
                                    <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                                </a>
                                <a href="/Bus/Index" class="btn btn-info">
                                    <i class="glyphicon glyphicon-list"></i> 公车列表
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4 class="panel-title">使用说明</h4>
                </div>
                <div class="panel-body">
                    <ol>
                        <li>请如实填写申请信息，确保联系方式准确</li>
                        <li>用车时间不能早于当前时间</li>
                        <li>结束时间必须晚于开始时间</li>
                        <li>申请提交后将进入审批流程</li>
                        <li>审批结果将通过系统通知</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>