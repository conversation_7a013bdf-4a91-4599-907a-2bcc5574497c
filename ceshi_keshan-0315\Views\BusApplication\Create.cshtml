

@model ceshi_keshan_0315.Models.BusApplication
@{
    ViewBag.Title = "创建公车申请";
    Layout = "~/Views/Shared/_Layout.cshtml";
    Response.ContentType = "text/html; charset=utf-8"; // 显式指定编码
}

<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 col-md-offset-2">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">填写用车申请信息，提交后等待审批</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">申请信息</h4>
                </div>
                <div class="panel-body">
                    @using (Html.BeginForm("Create", "BusApplication", FormMethod.Post, new { @class = "form-horizontal" }))
                    {
                        @Html.AntiForgeryToken()

                        <div class="form-group">
                            @Html.LabelFor(m => m.ApplicantName, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.ApplicantName, new { @class = "form-control", placeholder = "请输入申请人姓名" })
                                @Html.ValidationMessageFor(m => m.ApplicantName, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.Department, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.Department, new { @class = "form-control", placeholder = "请输入申请部门" })
                                @Html.ValidationMessageFor(m => m.Department, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.Phone, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.Phone, new { @class = "form-control", placeholder = "请输入联系电话" })
                                @Html.ValidationMessageFor(m => m.Phone, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">选择车辆 <span class="text-danger">*</span></label>
                            <div class="col-sm-9">
                                @Html.DropDownListFor(m => m.BusId, ViewBag.AvailableBuses as IEnumerable<SelectListItem>, "请选择车辆", new { @class = "form-control" })
                                @Html.ValidationMessageFor(m => m.BusId, "", new { @class = "text-danger" })
                                <p class="help-block">只显示当前可用的车辆</p>
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.Purpose, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextAreaFor(m => m.Purpose, new { @class = "form-control", rows = 3, placeholder = "请详细说明用车目的" })
                                @Html.ValidationMessageFor(m => m.Purpose, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.Destination, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.Destination, new { @class = "form-control", placeholder = "请输入目的地" })
                                @Html.ValidationMessageFor(m => m.Destination, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.StartTime, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.StartTime, new { @class = "form-control", type = "datetime-local" })
                                @Html.ValidationMessageFor(m => m.StartTime, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.EndTime, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.EndTime, new { @class = "form-control", type = "datetime-local" })
                                @Html.ValidationMessageFor(m => m.EndTime, "", new { @class = "text-danger" })
                            </div>
                        </div>

                        <div class="form-group">
                            @Html.LabelFor(m => m.EstimatedPeople, new { @class = "col-sm-3 control-label" })
                            <div class="col-sm-9">
                                @Html.TextBoxFor(m => m.EstimatedPeople, new { @class = "form-control", type = "number", min = "1", max = "50", value = "1" })
                                @Html.ValidationMessageFor(m => m.EstimatedPeople, "", new { @class = "text-danger" })
                                <p class="help-block">请根据车辆座位数合理填写</p>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-primary">
                                    <i class="glyphicon glyphicon-ok"></i> 提交申请
                                </button>
                                <a href="@Url.Action("Index", "BusApplication")" class="btn btn-default">
                                    <i class="glyphicon glyphicon-arrow-left"></i> 返回列表
                                </a>
                                <a href="@Url.Action("Index", "Bus")" class="btn btn-info">
                                    <i class="glyphicon glyphicon-list"></i> 公车列表
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>

            <div class="panel panel-info">
                <div class="panel-heading">
                    <h4 class="panel-title">使用说明</h4>
                </div>
                <div class="panel-body">
                    <ol>
                        <li>请如实填写申请信息，确保联系方式准确</li>
                        <li>用车时间不能早于当前时间</li>
                        <li>结束时间必须晚于开始时间</li>
                        <li>申请提交后将进入审批流程</li>
                        <li>审批结果将通过系统通知</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @Html.Partial("_ValidationScriptsPartial")
}