@model List<ceshi_keshan_0315.Areas.BusManagement.ViewModels.BusVM>
@{
    ViewBag.Title = "公车信息管理";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- 新增模块：公车信息管理页面 -->
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="page-header">
                <h2>@ViewBag.Title</h2>
                <p class="text-muted">管理公车基础信息，查看车辆状态和使用情况</p>
            </div>

            @if (!string.IsNullOrEmpty(ViewBag.ErrorMessage))
            {
                <div class="alert alert-danger">
                    <strong>错误：</strong> @ViewBag.ErrorMessage
                </div>
            }

            <!-- 筛选条件 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">筛选条件</h4>
                </div>
                <div class="panel-body">
                    @using (Html.BeginForm("Index", "Bus", FormMethod.Get, new { @class = "form-inline" }))
                    {
                        <div class="form-group">
                            <label for="plateNumber">车牌号：</label>
                            <input type="text" class="form-control" name="plateNumber" id="plateNumber" 
                                   value="@ViewBag.PlateNumber" placeholder="请输入车牌号" />
                        </div>

                        <div class="form-group">
                            <label for="status">状态：</label>
                            <select class="form-control" name="status" id="status">
                                <option value="">全部状态</option>
                                <option value="1" @(ViewBag.Status?.ToString() == "1" ? "selected" : "")>可用</option>
                                <option value="2" @(ViewBag.Status?.ToString() == "2" ? "selected" : "")>使用中</option>
                                <option value="3" @(ViewBag.Status?.ToString() == "3" ? "selected" : "")>维修中</option>
                                <option value="4" @(ViewBag.Status?.ToString() == "4" ? "selected" : "")>已报废</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="purchaseDate">购买日期：</label>
                            <input type="text" class="form-control Wdate" name="purchaseDate" id="purchaseDate" 
                                   value="@ViewBag.PurchaseDate" 
                                   onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" 
                                   placeholder="选择日期" readonly />
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="glyphicon glyphicon-search"></i> 查询
                        </button>

                        <a href="@Url.Action("Index", "Bus")" class="btn btn-default">
                            <i class="glyphicon glyphicon-refresh"></i> 重置
                        </a>

                        <a href="@Url.Action("ExportToExcel", "Bus")" class="btn btn-success">
                            <i class="glyphicon glyphicon-download-alt"></i> 导出Excel
                        </a>
                    }
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        公车列表 
                        <span class="badge">共 @ViewBag.TotalCount 辆</span>
                    </h4>
                </div>
                <div class="panel-body">
                    @if (Model != null && Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>车牌号</th>
                                        <th>车型</th>
                                        <th>品牌</th>
                                        <th>状态</th>
                                        <th>座位数</th>
                                        <th>购买日期</th>
                                        <th>使用年限</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var bus in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@bus.PlateNumber</strong>
                                            </td>
                                            <td>@bus.Model</td>
                                            <td>@bus.Brand</td>
                                            <td>
                                                <span class="label @GetStatusClass(bus.Status)">
                                                    @bus.StatusText
                                                </span>
                                            </td>
                                            <td>@bus.SeatCount 座</td>
                                            <td>@bus.PurchaseDate.ToString("yyyy-MM-dd")</td>
                                            <td>@bus.UsageYears 年</td>
                                            <td>
                                                <a href="@Url.Action("Details", "Bus", new { id = bus.Id })" 
                                                   class="btn btn-sm btn-info" title="查看详情">
                                                    <i class="glyphicon glyphicon-eye-open"></i>
                                                </a>
                                                @if (bus.IsAvailable)
                                                {
                                                    <a href="@Url.Action("Create", "Application", new { busId = bus.Id })" 
                                                       class="btn btn-sm btn-success" title="申请用车">
                                                        <i class="glyphicon glyphicon-plus"></i>
                                                    </a>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        @if (ViewBag.TotalPages > 1)
                        {
                            <nav>
                                <ul class="pagination">
                                    @if (ViewBag.HasPreviousPage)
                                    {
                                        <li>
                                            <a href="@Url.Action("Index", new { pageIndex = ViewBag.PageIndex - 1, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status, purchaseDate = ViewBag.PurchaseDate })">
                                                &laquo; 上一页
                                            </a>
                                        </li>
                                    }

                                    @for (int i = Math.Max(1, ViewBag.PageIndex - 2); i <= Math.Min(ViewBag.TotalPages, ViewBag.PageIndex + 2); i++)
                                    {
                                        <li class="@(i == ViewBag.PageIndex ? "active" : "")">
                                            <a href="@Url.Action("Index", new { pageIndex = i, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status, purchaseDate = ViewBag.PurchaseDate })">
                                                @i
                                            </a>
                                        </li>
                                    }

                                    @if (ViewBag.HasNextPage)
                                    {
                                        <li>
                                            <a href="@Url.Action("Index", new { pageIndex = ViewBag.PageIndex + 1, plateNumber = ViewBag.PlateNumber, status = ViewBag.Status, purchaseDate = ViewBag.PurchaseDate })">
                                                下一页 &raquo;
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </nav>
                        }
                    }
                    else
                    {
                        <div class="alert alert-info text-center">
                            <h4>暂无数据</h4>
                            <p>没有找到符合条件的公车信息</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusClass(ceshi_keshan_0315.Areas.BusManagement.Models.BusStatus status)
    {
        return status switch
        {
            ceshi_keshan_0315.Areas.BusManagement.Models.BusStatus.Available => "label-success",
            ceshi_keshan_0315.Areas.BusManagement.Models.BusStatus.InUse => "label-warning",
            ceshi_keshan_0315.Areas.BusManagement.Models.BusStatus.Maintenance => "label-danger",
            ceshi_keshan_0315.Areas.BusManagement.Models.BusStatus.Retired => "label-default",
            _ => "label-default"
        };
    }
}

@section Scripts {
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
}
