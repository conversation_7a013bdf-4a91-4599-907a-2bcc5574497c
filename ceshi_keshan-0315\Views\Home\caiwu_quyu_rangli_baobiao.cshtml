﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                日期：<input type="text" name="workday_s" id="workday_s" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_d" id="workday_d" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp;
            </div>
            <br>
            <div>
                金额：<input type="text" name="jinemin" id="jinemin" size="15" value="1" />---
                <input type="text" name="jinemax" id="jinemax" size="15" value="1000000" />

            </div>
            <br>
            <div>
                油品名称：<input type="text" name="GasName" id="GasName" size="15" />&nbsp;
                油站位置：<input type="text" name="nodename" id="nodename" size="15" />&nbsp;
                油站名称：<input type="text" name="ouname" id="ouname" size="15" />&nbsp;
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {
            var test = "/api/values/caiwu_quyu_rangli_baobiao?workday_s=" + $('#workday_s').val() + "&workday_d=" + $('#workday_d').val()
                 + "&jinemin=" + $('#jinemin').val() + "&jinemax=" + $('#jinemax').val() + "&GasName=" + $('#GasName').val()
                  + "&nodename=" + $('#nodename').val() + "&ouname=" + $('#ouname').val();
            $.getJSON("/api/values/caiwu_quyu_rangli_baobiao?workday_s=" + $('#workday_s').val() + "&workday_d=" + $('#workday_d').val()
                 + "&jinemin=" + $('#jinemin').val() + "&jinemax=" + $('#jinemax').val() + "&GasName=" + $('#GasName').val()
                  + "&nodename=" + $('#nodename').val() + "&ouname=" + $('#ouname').val() , function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                      if (data.length > 0)
                      {
                          var jsonobj = $.parseJSON(data);//转化data的格式
                          var htmltable = "";
                          htmltable += '<tr><td>区域名称</td><td>市县编码</td><td>片区名称</td><td>时间</td><td>油站名称</td><td>油站编码</td><td>油品编码</td><td>支付方式</td><td>优惠升数</td><td>单价</td><td>quanpr</td><td>销售金额</td><td>差价</td><td>优惠金额</td><td>位置</td><td>挂牌价</tr>';
                          $.each(jsonobj, function (i, item) {
                              htmltable += '<tr><td>' + item.BigAreaName + '</td><td>' + item.BranchName + '</td><td>' + item.AreaName + '</td><td>' + item.SFDAT + '</td><td>' + item.OUName + '</td><td>' + item.STNID + '</td><td>' + item.MATNR + '</td><td>' + item.POSNR + '</td><td>' +
                                                        item.QUANTITY + '</td> <td> ' + item.PRICE + '</td ><td>' + item.quanpr + '</td><td>' + item.AMOUNT + '</td><td>' + item.chajia + '</td><td>' + item.jine + '</td><td>' + item.NodeName + '</td><td>' + item.guapai+ '</tr>';
                          });  //循环each   json数组     <tr>行  <td>列
                          $('#shuju_1').html(htmltable);
                      } else
                      { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/cwrl_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>

