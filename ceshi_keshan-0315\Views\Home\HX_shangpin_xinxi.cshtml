﻿
@{
    ViewBag.Title = "HX_shangpin_xinxi";
}


<h2>海信商品属性查询</h2>

  
<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                商品编码：<input type="text" name="plucode" id="plucode" size="15">
                &nbsp;
                商品名称：<input type="text" name="pluname" id="pluname" size="15">
                &nbsp;
                便利店编码：<input type="text" name="orgcode" id="orgcode" value="46" size="15">
                &nbsp;
                商品条码：<input type="text" name="barcode" id="barcode" size="15">
            </div>
            &nbsp;
            <div>
                <input id="Button1" type="button" value="查询" />
                &nbsp;<input id="btn" type="submit" value="导出" />
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/HX_shangpin_xinxi?plucode=" + $('#plucode').val() + "&pluname=" + $('#pluname').val() + "&orgcode=" + $('#orgcode').val() + "&barcode=" + $('#barcode').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>商品编码</td><td>便利店编码</td><td>商品名称</td><td>商品条码</td><td>标准编码</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.PLUCODE + '</td><td>' + item.ORGCODE + '</td><td>' + item.PLUNAME + '</td><td>' + item.BARCODE + '</td><td>' + item.UDP1  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列   //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });
        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/HX_shangpin_xinxi_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });
    });
</script>
