﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace ceshi_keshan_0315
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            // 新增模块：公车管理路由（已整合到主项目）
            routes.MapRoute(
                name: "BusManagement",
                url: "Bus/{action}/{id}",
                defaults: new { controller = "Bus", action = "Index", id = UrlParameter.Optional },
                namespaces: new[] { "ceshi_keshan_0315.Controllers" }
            );

            routes.MapRoute(
                name: "BusApplication",
                url: "BusApplication/{action}/{id}",
                defaults: new { controller = "BusApplication", action = "Index", id = UrlParameter.Optional },
                namespaces: new[] { "ceshi_keshan_0315.Controllers" }
            );



            // 默认路由
            routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional },
                namespaces: new[] { "ceshi_keshan_0315.Controllers" }
            );
        }
    }
}