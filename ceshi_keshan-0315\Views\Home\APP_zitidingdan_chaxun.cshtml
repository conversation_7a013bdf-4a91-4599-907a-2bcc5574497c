﻿@{
    //Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src="~/Scripts/jquery-1.10.2.min.js"></script>
    <script type="text/javascript" src="~/Scripts/My97DatePicker/WdatePicker.js"></script>
    <title></title>
</head>
<body>
    <br />
    <div>
        <form method="post" id="form1">
            <div>
                记账日期：<input type="text" name="workday_sappddcx" id="workday_sappddcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                --<input type="text" name="workday_dappddcx" id="workday_dappddcx" size="15" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd',readOnly:true})" />
                &nbsp; 油站名称：<input type="text" name="orgname" id="orgname" size="15" />
                &nbsp; 便利店编码：<input type="text" name="orgcode" id="orgcode" size="15" />
                &nbsp; APP订单号前三位：<input type="text" name="djzbillno" id="djzbillno" size="15" />

            </div>
            &nbsp;
            <div>
                <div class="left">
                    <input id="Button1" type="button" value="查询" />
                    &nbsp;<input id="btn" type="submit" value="导出" />
                    &nbsp;&nbsp;<input id="btn_hz" type="button" value="订单汇总日查询" />
                    &nbsp;&nbsp;<input id="btn_yhz" type="button" value="订单汇总月查询" />
                    &nbsp;&nbsp;<input id="btn_zfmx" type="button" value="支付明细查询" />
                    &nbsp;&nbsp;<input id="btn_zfhz" type="button" value="支付汇总查询" />
                </div>
                <div class="pull-right">
                    海信订单号：<input type="text" name="billno" id="billno" size="15" />(明细查询必填)                    
                    &nbsp;<input id="btn_mx" type="button" value="APP自提订单明细查询" />
                </div>
                
                <div class="left">
                    <div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
                    {===***备注：05-网上结算方式，内部订单号为海信订单号billno；订单号前三位为“462”“202”***===}
                </div>
                <div>
                    &nbsp;&nbsp;
                </div>
                
            </div>
        </form>


        <div id="div1"></div>
        <br />
        <table id="shuju_1" border=1></table>
    </div>
</body>
</html>

<script>
    $(document).ready(function () {
        $('#Button1').click(function () {

            $.getJSON("/api/values/APP_zitidingdan_chaxun?workday_sappddcx=" + $('#workday_sappddcx').val() + "&workday_dappddcx=" + $('#workday_dappddcx').val() + "&orgname=" + $('#orgname').val() + "&djzbillno=" + $('#djzbillno').val() + "&orgcode=" + $('#orgcode').val(), function (data) {   //$('#workday').html()  取页面信息值
                //$('#div1').html(data);
                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站编码</td><td>油站名称</td><td>记账状态</td><td>生产类型</td><td>APP订单号</td><td>结算方式</td><td>发货方式</td><td>记账日期</td><td>报表日期</td><td>海信订单号</td><td>批发数量</td><td>应收金额</td><td>实收金额</td><td>销项税额</td><td>qktotal</td><td>hcost</td><td>profit</td><td>订单类型</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGCODE + '</td><td>' + item.ORGNAME + '</td><td>' + item.JZTYPE + '</td><td>' + item.GENTYPE + '</td><td>' + item.DJZBILLNO + '</td><td>' + item.JSCODE + '</td><td>' + item.FHMODE + '</td><td>' + item.JZDATE + '</td><td>' +
                            item.RPTDATE + '</td><td> ' + item.BILLNO + '</td ><td>' + item.PFCOUNT + '</td><td>' + item.PFTOTAL + '</td><td>' + item.SSTOTAL + '</td><td>' + item.XTAXTOTAL + '</td><td>' + item.QKTOTAL + '</td><td>' + item.HCOST + '</td><td>' + item.PROFIT + '</td><td>' + item.DDLX+ '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

                // $.getJSON("/api/values/chaxun_daozhang_yujin", function (data) {
                // $('#div1').html(data);    //getJSON 是有数据返回的使用

                //新增新的方法需要在controllers  中的  homecontrollers中增加新的页面方法
            })
        });

        $('#btn').click(function () {
            $("#form1").attr("action", "/Home/APP_zitidingdan_chaxun_ExportData");//根据促发表单，把action 替换成后面的路劲
            $('#btn').submit(); // 执行按钮  触法表单，然后表单上面的代码进行替换跳转路劲
        });

        $('#btn_hz').click(function () {

            $.getJSON("/api/values/APP_zitidingdanhuizong_chaxun?workday_sappddcx=" + $('#workday_sappddcx').val() + "&workday_dappddcx=" + $('#workday_dappddcx').val() + "&orgname=" + $('#orgname').val() + "&djzbillno=" + $('#djzbillno').val() + "&orgcode=" + $('#orgcode').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站编码</td><td>油站名称</td><td>报表日期</td><td>订单笔数</td><td>批发数量</td><td>应收金额</td><td>实收金额</td><td>销项税额</td><td>qktotal</td><td>hcost</td><td>profit</td><td>订单类型</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGCODE + '</td><td>' + item.ORGNAME + '</td><td>' + item.RPTDATE + '</td><td>' +
                            item.DJZBILLNO + '</td ><td>' + item.PFCOUNT + '</td><td>' + item.PFTOTAL + '</td><td>' + item.SSTOTAL + '</td><td>' + item.XTAXTOTAL + '</td><td>' + item.QKTOTAL + '</td><td>' + item.HCOST + '</td><td>' + item.PROFIT + '</td><td>' + item.DDLX + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        $('#btn_yhz').click(function () {

            $.getJSON("/api/values/APP_zitidingdanhuizong_yuechaxun?workday_sappddcx=" + $('#workday_sappddcx').val() + "&workday_dappddcx=" + $('#workday_dappddcx').val() + "&orgname=" + $('#orgname').val() + "&djzbillno=" + $('#djzbillno').val() + "&orgcode=" + $('#orgcode').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站编码</td><td>油站名称</td><td>订单笔数</td><td>批发数量</td><td>应收金额</td><td>实收金额</td><td>销项税额</td><td>qktotal</td><td>hcost</td><td>profit</td><td>订单类型</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGCODE + '</td><td>' + item.ORGNAME + '</td><td>' +
                            item.DJZBILLNO + '</td ><td>' + item.PFCOUNT + '</td><td>' + item.PFTOTAL + '</td><td>' + item.SSTOTAL + '</td><td>' + item.XTAXTOTAL + '</td><td>' + item.QKTOTAL + '</td><td>' + item.HCOST + '</td><td>' + item.PROFIT + '</td><td>' + item.DDLX + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        $('#btn_mx').click(function () {

            $.getJSON("/api/values/APP_zitidingdan_mingxi_chaxun?billno=" + $('#billno').val() , function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>海信订单号</td><td>商品序号</td><td>海信内码</td><td>海信编码</td><td>商品名称</td><td>商品编码</td><td>规格</td><td>单位</td><td>箱装单位</td><td>商品数量</td><td>单位数量</td><td>平均单价</td><td>系统单价</td><td>订单金额</td><td>税率</td><td>销项税额</td><td>HCOST</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.BILLNO + '</td><td>' + item.SERIALNO + '</td><td>' +
                            item.PLUID + '</td ><td>' + item.PLUCODE + '</td><td>' + item.PLUNAME + '</td><td>' + item.BARCODE + '</td><td>' + item.SPEC + '</td><td>' + item.UNIT + '</td><td>' + item.PACKUNIT +
                            '</td ><td>' + item.PACKQTY + '</td><td>' + item.PFCOUNT + '</td><td>' + item.HJPRICE + '</td><td>' + item.PRICE + '</td><td>' + item.YSTOTAL + '</td><td>' + item.XTAXRATE + '</td><td>' + item.XTAXTOTAL + '</td><td>' + item.HCOST
                            + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        $('#btn_zfmx').click(function () {

            $.getJSON("/api/values/APP_zitidingdan_zhifumingxi_chaxun?workday_sappddcx=" + $('#workday_sappddcx').val() + "&workday_dappddcx=" + $('#workday_dappddcx').val() + "&orgname=" + $('#orgname').val() + "&orgcode=" + $('#orgcode').val() +"&billno=" + $('#billno').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站编码</td><td>油站名称</td><td>报表日期</td><td>支付名称</td><td>支付编码</td><td>状态</td><td>金额</td><td>ISZFSUCCESS</td><td>海信订单号</td><td>备注</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGCODE + '</td><td>' + item.ORGNAME + '</td><td>' + item.RPTDATE + '</td><td>' +
                            item.ZFNAME + '</td ><td>' + item.ZFCODE + '</td><td>' + item.TRANTYPE + '</td><td>' + item.JINE + '</td><td>' + item.ISZFSUCCESS + '</td><td>' + item.BILLNO + '</td><td>' + item.REMARK  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

        $('#btn_zfhz').click(function () {

            $.getJSON("/api/values/APP_zitidingdan_zhifuhuizong_chaxun?workday_sappddcx=" + $('#workday_sappddcx').val() + "&workday_dappddcx=" + $('#workday_dappddcx').val() + "&orgname=" + $('#orgname').val() + "&orgcode=" + $('#orgcode').val(), function (data) {   //$('#workday').html()  取页面信息值

                if (data.length > 0) {
                    var jsonobj = $.parseJSON(data);//转化data的格式
                    var htmltable = "";
                    htmltable += '<tr><td>油站编码</td><td>油站名称</td><td>报表日期</td><td>支付名称</td><td>支付编码</td><td>交易笔数</td><td>状态</td><td>金额</td><td>ISZFSUCCESS</tr>';
                    $.each(jsonobj.tmp, function (i, item) {
                        htmltable += '<tr><td>' + item.ORGCODE + '</td><td>' + item.ORGNAME + '</td><td>' + item.RPTDATE + '</td><td>' +
                            item.ZFNAME + '</td ><td>' + item.ZFCODE + '</td><td>' + item.NUM + '</td><td>' + item.TRANTYPE + '</td><td>' + item.JINE + '</td><td>' + item.ISZFSUCCESS  + '</tr>';
                    });  //循环each   json数组     <tr>行  <td>列     
                    //WEBapi接口需要用到jsonobj.tmp 接口封装的时候是tmp 源代码是dt35.TableName = "tmp"; (项目名称：hx_xiaoshoumingxi_biao) 和字段名item.ORGCODE需大写，小写报错
                    $('#shuju_1').html(htmltable);
                } else { alert("查询为空"); }

            })
        });

    });
</script>
